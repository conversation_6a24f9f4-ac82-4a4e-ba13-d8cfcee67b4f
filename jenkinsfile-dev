pipeline {
    agent any
    tools {
        // Install the Maven version configured as "M3" and add it to the path.
        maven "Default"
    }
    environment{
        def remote = ""
        HOST_DIR = "/home/<USER>/"
        GIT_URL = "http://***********/tbm/backend.git" //GIT仓库地址
        GIT_BRANCH = "*/dev_ctp" //GIT仓库分支
        TEST_SERVER_IP = "************" //测试服务器IP
        TEST_SERVER_USER = "root" //测试服务器用户名
        TEST_SERVER_PASSWD = "123456" //测试服务器密码
        PROJECT_NAME = "tbm" //项目名称
        PROJECT_DIR = "target/" //项目工程目录
        BACKEND_DIR = "backend" //后端部署目录
        FRONTEND_DIR = "frontend" //前端部署目录
        JAVA_JAR_NAME = "tbm-2.0.0.RELEASE.jar" //后台服务JAR报名
        JAVA_JAR_DOCKER_PORT = "11901" //后台服务端口（容器）
        JAVA_JAR_SERVER_PORT = "11901" //后台服务端口（宿主）
    }

    stages {
        stage('更新') {
            steps {
                checkout([$class: 'GitSCM',
                branches: [[name: '*/dev_ctp']],
                extensions: [],
                userRemoteConfigs: [[credentialsId: '8b37f8b2-7514-49d9-91ed-5fac0e1cb5ce',
                url: 'http://***********/tbm/backend.git']]])
            }

            post {
                success {
                    echo "成功"
                    dingtalk (
                        robot: 'b9ea7de8-6086-408b-9c2d-4b2f539e982a',
                        type: 'MARKDOWN',
                        at:[],
                        atAll: false,
                        title: "发送成功",
                        text:[
                            "### [耶]完成\n\n",
                            "--项目: [${env.JOB_NAME}](${env.JOB_URL}) ",
                            "- 状态：<font color=#00CD00 >部署开始</font>",
                            "- 任务：[${currentBuild.displayName}](${env.BUILD_URL})",
                            "- 持续时间：${currentBuild.durationString}".split("and counting")[0],
                            "- 执行人：**${currentBuild.buildCauses.shortDescription}**",

                        ]
                    )
                }
                failure {
                    dingtalk (
                        robot: 'b9ea7de8-6086-408b-9c2d-4b2f539e982a',
                        type: 'MARKDOWN',
                        at:[],
                        atAll: false,
                        title: "发送不成功",
                        text:["### [耶]部署失败\n\n**项目**: ****\n\n**记录**:\n\n"]
                    )
                }
            }

        }
        stage('编译') {
            steps {
                // Run Maven on a Unix agent.
                sh "mvn -Dmaven.test.failure.ignore=true clean package"
            }
        }

        stage('上传') {
            steps {
                sshPublisher(publishers: [sshPublisherDesc(configName: "${TEST_SERVER_IP}",
                transfers: [sshTransfer(cleanRemote: false, excludes: '',
                execCommand: """cd ${HOST_DIR}
                echo "#! /bin/bash" > mkprodir.sh
                echo "if [ ! -d ${PROJECT_NAME}/${BACKEND_DIR} ]; then" >> mkprodir.sh
                echo "        echo "创建文件夹"" >> mkprodir.sh
                echo "        mkdir -p ${PROJECT_NAME}/${BACKEND_DIR}" >> mkprodir.sh
                echo "else" >> mkprodir.sh
                echo "        echo "文件夹已存在"" >> mkprodir.sh
                echo "fi" >> mkprodir.sh
                chmod +x mkprodir.sh
                ./mkprodir.sh
                cd ${PROJECT_NAME}/${BACKEND_DIR}
                rm -rf *.jar
                mv ../../${JAVA_JAR_NAME} ./
                echo "FROM primetoninc/jdk:1.8" > Dockerfile
                echo "WORKDIR /app" >> Dockerfile
                echo "COPY ${JAVA_JAR_NAME} app.jar" >> Dockerfile
                echo "EXPOSE ${JAVA_JAR_DOCKER_PORT}" >> Dockerfile
                echo "ENTRYPOINT [\\"java\\",\\"-Xmx2048m\\",\\"-Djava.security.egd=file:/dev/./urandom\\",\\"-jar\\",\\"app.jar\\"]" >> Dockerfile
                echo "docker stop ${PROJECT_NAME}-${BACKEND_DIR} || return 0" > DockerShell.sh
                echo "docker rm ${PROJECT_NAME}-${BACKEND_DIR} || return 0" >> DockerShell.sh
                echo "docker rmi ${PROJECT_NAME}-${BACKEND_DIR}:v1 || return 0" >> DockerShell.sh
                chmod +x DockerShell.sh
                """,
                execTimeout: 120000, flatten: false,
                makeEmptyDirs: false, noDefaultExcludes: false,
                patternSeparator: '[, ]+', remoteDirectory: '',
                remoteDirectorySDF: false, removePrefix: "${PROJECT_DIR}",
                sourceFiles: "${PROJECT_DIR}${JAVA_JAR_NAME}")],
                usePromotionTimestamp: false, useWorkspaceInPromotion: false, verbose: false)])
            }
        }
        stage('构建') {
            steps {
                script {
                    remote = [:]
                    remote.name = 'server-1'
                    remote.host = "${TEST_SERVER_IP}"
                    remote.user = "${TEST_SERVER_USER}"
                    remote.port = 22
                    remote.password = "${TEST_SERVER_PASSWD}"
                    remote.allowAnyHosts = true
                    try {
                        sshCommand remote: remote, command: "cd ${HOST_DIR}${PROJECT_NAME}/${BACKEND_DIR} && docker logs ${PROJECT_NAME}-${BACKEND_DIR} >> ${currentBuild.number}.log && ./DockerShell.sh"
                    }
                    catch (exc) {
                        echo'catch error'
                    }
                    finally{
                        sshCommand remote: remote, command: "cd ${HOST_DIR}${PROJECT_NAME}/${BACKEND_DIR} &&  docker build -t ${PROJECT_NAME}-${BACKEND_DIR}:v1 . && docker run -d --name ${PROJECT_NAME}-${BACKEND_DIR} -e TZ='Asia/Shanghai' --net fawkesDocker -p ${JAVA_JAR_SERVER_PORT}:${JAVA_JAR_DOCKER_PORT} ${PROJECT_NAME}-${BACKEND_DIR}:v1"
                    }

                }
            }
        }
    }

    post {
        success {
            echo "成功"
            dingtalk (
                robot: 'b9ea7de8-6086-408b-9c2d-4b2f539e982a',
                type: 'MARKDOWN',
                at:[],
                atAll: false,
                title: "发送成功",
                text:[
                    "### [耶]完成\n\n",
                    "--项目: [${env.JOB_NAME}](${env.JOB_URL}) ",
                    "- 状态：<font color=#00CD00 >部署完成</font>",
                    "- 任务：[${currentBuild.displayName}](${env.BUILD_URL})",
                    "- 持续时间：${currentBuild.durationString}".split("and counting")[0],
                    "- 执行人：**${currentBuild.buildCauses.shortDescription}**",

                ]
            )
        }
        failure {
            dingtalk (
                robot: 'b9ea7de8-6086-408b-9c2d-4b2f539e982a',
                type: 'MARKDOWN',
                at:[],
                atAll: false,
                title: "发送不成功",
                text:[
                    "### [对不起]\n\n",
                    "--项目: [${env.JOB_NAME}](${env.JOB_URL}) ",
                    "- 状态：<font color=#00CD00 >部署失败</font>",
                    "- 任务：[${currentBuild.displayName}](${env.BUILD_URL})",
                    "- 持续时间：${currentBuild.durationString}".split("and counting")[0],
                    "- 执行人：**${currentBuild.buildCauses.shortDescription}**",

                ]
            )
        }
    }
}
