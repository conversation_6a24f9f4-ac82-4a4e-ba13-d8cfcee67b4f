package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 状态检测页面有害气体VO
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel("状态检测页面有害气体VO")
public class StateHarmfulGasDeviceVO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String name;

    /**
     * 甲烷
     */
    @ApiModelProperty("甲烷")
    private String ch4Level;

    /**
     * 二氧化碳
     */
    @ApiModelProperty("二氧化碳")
    private String h2sLevel;

    /**
     * 氧气
     */
    @ApiModelProperty("氧气")
    private String o2Level;

    /**
     * 一氧化碳
     */
    @ApiModelProperty("一氧化碳")
    private String coLevel;

    /**
     * 警报
     */
    @ApiModelProperty("警报")
    private Boolean alarm;

}