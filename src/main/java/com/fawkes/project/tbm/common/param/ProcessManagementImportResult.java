package com.fawkes.project.tbm.common.param;

import com.fawkes.project.tbm.common.tools.verify.domain.ImportResult;
import com.fawkes.project.tbm.common.tools.verify.domain.RowVerifyError;
import com.fawkes.project.tbm.common.vo.ProcessManagementAddImportVO;
import com.fawkes.project.tbm.common.vo.ProcessManagementVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.Assert;

import java.util.List;

/**
 * 安全教育成员导入结果
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("工序管理导入结果")
public class ProcessManagementImportResult
        extends ImportResult<ProcessManagementAddImportVO, ProcessManagementVO>
{

    public ProcessManagementImportResult() {
        super();
    }

    /**
     * 构造函数
     * @param verifyErrorList 验证错误列表
     */
    public ProcessManagementImportResult(List<RowVerifyError<ProcessManagementAddImportVO>> verifyErrorList) {
        super();
        Assert.notNull(verifyErrorList, "verifyErrorList must not be null");
        this.setErrorList(verifyErrorList);
    }
}
