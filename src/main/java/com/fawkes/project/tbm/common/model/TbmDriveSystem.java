package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * TBM驱动系统参数(此表为原始表，数据按月分表)
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tbm_drive_system")
@ApiModel("TBM驱动系统参数(此表为原始表，数据按月分表)")
public class TbmDriveSystem implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;
    /**
     * 设备编码
     */
    private String code;

    /**
     * 电机总功率/Double/kw
     */
    private BigDecimal power;

    /**
     * 振动频率/Double/hz
     */
    private BigDecimal frequency;

    /**
     * 电机电流/Double#1
     */
    private BigDecimal electricCurrentOne;

    /**
     * 电机电流/Double#2
     */
    private BigDecimal electricCurrentTwo;

    /**
     * 电机电流/Double#3
     */
    private BigDecimal electricCurrentThree;

    /**
     * 电机电流/Double#4
     */
    private BigDecimal electricCurrentFour;

    /**
     * 电机电流/Double#5
     */
    private BigDecimal electricCurrentFive;

    /**
     * 电机电流/Double#6
     */
    private BigDecimal electricCurrentSix;

    /**
     * 电机电流/Double#7
     */
    private BigDecimal electricCurrentSeven;

    /**
     * 电机电流/Double#8
     */
    private BigDecimal electricCurrentEight;

    /**
     * 电机电流/Double#9
     */
    private BigDecimal electricCurrentNine;

    /**
     * 电机电流/Double#10
     */
    private BigDecimal electricCurrentTen;

    /**
     * 电机电流/Double#11
     */
    private BigDecimal electricCurrentEleven;

    /**
     * 电机电流/Double#12
     */
    private BigDecimal electricCurrentTwelve;

    /**
     * 电机电流/Double#13
     */
    private BigDecimal electricCurrentThirteen;

    /**
     * 电机电流/Double#14
     */
    private BigDecimal electricCurrentFourteen;

    /**
     * 环数
     */
    @ApiModelProperty("环数")
    private Integer ringNum;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Timestamp updateTime;
    /** 电机温度/Double#1 */
    private BigDecimal temperatureOne;
    /** 电机温度/Double#2 */
    private BigDecimal temperatureTwo;
    /** 电机温度/Double#3 */
    private BigDecimal temperatureThree;
    /** 电机温度/Double#4 */
    private BigDecimal temperatureFour;
    /** 电机温度/Double#5 */
    private BigDecimal temperatureFive;
    /** 电机温度/Double#6 */
    private BigDecimal temperatureSix;
    /** 电机温度/Double#7 */
    private BigDecimal temperatureSeven;
    /** 电机温度/Double#8 */
    private BigDecimal temperatureEight;
    /** 电机温度/Double#9 */
    private BigDecimal temperatureNine;
    /** 电机温度/Double#10 */
    private BigDecimal temperatureTen;
    /** 电机温度/Double#11 */
    private BigDecimal temperatureEleven;
    /** 电机温度/Double#12 */
    private BigDecimal temperatureTwelve;
    /** 电机温度/Double#13 */
    private BigDecimal temperatureThirteen;
    /** 电机温度/Double#14 */
    private BigDecimal temperatureFourteen;

}
