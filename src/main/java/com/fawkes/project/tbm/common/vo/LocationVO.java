package com.fawkes.project.tbm.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 标段位置点位
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@ApiModel("标段位置点位VO")
public class LocationVO implements Serializable {

    /**
     * 经度
     */
    @ApiModelProperty("经度")
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    private String latitude;

    /**
     * wgs84经度
     */
    @ApiModelProperty("wgs84经度")
    private String longitudeWgs;

    /**
     * wgs84纬度
     */
    @ApiModelProperty("wgs84纬度")
    private String latitudeWgs;

}