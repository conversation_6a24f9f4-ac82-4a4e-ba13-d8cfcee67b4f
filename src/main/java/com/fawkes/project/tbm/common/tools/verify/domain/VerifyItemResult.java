package com.fawkes.project.tbm.common.tools.verify.domain;

import com.fawkes.project.tbm.common.utils.ListUtil;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 验证项目结果
 * <AUTHOR>
 */
public class VerifyItemResult {
	/** 错误消息 */
	private final List<String> errorMsgList;

	public VerifyItemResult() {
		this.errorMsgList = new ArrayList<>();
	}

	/**
	 * 获取错误列表
	 * @return 错误信息列表
	 */
	public List<String> getErrorMsgList() {
		return Collections.unmodifiableList(this.errorMsgList);
	}

	/**
	 * 添加错误信息
	 * @param errorMsg 错误信息
	 */
	public void addErrorMsg(String errorMsg) {
		Assert.hasText(errorMsg, "错误信息不能为空");
		this.errorMsgList.add(errorMsg);
	}

	/**
	 * 是否错误
	 * @return true: 存在错误
	 */
	public boolean isError() {
		return ListUtil.any(this.errorMsgList);
	}

	/**
	 * 获取错误信息
	 * @return 错误信息
	 */
	public String getErrorMessage() {
		return  String.join(",",this.errorMsgList);
	}
}
