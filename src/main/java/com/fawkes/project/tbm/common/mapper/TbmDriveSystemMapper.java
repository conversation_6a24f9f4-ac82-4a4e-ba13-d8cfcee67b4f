package com.fawkes.project.tbm.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fawkes.project.tbm.common.model.TbmDriveSystem;
import com.fawkes.project.tbm.common.vo.TbmDriveSystemXYVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * TBM驱动系统参数(此表为原始表，数据按月分表) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface TbmDriveSystemMapper extends BaseMapper<TbmDriveSystem> {

    /**
     * 获取最后十个环号对应的TbmDriveSystem对象
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     * @param motorTemperature 电机温度
     * @param electricCurrent 电机电流
     */
    List<TbmDriveSystemXYVO> getRingNumTbmDriveSystem(@Param("deviceCode") String deviceCode,
                                                      @Param("ringNumList") List<Integer> ringNumList,
                                                      @Param("motorTemperature") String motorTemperature,
                                                      @Param("electricCurrent") String electricCurrent,
                                                      @Param("tableName") String tableName);

    /**
     * 是否存在表
     *
     * @param tableName
     * @return
     */
    int existTable(@Param("tableName") String tableName);

    /**
     * 删除表
     *
     * @param tableName
     * @return
     */
    int dropTable(@Param("tableName") String tableName);

    /**
     * 创建表
     *
     * @param tableName
     * @return
     */
    int createTable(@Param("tableName") String tableName);
}
