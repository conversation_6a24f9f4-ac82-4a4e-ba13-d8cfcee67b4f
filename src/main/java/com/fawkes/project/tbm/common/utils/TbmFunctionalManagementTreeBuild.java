package com.fawkes.project.tbm.common.utils;

import com.fawkes.project.tbm.common.vo.TbmFunctionalManagementVO;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/7 16:03
 * @description
 */
public class TbmFunctionalManagementTreeBuild {

    public List<TbmFunctionalManagementVO> nodeList;

    /**
     * 构造方法
     *
     * @param nodeList 将数据集合赋值给nodeList，即所有数据作为所有节点。
     */
    public TbmFunctionalManagementTreeBuild(List<TbmFunctionalManagementVO> nodeList) {
        this.nodeList = nodeList;
    }

    /**
     * 获取需构建的所有根节点  这里默认父节点值为"0"作为根节点
     *
     * @return 所有根节点List集合
     */
    public List<TbmFunctionalManagementVO> getRootNode() {
        //  rootNodeList 保存所有根节点（所有根节点的数据）
        // TbmFunctionalManagementVO：查询出的每一条数据（节点）
        List<TbmFunctionalManagementVO> rootNodeList = nodeList.stream().filter(i -> i.getParentId() == 0).collect(Collectors.toList());
        return rootNodeList;
    }

    /**
     * 根据所有根节点进行构建树形结构
     *
     * @return 构建整棵树
     */
    public List<TbmFunctionalManagementVO> buildTree() {
        // TbmFunctionalManagementVOs：保存一个顶级节点所构建出来的完整树形
        // getRootNode()：获取所有的根节点

        List<TbmFunctionalManagementVO> tbmFunctionalManagementVOS = getRootNode().stream()
                .map(this::buildChildTree)
                .collect(Collectors.toList());
        return tbmFunctionalManagementVOS;
    }

    /**
     * 递归-----构建单个子树形结构
     *
     * @param pNode 根节点
     * @return 整棵树
     */
    public TbmFunctionalManagementVO buildChildTree(TbmFunctionalManagementVO pNode) {
        List<TbmFunctionalManagementVO> childTree = new ArrayList<TbmFunctionalManagementVO>();
        // nodeList：所有节点集合（所有数据）

        nodeList.stream()
                .filter(i -> i.getParentId().equals(pNode.getId()))
                .forEach(i -> childTree.add(buildChildTree(i)));
        // 处理数据结束，即节点下没有任何节点，树形构建结束，设置树结果
        pNode.setChildren(childTree);
        return pNode;
    }
}
