package com.fawkes.project.tbm.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 综合指标VO
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@ApiModel("综合指标VO")
public class ComprehensiveIndexVO implements Serializable {

    /**
     * 安全运行
     */
    @ApiModelProperty("安全运行")
    private Integer runingDay;

    /**
     * 累计里程
     */
    @ApiModelProperty("累计里程")
    private String mileage;

    /**
     * 设备总数
     */
    @ApiModelProperty("设备总数")
    private Integer deviceNum;

    /**
     * 今日推进环数
     */
    @ApiModelProperty("今日推进环数")
    private String todayRingNum;


}