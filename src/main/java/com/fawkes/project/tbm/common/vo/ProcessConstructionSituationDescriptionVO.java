package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@ApiModel("施工情况描述")
@Data
public class ProcessConstructionSituationDescriptionVO {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("出渣量")
    private String residueDischargeVolume;

    @ApiModelProperty("泡沫剂使用")
    private String useOfFoamingAgent;

    @ApiModelProperty("油脂使用")
    private String greaseUsage;

    @ApiModelProperty("拼装点位")
    private BigDecimal assemblyPoint;
}