package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 风险源预警表
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("risk_warning")
@ApiModel("风险源预警表")
public class RiskWarning extends BaseModel {

    private static final long serialVersionUID = 1L;

    /**
     * TBM设备code
     */
    @ApiModelProperty("TBM设备code")
    private String deviceCode;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 风险名称
     */
    @ApiModelProperty("风险名称")
    private String riskName;

    /**
     * 风险起始点距离
     */
    @ApiModelProperty("风险起始点距离")
    private BigDecimal riskStart;

    /**
     * 风险终点距离
     */
    @ApiModelProperty("风险终点距离")
    private BigDecimal riskEnd;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 附件
     */
    @ApiModelProperty("附件")
    private String attachment;

}