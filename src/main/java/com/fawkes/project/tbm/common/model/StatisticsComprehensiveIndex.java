package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 综合指标统计表
 * <AUTHOR>
 * @date 2025-04-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("statistics_comprehensive_index")
@ApiModel("综合指标统计表")
public class StatisticsComprehensiveIndex {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 里程/Double/m
     */
    @ApiModelProperty("里程/Double/m")
    private BigDecimal mileage;

    /**
     * 今日里程/Double/m
     */
    @ApiModelProperty("今日里程/Double/m")
    private BigDecimal todayMileage;

        /**
         * 环数
         */
    @ApiModelProperty("环数")
    private Integer ringNum;

        /**
         * 今日推进环数
         */
    @ApiModelProperty("今日推进环数")
    private Integer todayRingNum;

    /**
     * 运行状态： 掘进中|已停机
     */
    @ApiModelProperty("运行状态： 掘进中|已停机")
    private String runingState;

    /**
     * 统计时间
     */
    @ApiModelProperty("统计时间")
    private Date statisticsDate;

}