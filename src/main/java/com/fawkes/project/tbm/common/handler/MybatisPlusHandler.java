package com.fawkes.project.tbm.common.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.fawkes.core.base.model.CurrentUser;
import com.fawkes.core.utils.http.HttpHeaderTool;
import org.apache.ibatis.reflection.MetaObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;

/**
 * mybatis-plus Handler类
 */
public class MybatisPlusHandler implements MetaObjectHandler {

    private static final Logger log = LoggerFactory.getLogger(MybatisPlusHandler.class);

    @Override
    public void insertFill(MetaObject metaObject) {
        String username = HttpHeaderTool.getHeaderValue(HttpHeaderTool.HeaderEnum.AUTH_USERNAME);
        CurrentUser currentUser = HttpHeaderTool.getCurrentUser();
        String userFullname = currentUser.getUserFullname();
        //创建时间
        if(metaObject.getValue("createDate") == null){
            this.setFieldValByName("createDate", new Timestamp(System.currentTimeMillis()), metaObject); // 起始版本 3.3.0(推荐使用)
        }
        //创建人姓名
        this.setFieldValByName("createByFullname", userFullname, metaObject);
        //创建人
        this.setFieldValByName("createBy", username, metaObject); // 当前用户id

        //更新时间
        this.setFieldValByName("updateDate", new Timestamp(System.currentTimeMillis()), metaObject);
        //更新人
        this.setFieldValByName("updateBy", username, metaObject);
        //更新人姓名
        this.setFieldValByName("updateByFullname", userFullname, metaObject);
        //删除标记
        this.setFieldValByName("deleteFlag", 0, metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        String username = HttpHeaderTool.getHeaderValue(HttpHeaderTool.HeaderEnum.AUTH_USERNAME);
        CurrentUser currentUser = HttpHeaderTool.getCurrentUser();
        String userFullname = currentUser.getUserFullname();
        //更新时间
        this.setFieldValByName("updateDate", new Timestamp(System.currentTimeMillis()), metaObject);
        //更新人
        this.setFieldValByName("updateBy", username, metaObject);
        //更新人姓名
        this.setFieldValByName("updateByFullname", userFullname, metaObject);
    }
}
