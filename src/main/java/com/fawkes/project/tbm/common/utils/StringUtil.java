package com.fawkes.project.tbm.common.utils;

import com.fawkes.project.tbm.common.model.TbmDriveSystem;
import com.fawkes.project.tbm.common.model.TbmPropulsionSystem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtil {

    /**
     * 下划线命名转驼峰命名
     * @param underscore
     * @return
     */
    public static String underscoreToCamelCase(String underscore){
        String[] ss = underscore.split("_");
        if(ss.length ==1){
            return underscore;
        }
        StringBuffer sb = new StringBuffer();
        sb.append(ss[0]);
        for (int i = 1; i < ss.length; i++) {
            sb.append(upperFirstCase(ss[i]));
        }
        return sb.toString();
    }

    /**
     * 首字母 转大写
     * @param str
     * @return
     */
    public static String upperFirstCase(String str) {
        char[] chars = str.toCharArray();
        chars[0] -= 32;
        return String.valueOf(chars);
    }

    /**
     * 根据指定字段获取内容
     * @param underscore
     * @return
     */
    public static BigDecimal getCvlinderPressure(String underscore,TbmPropulsionSystem lastData){
        String s = underscoreToCamelCase(underscore);
        switch(s){
            case "cylinderPressureOne":
                return lastData.getCylinderPressureOne();
            case "cylinderPressureTwo":
                return lastData.getCylinderPressureTwo();
            case "cylinderPressureThree":
                return lastData.getCylinderPressureThree();
            case "cylinderPressureFour":
                return lastData.getCylinderPressureFour();
            case "cylinderPressureFive":
                return lastData.getCylinderPressureFive();
            case "cylinderPressureSix":
                return lastData.getCylinderPressureSix();
            default:
        }
        return null;
    }

    /**
     * 根据传入的电机编号返回对应字段的值
     *
     * @param tbmDriveSystem
     * @param electricCurrentCode
     * @return
     */
    public static BigDecimal getElectricCurrent(TbmDriveSystem tbmDriveSystem, String electricCurrentCode) {
        BigDecimal result = BigDecimal.ZERO;
        String s = underscoreToCamelCase(electricCurrentCode);
        switch (s) {
            case "electricCurrentOne":
                result = tbmDriveSystem.getElectricCurrentOne();
                break;
            case "electricCurrentTwo":
                result = tbmDriveSystem.getElectricCurrentTwo();
                break;
            case "electricCurrentThree":
                result = tbmDriveSystem.getElectricCurrentThree();
                break;
            case "electricCurrentFour":
                result = tbmDriveSystem.getElectricCurrentFour();
                break;
            case "electricCurrentFive":
                result = tbmDriveSystem.getElectricCurrentFive();
                break;
            case "electricCurrentSix":
                result = tbmDriveSystem.getElectricCurrentSix();
                break;
            case "electricCurrentSeven":
                result = tbmDriveSystem.getElectricCurrentSeven();
                break;
            case "electricCurrentEight":
                result = tbmDriveSystem.getElectricCurrentEight();
                break;
            case "electricCurrentNine":
                result = tbmDriveSystem.getElectricCurrentNine();
                break;
            case "electricCurrentTen":
                result = tbmDriveSystem.getElectricCurrentTen();
                break;
            case "electricCurrentEleven":
                result = tbmDriveSystem.getElectricCurrentEleven();
                break;
            case "electricCurrentTwelve":
                result = tbmDriveSystem.getElectricCurrentTwelve();
                break;
            case "electricCurrentThirteen":
                result = tbmDriveSystem.getElectricCurrentThirteen();
                break;
            case "electricCurrentFourteen":
                result = tbmDriveSystem.getElectricCurrentFourteen();
                break;
            default:
        }
        return result;
    }

    /**
     * 根据传入的电机温度编号返回对应字段的值
     *
     * @param tbmDriveSystem
     * @param temperature
     * @return
     */
    public static BigDecimal getTemperature(TbmDriveSystem tbmDriveSystem, String temperature) {
        BigDecimal result = BigDecimal.ZERO;
        String s = underscoreToCamelCase(temperature);
        switch (s) {
            case "temperatureOne":
                result = tbmDriveSystem.getTemperatureOne();
                break;
            case "temperatureTwo":
                result = tbmDriveSystem.getTemperatureTwo();
                break;
            case "temperatureThree":
                result = tbmDriveSystem.getTemperatureThree();
                break;
            case "temperatureFour":
                result = tbmDriveSystem.getTemperatureFour();
                break;
            case "temperatureFive":
                result = tbmDriveSystem.getTemperatureFive();
                break;
            case "temperatureSix":
                result = tbmDriveSystem.getTemperatureSix();
                break;
            case "temperatureSeven":
                result = tbmDriveSystem.getTemperatureSeven();
                break;
            case "temperatureEight":
                result = tbmDriveSystem.getTemperatureEight();
                break;
            case "temperatureNine":
                result = tbmDriveSystem.getTemperatureNine();
                break;
            case "temperatureTen":
                result = tbmDriveSystem.getTemperatureTen();
                break;
            case "temperatureEleven":
                result = tbmDriveSystem.getTemperatureEleven();
                break;
            case "temperatureTwelve":
                result = tbmDriveSystem.getTemperatureTwelve();
                break;
            case "temperatureThirteen":
                result = tbmDriveSystem.getTemperatureThirteen();
                break;
            case "temperatureFourteen":
                result = tbmDriveSystem.getTemperatureFourteen();
                break;
            default:
        }
        return result;
    }
}
