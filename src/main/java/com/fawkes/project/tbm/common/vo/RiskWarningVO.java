package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 风险源预警
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@ApiModel("风险源预警VO")
public class RiskWarningVO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备code
     */
    @ApiModelProperty("设备code")
    private String deviceCode;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 风险名称
     */
    @ApiModelProperty("风险名称")
    private String riskName;

    /**
     * 风险距离
     */
    @ApiModelProperty("风险距离")
    private BigDecimal riskDistance;

    /**
     * 风险起始点距离
     */
    @ApiModelProperty("风险起始点距离")
    private BigDecimal riskStart;

    /**
     * 风险终点距离
     */
    @ApiModelProperty("风险终点距离")
    private BigDecimal riskEnd;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 附件
     */
    @ApiModelProperty("附件")
    private String attachment;

}