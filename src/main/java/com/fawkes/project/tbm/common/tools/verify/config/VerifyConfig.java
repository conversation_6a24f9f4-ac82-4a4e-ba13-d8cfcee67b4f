package com.fawkes.project.tbm.common.tools.verify.config;

import com.fawkes.project.tbm.common.tools.verify.enums.VerifyType;
import com.fawkes.project.tbm.common.tools.verify.verifypackage.DataRangeVerifyPackage;
import com.fawkes.project.tbm.common.tools.verify.verifypackage.LengthVerifyPackage;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.function.Function;

/**
 * 验证配置
 * <AUTHOR>
 */
public class VerifyConfig<T> {

    /** 验证项目列表 */
    @Getter
    protected final List<VerifyItem<T>> verifyItemList = new ArrayList<>();

    /** 是否快速失败 */
    @Getter
    @Setter
    private Boolean shouldFailFast = true;

    /**
     * 获取空验证配置
     * @param <T> 验证类型
     * @return 空验证配置
     */
    public static <T> VerifyConfig<T> emptyVerifyConfig() {
        return new VerifyConfig<>();
    }

    /**
     * 添加验证配置
     * @param verifyType 验证类型
     * @param contextSelector 提取验证内容的函数
     * @param errorMessageGenerator 错误消息生成器
     * @return 验证配置 用于链式调用
     */
    public VerifyConfig<T> addConfig(VerifyType verifyType, Function<T, Object> contextSelector, Function<T, String> errorMessageGenerator) {
        VerifyItem<T> verifyItem = new VerifyItem<>();
        verifyItem.setVerifyType(verifyType);
        verifyItem.setContextSelector(contextSelector);
        verifyItem.setErrorMessageGenerator(errorMessageGenerator);
        verifyItemList.add(verifyItem);

        return this;
    }

    /**
     * 添加验证配置
     * @param verifyType 验证类型
     * @param contextSelector 提取验证内容的函数
     * @param errorMessage 错误消息
     * @return 验证配置 用于链式调用
     */
    public VerifyConfig<T> addConfig(VerifyType verifyType, Function<T, Object> contextSelector, String errorMessage) {
        return this.addConfig(verifyType, contextSelector, item -> errorMessage);
    }

    /**
     * 添加长度验证验证配置
     * @param min 允许的最小长度
     * @param max 允许的最大长度
     * @param contextSelector 提取验证内容的函数
     * @param errorMessageGenerator 错误消息生成器
     * @return 验证配置 用于链式调用
     */
    public VerifyConfig<T> addLengthVerify(Integer min, Integer max, Function<T, String> contextSelector, Function<T, String> errorMessageGenerator) {
        return this.addConfig(VerifyType.Length,
                item -> LengthVerifyPackage.valueOf(contextSelector.apply(item), min, max),
                errorMessageGenerator);
    }

    /**
     * 添加最小长度验证验证配置
     * @param min 允许的最小长度
     * @param contextSelector 提取验证内容的函数
     * @param errorMessageGenerator 错误消息生成器
     * @return 验证配置 用于链式调用
     */
    public VerifyConfig<T> addLengthMinVerify(Integer min, Function<T, String> contextSelector, Function<T, String> errorMessageGenerator) {
        return this.addConfig(VerifyType.Length,
                item -> LengthVerifyPackage.valueOf(contextSelector.apply(item), min, null),
                errorMessageGenerator);
    }

    /**
     * 添加最大长度验证验证配置
     * @param max 允许的最大长度
     * @param contextSelector 提取验证内容的函数
     * @param errorMessageGenerator 错误消息生成器
     * @return 验证配置 用于链式调用
     */
    public VerifyConfig<T> addLengthMaxVerify(Integer max, Function<T, String> contextSelector, Function<T, String> errorMessageGenerator) {
        return this.addConfig(VerifyType.Length,
                item -> LengthVerifyPackage.valueOf(contextSelector.apply(item), null, max),
                errorMessageGenerator);
    }

    /**
     * 添加最大长度验证验证配置
     * @param max 允许的最大长度
     * @param contextSelector 提取验证内容的函数
     * @param errorMessageGenerator 错误消息生成器
     * @return 验证配置 用于链式调用
     */
    public VerifyConfig<T> addLengthMaxVerify(Integer max, Function<T, String> contextSelector, String errorMessageGenerator) {
        return this.addConfig(VerifyType.Length,
                item -> LengthVerifyPackage.valueOf(contextSelector.apply(item), null, max),
                errorMessageGenerator);
    }

    /**
     * 添加容器类型不能为空校验
     * @param contextSelector 提取验证内容的函数
     * @param errorMessageGenerator 错误消息生成器
     * @return 验证配置 用于链式调用
     */
    public VerifyConfig<T> addNotEmptyVerify(Function<T, Collection<?>> contextSelector, Function<T, String> errorMessageGenerator) {
        return this.addConfig(VerifyType.Length,
                item -> LengthVerifyPackage.valueOf(contextSelector.apply(item), 1, null),
                errorMessageGenerator);
    }

    /**
     * 添加容器类型不能为空校验
     * @param contextSelector 提取验证内容的函数
     * @param errorMessage 错误消息
     * @return 验证配置 用于链式调用
     */
    public VerifyConfig<T> addNotEmptyVerify(Function<T, Collection<?>> contextSelector, String errorMessage) {
        return this.addConfig(VerifyType.Length,
                item -> LengthVerifyPackage.valueOf(contextSelector.apply(item), 1, null),
                errorMessage);
    }

    /**
     * 添加数据范围验证配置
     * @param rangeSet 数据范围值集合
     * @param contextSelector 提取验证内容的函数
     * @param errorMessageGenerator 错误消息生成器
     * @param <TValue> 数据范围值类型
     * @return  验证配置 用于链式调用
     */
    public <TValue> VerifyConfig<T> addInDataRangeVerify(Set<TValue> rangeSet, Function<T, TValue> contextSelector, Function<T, String> errorMessageGenerator){
        return this.addConfig(VerifyType.InDataRange,
                item -> DataRangeVerifyPackage.valueOf(contextSelector.apply(item), rangeSet),
                errorMessageGenerator);
    }

    /**
     * 添加数据范围验证配置
     * @param rangeSet 数据范围值集合
     * @param contextSelector 提取验证内容的函数
     * @param errorMessage 错误消息
     * @param <TValue> 数据范围值类型
     * @return  验证配置 用于链式调用
     */
    public <TValue> VerifyConfig<T> addInDataRangeVerify(Set<TValue> rangeSet, Function<T, TValue> contextSelector, String errorMessage){
        return this.addConfig(VerifyType.InDataRange,
                item -> DataRangeVerifyPackage.valueOf(contextSelector.apply(item), rangeSet),
                item -> errorMessage);
    }

    /**
     * 添加数据范围验证配置
     * @param rangeSet 数据范围值集合
     * @param contextSelector 提取验证内容的函数
     * @param errorMessageGenerator 错误消息生成器
     * @param <TValue> 数据范围值类型
     * @return  验证配置 用于链式调用
     */
    public <TValue> VerifyConfig<T> addNotInDataRangeVerify(Set<TValue> rangeSet, Function<T, TValue> contextSelector, Function<T, String> errorMessageGenerator){
        return this.addConfig(VerifyType.NotInDataRange,
                item -> DataRangeVerifyPackage.valueOf(contextSelector.apply(item), rangeSet),
                errorMessageGenerator);
    }

    /**
     * 添加数据范围验证配置
     * @param rangeSet 数据范围值集合
     * @param contextSelector 提取验证内容的函数
     * @param errorMessage 错误消息
     * @param <TValue> 数据范围值类型
     * @return  验证配置 用于链式调用
     */
    public <TValue> VerifyConfig<T> addNotInDataRangeVerify(Set<TValue> rangeSet, Function<T, TValue> contextSelector, String errorMessage){
        return this.addConfig(VerifyType.NotInDataRange,
                item -> DataRangeVerifyPackage.valueOf(contextSelector.apply(item), rangeSet),
                item -> errorMessage);
    }

    /**
     * 添加时间范围验证配置
     * @param verifyType 验证类型
     * @param contextSelector 提取验证内容的函数
     * @param errorMessage 错误消息
     * @return 验证配置 用于链式调用
     */
    public VerifyConfig<T> addDateRangeVerify(VerifyType verifyType, Function<T, Object> contextSelector, String errorMessage) {
        return this.addConfig(verifyType, contextSelector, item -> errorMessage);
    }
}
