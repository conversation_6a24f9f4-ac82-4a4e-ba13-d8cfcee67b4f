package com.fawkes.project.tbm.common.tools.verify.verifyprovider.impl;

import cn.hutool.core.util.PhoneUtil;
import com.fawkes.project.tbm.common.tools.verify.enums.VerifyType;
import com.fawkes.project.tbm.common.tools.verify.verifyprovider.VerifyProvider;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 手机号格式校验
 * <AUTHOR>
 * @version PhoneVerifyProvider 2023/8/23 16:51
 */
@Component
public class PhoneVerifyProvider implements VerifyProvider {
    /**
     * 支持的验证类型
     * @return 支持的验证类型
     */
    @Override
    public VerifyType supportedVerifyType() {
        return VerifyType.Phone;
    }

    /**
     * 验证
     * @param target 验证目标
     * @return 验证结果 true:验证通过  false:验证失败
     * @throws Exception 验证异常
     */
    @Override
    public boolean verify(Object target) throws Exception {
        if(target == null) {
            return true;
        }

        if(target instanceof String == false) {
            throw new IllegalArgumentException("手机号格式校验只支持String类型");
        }
        if(StringUtils.isBlank(target.toString())){
            return true;
        }

        return PhoneUtil.isPhone((String) target);
    }
}
