package com.fawkes.project.tbm.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 标段位置表
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@ApiModel("标段位置VO")
public class SectionLocationVO implements Serializable {

    /**
     * 区段
     */
    @ApiModelProperty("区段")
    private String section;

    @ApiModelProperty("位置点位列表")
    private List<LocationVO> location;

}