package com.fawkes.project.tbm.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 风险统计
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@ApiModel("风险统计VO")
public class RiskStatisticsVO implements Serializable {

    /**
     * 已通过
     */
    @ApiModelProperty("已通过")
    private Integer pass;

    /**
     * 正在通过
     */
    @ApiModelProperty("正在通过")
    private Integer ing;

    /**
     * 临近风险
     */
    @ApiModelProperty("临近风险")
    private Integer near;

    /**
     * 远距离风险
     */
    @ApiModelProperty("远距离风险")
    private Integer remote;

}