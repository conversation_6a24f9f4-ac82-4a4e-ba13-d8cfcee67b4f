package com.fawkes.project.tbm.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fawkes.project.tbm.common.model.TbmTappingSystem;
import com.fawkes.project.tbm.common.vo.TbmTappingSystemXYVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * TBM出渣系统参数(此表为原始表，数据按月分表) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface TbmTappingSystemMapper extends BaseMapper<TbmTappingSystem> {

    /**
     * 获取最后十个环号对应的 TbmTappingSystem 对象
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     */
    List<TbmTappingSystemXYVO> getRingNumTbmTappingSystem(@Param("deviceCode") String deviceCode,
                                                          @Param("ringNumList") List<Integer> ringNumList,
                                                          @Param("tableName") String tableName);

    /**
     * 是否存在表
     *
     * @param tableName
     * @return
     */
    int existTable(@Param("tableName") String tableName);

    /**
     * 删除表
     *
     * @param tableName
     * @return
     */
    int dropTable(@Param("tableName") String tableName);

    /**
     * 创建表
     *
     * @param tableName
     * @return
     */
    int createTable(@Param("tableName") String tableName);
}
