package com.fawkes.project.tbm.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fawkes.project.tbm.common.model.TbmEarthSupportAlarm;
import com.fawkes.project.tbm.common.model.TbmTappingSystemAlarm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * TBM出渣系统-告警表(数据按月分表)
 *
 * <AUTHOR>
 */
@Mapper
public interface TbmTappingSystemAlarmMapper extends BaseMapper<TbmTappingSystemAlarm> {

    /**
     * 是否存在表
     *
     * @param tableName
     * @return
     */
    int existTable(@Param("tableName") String tableName);

    /**
     * 删除表
     *
     * @param tableName
     * @return
     */
    int dropTable(@Param("tableName") String tableName);

    /**
     * 创建表
     *
     * @param tableName
     * @return
     */
    int createTable(@Param("tableName") String tableName);
}
