package com.fawkes.project.tbm.common.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 工序管理导入数据VO
 * @Date 2025/6/9 10:25
 */
@Data
@ContentRowHeight(20)
public class ProcessManagementExportVO {

    @ExcelProperty(value = "*区段", index = 0)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String section;

    @ExcelProperty(value = "*环号", index = 1)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String ringNum;

    @ExcelProperty(value = "*拼装点位", index = 2)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String assemblyPoint;

    @ExcelProperty(value = {"掘进工序填报", "（掘进）开始时间"},  index = 3)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String tunnelingStartTime;

    @ExcelProperty(value = {"掘进工序填报", "（掘进）结束时间"}, index = 4)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String tunnelingEndTime;

    @ExcelProperty(value = {"掘进工序填报", "（换步）开始时间"}, index = 5)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String changeStepsStartTime;

    @ExcelProperty(value = {"掘进工序填报", "（换步）结束时间"}, index = 6)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String changeStepsEndTime;

    @ExcelProperty(value = {"掘进工序填报", "（管片拼装）开始时间"}, index = 7)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String segmentAssemblyStartTime;

    @ExcelProperty(value = {"掘进工序填报", "（管片拼装）结束时间"}, index = 8)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String segmentAssemblyEndTime;

    @ExcelProperty(value = {"掘进工序填报", "（其它工作）开始时间"}, index = 9)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String othersJobStartTime;

    @ExcelProperty(value = {"掘进工序填报", "（其它工作）结束时间"}, index = 10)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String othersJobEndTime;

    @ExcelProperty(value = {"掘进工序填报", "（维修保养-常规保养）开始时间"},  index = 11)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String commonMaintenanceAndRepairStartTime;

    @ExcelProperty(value = {"掘进工序填报", "（维修保养-常规保养）结束时间"}, index = 12)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String commonMaintenanceAndRepairEndTime;

    @ExcelProperty(value = {"掘进工序填报", "（维修保养-故障停机）开始时间"}, index = 13)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String shutdownMaintenanceAndRepairStartTime;

    @ExcelProperty(value = {"掘进工序填报", "（维修保养-故障停机）结束时间"}, index = 14)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String shutdownMaintenanceAndRepairEndTime;

    @ExcelProperty(value = {"掘进工序填报", "（开仓换刀）开始时间"}, index = 15)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String openWarehouseChangeToolStartTime;

    @ExcelProperty(value = {"掘进工序填报", "（开仓换刀）结束时间"}, index = 16)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String openWarehouseChangeToolEndTime;

    @ExcelProperty(value = {"掘进工序填报", "第几仓"}, index = 17)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String openWarehouseChangeToolNum;

    @ExcelProperty(value = "异常情况说明", index = 18)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String descriptionOfAbnormalSituations;

    @ExcelProperty(value = "渣土性状", index = 19)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String constructionWasteSoilProperties;

    @ExcelProperty(value = "出渣量（m³）", index = 20)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String residueDischargeVolume;

    @ExcelProperty(value = "管片坡度", index = 21)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String slopeOfTheSegment;

    @ExcelProperty(value = "泡沫剂使用（KG）", index = 22)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String useOfFoamingAgent;

    @ExcelProperty(value = "油脂使用（KG）", index = 23)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String greaseUsage;

    @ExcelProperty(value = "膨润土", index = 24)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String bentonite;

    @ExcelProperty(value = "其他", index = 25)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String others;

    @ExcelProperty(value = "备注", index = 26)
    @ColumnWidth(20)
    @ContentStyle(wrapped = BooleanEnum.TRUE,verticalAlignment = VerticalAlignmentEnum.CENTER)
    private String remark;
}
