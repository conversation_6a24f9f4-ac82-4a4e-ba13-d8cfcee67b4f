package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * TBM土仓压力参数环数表
 * <AUTHOR>
 * @version TbmEarthSupportRing 2025/6/10 18:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("TBM土仓压力参数环数表")
@TableName("tbm_earth_support_ring")
public class TbmEarthSupportRing implements Serializable {
    /** 主键id */
    private Long id;
    /** 设备编码 */
    private String code;
    /** 环数 */
    private Integer ringNum;
    /** 最新更新时间 */
    private LocalDateTime maxTime;
}

