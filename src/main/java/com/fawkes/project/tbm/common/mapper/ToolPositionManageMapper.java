package com.fawkes.project.tbm.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fawkes.project.tbm.common.model.ToolPositionManage;
import com.fawkes.project.tbm.common.vo.ToolPositionManageGroupVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ToolPositionManageMapper extends BaseMapper<ToolPositionManage> {

    /**
     * 查询区域下的刀具位置分组
     * @param section 区域code
     * @return 分组集合
     */
    List<ToolPositionManageGroupVO> listGroupVO(@Param("section") String section);

}