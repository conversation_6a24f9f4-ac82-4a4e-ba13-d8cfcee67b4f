package com.fawkes.project.tbm.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fawkes.project.tbm.common.model.TbmCutDisk;
import com.fawkes.project.tbm.common.vo.TbmCutDiskXYVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * TBM刀盘系统参数表(此表为原始表，数据按月分表) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Mapper
public interface TbmCutDiskMapper extends BaseMapper<TbmCutDisk> {

    /**
     * 十个环号对应的TbmCutDisk对象
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     */
    List<TbmCutDiskXYVO> getRingNumTbmCutDisk(@Param("deviceCode") String deviceCode,
                                              @Param("ringNumList") List<Integer> ringNumList,
                                              @Param("tableName") String tableName);

    /**
     * 是否存在表
     *
     * @param tableName
     * @return
     */
    int existTable(@Param("tableName") String tableName);

    /**
     * 删除表
     *
     * @param tableName
     * @return
     */
    int dropTable(@Param("tableName") String tableName);

    /**
     * 创建表
     *
     * @param tableName
     * @return
     */
    int createTable(@Param("tableName") String tableName);
}
