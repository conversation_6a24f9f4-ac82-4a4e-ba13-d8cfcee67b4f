package com.fawkes.project.tbm.common.utils;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.pojo.query.GetFileInfoParam;
import com.fawkes.project.tbm.client.StorageClient;
import com.fawkes.project.tbm.common.param.FileInfoParam;
import com.fawkes.project.tbm.common.vo.SysOssVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-03-10
 * @Description 利用token得到文件信息
 */
@Component
@Slf4j
public class TransferFileTool {

    @Resource
    private StorageClient storageClient;

    /**
     * @param f8s  file_token
     * @return  SysOssVo
     */
    public List<SysOssVO> getFileInfoByToken(List<String> f8s, boolean getStream){
        try{
            //调用查询接口,得到文件格式
            GetFileInfoParam param = new GetFileInfoParam();
            param.setF8s(f8s);
            ApiResponseBody<List<SysOssVO>> fileInfo = storageClient.getFileByToken(param);
            List<SysOssVO> list = new ArrayList<>();
            if (fileInfo.getData().size()>0) {
                if (getStream){
                    for(int i = 0; i<fileInfo.getData().size(); i++){
                        //linkHashMap转对象
                        SysOssVO sysOssVO = JSON.parseObject(JSON.toJSONString(fileInfo.getData().get(i)),new TypeReference<SysOssVO>(){});
                        sysOssVO.setResource((ByteArrayResource) storageClient.download(sysOssVO.getFileToken()).getBody());
                        list.add(sysOssVO);
                    }
                }
            }
            return list;
        }catch (Exception e){
            log.error("getFileInfoByToken error: {}", e);
            return null;
        }
    }

    /**
     * 下载文件流
     *
     * @param g9s
     * @return
     */
    public byte[] download(String g9s){
        try {
            FileInfoParam fileInfoParam = new FileInfoParam();
            ArrayList<String> g9sList = new ArrayList<>();
            g9sList.add(g9s);
            fileInfoParam.setG9s(g9sList);
            // 服务调用
            ApiResponseBody apiResponseBody = storageClient.file(fileInfoParam);
            if (apiResponseBody != null) {
                List list = (List) apiResponseBody.getData();
                if (CollectionUtils.isNotEmpty(list)) {
                    Object object = list.get(0);
                    HashMap<String, Object> returnData = (HashMap<String, Object>) object;
                    String f8s = (String) returnData.get("fileToken");
                    ResponseEntity<org.springframework.core.io.Resource> responseEntity = storageClient.download(f8s);
                    org.springframework.core.io.Resource responseEntityBody = responseEntity.getBody();
                    if (responseEntityBody == null) {
                        return null;
                    }
                    InputStream inputStream = responseEntityBody.getInputStream();
                    return inputStreamToByteArray(inputStream);
                }
            }

            return null;
        } catch (IOException e) {
            log.error("download error: {}", e);
            return null;
        }
    }

    /**
     * 获取InputStream数据的字节数组
     *
     * @param inputStream
     * @return
     * @throws IOException
     */
    private byte[] inputStreamToByteArray(InputStream inputStream) throws IOException {
        if (inputStream == null) {
            return null;
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[4096];
        int length;
        while ((length = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, length);
        }
        outputStream.flush();
        return outputStream.toByteArray();
    }
}