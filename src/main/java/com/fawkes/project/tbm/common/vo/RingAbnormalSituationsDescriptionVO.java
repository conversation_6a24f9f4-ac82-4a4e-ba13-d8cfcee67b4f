package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/6/10 15:11
 */
@Data
@ApiModel("环号的异常情况说明返回VO")
public class RingAbnormalSituationsDescriptionVO {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("环数")
    private String ringNum;

    @ApiModelProperty("异常情况")
    private String descriptionOfAbnormalSituations;

    private String rowNumber;
}
