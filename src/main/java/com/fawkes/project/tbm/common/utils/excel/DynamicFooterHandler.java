package com.fawkes.project.tbm.common.utils.excel;

import lombok.Getter;
import rx.functions.Action2;

import java.util.List;

/**
 * 动态表尾处理器
 * <AUTHOR>
 */
public class DynamicFooterHandler<T> {

    /** 动态表尾开始的位置 */
    @Getter
    private final int dynamicFooterIndex;
    /** 表尾处理回调 */
    private final Action2<T, List<String>> handleCallback;

    /**
     * 构造
     * @param dynamicFooterIndex 动态表尾开始的位置
     * @param handleCallback 表尾处理回调
     */
    public DynamicFooterHandler(int dynamicFooterIndex, Action2<T, List<String>> handleCallback) {
        this.dynamicFooterIndex = dynamicFooterIndex;
        this.handleCallback = handleCallback;
    }

    /**
     * 处理动态表尾
     * @param object 需设置的对象
     * @param footerDataList 未处理的表尾数据
     */
    public void handle(T object, List<String> footerDataList) {
        this.handleCallback.call(object, footerDataList);
    }
}
