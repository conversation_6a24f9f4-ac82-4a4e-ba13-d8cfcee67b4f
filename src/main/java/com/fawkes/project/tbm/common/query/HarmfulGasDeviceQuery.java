package com.fawkes.project.tbm.common.query;

import com.fawkes.core.pojo.query.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 有害气体设备管理表
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel("有害气体设备管理查询参数")
public class HarmfulGasDeviceQuery extends PageParam {

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String name;

    /**
     * TBM设备名称
     */
    @ApiModelProperty("TBM设备名称")
    private String deviceName;


}