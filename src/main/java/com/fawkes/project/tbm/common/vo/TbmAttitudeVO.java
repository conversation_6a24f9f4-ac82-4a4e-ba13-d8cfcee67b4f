package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * TBM姿态VO
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel("TBM姿态VO")
public class TbmAttitudeVO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 水平-后/Double/mm
     */
    @ApiModelProperty("水平-后/Double/mm")
    private String horizontalEnd;

    /**
     * 水平-前/Double/mm
     */
    @ApiModelProperty("水平-前/Double/mm")
    private String horizontalFront;

    /**
     * 垂直-后/Double/mm
     */
    @ApiModelProperty("垂直-后/Double/mm")
    private String verticalEnd;

    /**
     * 垂直-前/Double/mm
     */
    @ApiModelProperty("垂直-前/Double/mm")
    private String verticalFront;

    /**
     * 滚动角/Double/mm/m
     */
    @ApiModelProperty("滚动角/Double/mm/m")
    private String rollingAngle;

    /**
     * 俯仰角/Double/mm/m
     */
    @ApiModelProperty("俯仰角/Double/mm/m")
    private String pitchAngle;


    /**
     * 水平-后列表
     */
    @ApiModelProperty("水平-后列表")
    private List<TbmXYVO> horizontalEndList;

    /**
     * 水平-前列表
     */
    @ApiModelProperty("水平-前列表")
    private List<TbmXYVO> horizontalFrontList;

    /**
     * 垂直-后列表
     */
    @ApiModelProperty("垂直-后列表")
    private List<TbmXYVO> verticalEndList;

    /**
     * 垂直-前列表
     */
    @ApiModelProperty("垂直-前列表")
    private List<TbmXYVO> verticalFrontList;

    /**
     * 告警状态，0-正常，1-异常
     */
    @ApiModelProperty("告警状态，0-正常，1-异常")
    private Integer alarmStatus;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("告警id")
    private Long alarmId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd")
    private Timestamp createTime;

    /**
     * 环数
     */
    @ApiModelProperty("环数")
    private Integer ringNum;

    /**
     * 盾首坐标X
     */
    @ApiModelProperty("盾首坐标X")
    private String dishCoordinateX;

    /**
     * 盾首坐标Y
     */
    @ApiModelProperty("盾首坐标Y")
    private String dishCoordinateY;

    /**
     * 盾首坐标Z
     */
    @ApiModelProperty("盾首坐标Z")
    private String dishCoordinateZ;

    /**
     * 经度
     */
    @ApiModelProperty("经度")
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    private String latitude;

    /**
     * 经度-高德
     */
    @ApiModelProperty("经度-高德")
    private String longitudeGc;

    /**
     * 纬度-高德
     */
    @ApiModelProperty("纬度-高德")
    private String latitudeGc;
}