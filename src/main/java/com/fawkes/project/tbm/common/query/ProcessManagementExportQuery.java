package com.fawkes.project.tbm.common.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 工序管理导出
 * <AUTHOR>
 */
@Data
@ApiModel("工序管理导出")
public class ProcessManagementExportQuery {

    /** 工序管理导出ID */
    @ApiModelProperty("工序管理导出ID")
    @NotEmpty(message = "工序管理ID不能为空")
    private List<Long> exportIdList;
}
