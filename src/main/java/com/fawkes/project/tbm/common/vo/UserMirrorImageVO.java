package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UserMirrorImageVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private String userName;

//    private String password;

    private String userFullname;

    private String sex;

    private String phone;

    private String email;

    private String ext1;

    private String ext2;

    private String ext3;

    private String ext4;

    private String ext5;

    private String phoneMirror;

    @ApiModelProperty("账号周期 1长期 2临时")
    private String accountPeriod;

    @ApiModelProperty("账号状态：1、激活；2、休眠；3、注销")
    private Integer accountStatus;

    private String functionalIds;

    private String subProjectIds;

    private String roleIds;

    private String portalIds;
}
