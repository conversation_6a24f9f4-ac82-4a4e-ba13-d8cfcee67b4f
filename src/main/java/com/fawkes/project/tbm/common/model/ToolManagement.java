package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fawkes.core.base.model.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "tool_management")
public class ToolManagement extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 区段
     */
    private String section;

    /**
     * 填报人
     */
    private String createName;

    /**
     * 修改人名称
     */
    private String updateName;

    /**
     * 刀具名称id
     */
    private Long toolNameManageId;

    /**
     * 刀具位置id
     */
    private Long toolPositionManageId;

    /**
     * 环号
     */
    private Integer ringNum;

    /**
     * 磨损值
     */
    private BigDecimal wearValue;

    /**
     * 开始方位
     */
    private String startOrientation;

    /**
     * 开始里程
     */
    private BigDecimal startMileage;

    /**
     * 结束方位
     */
    private String endOrientation;

    /**
     * 结束里程
     */
    private BigDecimal endMileage;

    /**
     * 开仓开始时间
     */
    private Timestamp openWarehouseStartTime;

    /**
     * 开仓结束时间
     */
    private Timestamp openWarehouseEndTime;

    /**
     * 新装刀具状态
     */
    private String newlyInstalledCuttingToolsStatus;

    /**
     * 更换阈值
     */
    private BigDecimal changeThreshold;

    /**
     * 换刀原因
     */
    private String changeToolReason;

    /**
     * 是否换刀（true-是，false-否）
     */
    private boolean hasToolChanged;


    /**
     * 附件
     */
    private String attachment;

    /**
     * 备注
     */
    private String remark;


    /**
     * 换刀图片
     */
    private String changeToolPicture;

}