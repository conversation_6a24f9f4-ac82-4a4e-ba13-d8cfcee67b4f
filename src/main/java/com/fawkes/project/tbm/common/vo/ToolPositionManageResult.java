package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 刀具大屏左上角下拉框数据
 */
@Data
@ApiModel("刀具大屏左上角下拉框数据")
public class ToolPositionManageResult {

    /**
     * 刀具位置集合
     */
    @ApiModelProperty("刀具位置集合")
    private List<ToolPositionManageVO> positionManageVOList;


    /**
     * 最新记录的刀具位置id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("最新记录的刀具位置id")
    private Long id;
}
