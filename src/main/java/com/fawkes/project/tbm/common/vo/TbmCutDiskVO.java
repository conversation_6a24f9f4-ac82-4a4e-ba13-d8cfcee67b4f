package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * TBM刀盘系统VO
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel("TBM刀盘系统VO")
public class TbmCutDiskVO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 刀盘扭矩/Double/kN.M
     */
    @ApiModelProperty("刀盘扭矩/Double/kN.M")
    private String cutDiskTorchque;

    /**
     * 刀盘转速/Double/rpm
     */
    @ApiModelProperty("刀盘转速/Double/rpm")
    private String actualRpm;

    /**
     * 贯入度/Double/mm/rev
     */
    @ApiModelProperty("贯入度/Double/mm/rev")
    private String penetration;

    /**
     * 刀盘扭矩列表
     */
    @ApiModelProperty("刀盘扭矩列表")
    private List<TbmXYVO> cutDiskTorchqueList;

    /**
     * 刀盘转速列表
     */
    @ApiModelProperty("刀盘转速列表")
    private List<TbmXYVO> actualRpmList;

    /**
     * 贯入度列表
     */
    @ApiModelProperty("贯入度列表")
    private List<TbmXYVO> penetrationList;

    /**
     * 告警状态，0-正常，1-异常
     */
    @ApiModelProperty("告警状态，0-正常，1-异常")
    private Integer alarmStatus;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("告警id")
    private Long alarmId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd")
    private Timestamp createTime;

    /**
     * 环数
     */
    @ApiModelProperty("环数")
    private Integer ringNum;
}