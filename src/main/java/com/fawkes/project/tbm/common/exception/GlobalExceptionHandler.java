package com.fawkes.project.tbm.common.exception;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.enums.BizCodeMsgEnum;
import com.fawkes.core.exception.BusinessException;
import com.fawkes.core.log.utils.LogTool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 统一异常处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-5-5 19:34
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {
    @Resource
    private LogTool logTool;

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public Object handle(Exception e, HttpServletRequest request) {
        BizCodeMsgEnum bizCodeMsgEnum;
        if (e instanceof BusinessException) {
            BusinessException businessException = (BusinessException) e;
            if (Objects.isNull(businessException.bizCodeMsgEnum)) {
                return ApiResponseBody.error(BizCodeMsgEnum.ERROR.getCode(), businessException.getMessage());
            }
            bizCodeMsgEnum = businessException.bizCodeMsgEnum;
        } else {
            bizCodeMsgEnum = BizCodeMsgEnum.SYS_ERROR;
        }
        e.printStackTrace();
        logTool.exceptionLog(e);
        return ApiResponseBody.error(bizCodeMsgEnum);
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseBody
    public Object methodArgumentNotValidHandle(MethodArgumentNotValidException notValidException, HttpServletRequest request) {
        log.error(String.format("数据校验异常: %s", request.getRequestURI()), notValidException);
        List<ObjectError> allErrors = notValidException.getBindingResult().getAllErrors();
        List<String> errorsMessages = allErrors.stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.toList());
        String errorMsg = errorsMessages.toString()
                .replace("[", "").replace("]", "");
        return ApiResponseBody.error(BizCodeMsgEnum.PARAM_ERROR.getCode(), errorMsg);
    }

    @ExceptionHandler(value = BindException.class)
    @ResponseBody
    public Object bindExceptionHandle(BindException bindException, HttpServletRequest request) {
        log.error(String.format("数据绑定异常: %s", request.getRequestURI()), bindException);
        List<ObjectError> allErrors = bindException.getBindingResult().getAllErrors();
        List<String> errorsMessages = allErrors.stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.toList());
        String errorMsg = errorsMessages.toString()
                .replace("[", "").replace("]", "");
        return ApiResponseBody.error(BizCodeMsgEnum.PARAM_ERROR.getCode(), errorMsg);
    }
}
