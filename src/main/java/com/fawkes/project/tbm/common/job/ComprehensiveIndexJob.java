package com.fawkes.project.tbm.common.job;

import com.fawkes.project.tbm.common.handler.MonthTableNameHandler;
import com.fawkes.project.tbm.common.model.DeviceManage;
import com.fawkes.project.tbm.common.model.StatisticsComprehensiveIndex;
import com.fawkes.project.tbm.common.model.TbmAttitude;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.service.DeviceManageService;
import com.fawkes.project.tbm.service.StatisticsComprehensiveIndexService;
import com.fawkes.project.tbm.service.TbmAttitudeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Auther: xiaopeng
 * @Date: 2025/04/02/10:58
 * @Description:
 */
@EnableScheduling
@Component
@Slf4j
public class ComprehensiveIndexJob {
    @Resource
    private DeviceManageService deviceManageService;
    @Resource
    private TbmAttitudeService tbmAttitudeService;
    @Resource
    private StatisticsComprehensiveIndexService statisticsComprehensiveIndexService;

//    @Scheduled(cron = "0 * * * * *")
    @Scheduled(cron = "*/50 * * * * ?")
    public void generateComprehensiveIndex() {
        //log.info("集群大屏综合指标定时任务执行开始");
        List<DeviceManage> deviceManages = deviceManageService.list();
        if (CollectionUtils.isNotEmpty(deviceManages)) {
            // 当天日期（LocalDate）
            LocalDate localDate = LocalDate.now();
            Date currentDate = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            for (DeviceManage deviceManage : deviceManages) {
                StatisticsComprehensiveIndex index = new StatisticsComprehensiveIndex();
                // 物联网推过来的数据，累计里程，推进环数
                // 获取当天起始和结束时间
                MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
                LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
                LocalDateTime todayEnd = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
                TbmAttitude tbmAttitudeStart = tbmAttitudeService.lambdaQuery().eq(TbmAttitude::getCode, deviceManage.getCode())
                        .between(TbmAttitude::getCreateTime, todayStart, todayEnd).last("LIMIT 1").one();
                TbmAttitude tbmAttitudeEnd = tbmAttitudeService.lambdaQuery().eq(TbmAttitude::getCode, deviceManage.getCode())
                        .between(TbmAttitude::getCreateTime, todayStart, todayEnd).orderByDesc(TbmAttitude::getCreateTime).last("LIMIT 1").one();
                // 如果当天没有数据，就不统计了
                if (Objects.nonNull(tbmAttitudeStart) && Objects.nonNull(tbmAttitudeEnd)) {
                    index.setRingNum(tbmAttitudeEnd.getRingNum());
                    index.setMileage(tbmAttitudeEnd.getMileage());
                    index.setRuningState(tbmAttitudeEnd.getRuningState());
                    index.setTodayMileage(tbmAttitudeEnd.getMileage().subtract(tbmAttitudeStart.getMileage()));
                    index.setTodayRingNum(tbmAttitudeEnd.getRingNum() - tbmAttitudeStart.getRingNum());
                }else{
                    TbmAttitude tbmAttitude = tbmAttitudeService.lambdaQuery().eq(TbmAttitude::getCode, deviceManage.getCode()).orderByDesc(TbmAttitude::getCreateTime).last("LIMIT 1").one();
                    if(Objects.isNull(tbmAttitude)){
                        continue;
                    }
                    index.setRingNum(tbmAttitude.getRingNum());
                    index.setMileage(tbmAttitude.getMileage());
                    index.setRuningState(tbmAttitude.getRuningState());
                    index.setTodayMileage(new BigDecimal("0"));
                    index.setTodayRingNum(0);
                }
                index.setCode(deviceManage.getCode());
                index.setStatisticsDate(currentDate);
                MonthTableNameHandler.removeMonthData();
                // 入库
                StatisticsComprehensiveIndex comprehensiveIndex = statisticsComprehensiveIndexService.lambdaQuery()
                        .eq(StatisticsComprehensiveIndex::getStatisticsDate, currentDate)
                        .eq(StatisticsComprehensiveIndex::getCode, deviceManage.getCode()).last("LIMIT 1").one();
                if (Objects.nonNull(comprehensiveIndex)) {
                    index.setId(comprehensiveIndex.getId());
                }
                statisticsComprehensiveIndexService.saveOrUpdate(index);
            }
        }
        //log.info("集群大屏综合指标定时任务执行结束");
    }
}

