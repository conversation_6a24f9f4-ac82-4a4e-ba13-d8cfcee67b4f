package com.fawkes.project.tbm.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fawkes.project.tbm.common.model.TbmPropulsionSystem;
import com.fawkes.project.tbm.common.vo.TbmPropulsionSystemXYVO;
import com.fawkes.project.tbm.common.vo.TbmXYVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * TBM推进系统参数(此表为原始表，数据按月分表) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface TbmPropulsionSystemMapper extends BaseMapper<TbmPropulsionSystem> {

    /**
     * 获取最后十个环号对应的 TbmPropulsionSystem 对象
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     * @param cylinderPressureStr 油箱压力
     * @return
     */
    List<TbmPropulsionSystemXYVO> getRingNumTbmPropulsionSystem(@Param("deviceCode") String deviceCode,
                                                                @Param("ringNumList") List<Integer> ringNumList,
                                                                @Param("cylinderPressureStr") String cylinderPressureStr,
                                                                @Param("tableName") String tableName);

    /**
     * 是否存在表
     *
     * @param tableName
     * @return
     */
    int existTable(@Param("tableName") String tableName);

    /**
     * 删除表
     *
     * @param tableName
     * @return
     */
    int dropTable(@Param("tableName") String tableName);

    /**
     * 创建表
     *
     * @param tableName
     * @return
     */
    int createTable(@Param("tableName") String tableName);
}
