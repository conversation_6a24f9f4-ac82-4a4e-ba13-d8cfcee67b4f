package com.fawkes.project.tbm.common.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 有害气体设备管理表
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel("有害气体设备管理参数")
public class HarmfulGasDeviceParam implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @NotBlank(message = "设备编码不能为空")
    @Size(min = 1, max = 50, message = "设备编码长度不能超过50")
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 设备名称
     */
    @NotBlank(message = "设备名称不能为空")
    @Size(min = 1, max = 20, message = "设备名称长度不能超过20")
    @ApiModelProperty("设备名称")
    private String name;

    /**
     * TBM设备code
     */
    @NotBlank(message = "TBM设备code不能为空")
    @ApiModelProperty("TBM设备code")
    private String deviceCode;

    /**
     * TBM设备名称
     */
    @NotBlank(message = "TBM设备名称不能为空")
    @ApiModelProperty("TBM设备名称")
    private String deviceName;


}