package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * TBM推进系统VO
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel("TBM推进系统VO")
public class TbmPropulsionSystemVO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 推力/Double/kN
     */
    @ApiModelProperty("推力/Double/kN")
    private String thrust;

    /**
     * 推进速度/Double/mm/M
     */
    @ApiModelProperty("推进速度/Double/mm/M")
    private String propSpeed;

    /**
     * 油缸压力/bar
     */
    @ApiModelProperty("油缸压力/bar")
    private String cylinderPressure;

    /**
     * 油箱温度/Double/℃
     */
    @ApiModelProperty("油箱温度/Double/℃")
    private String oilTemp;

    /**
     * 推力列表
     */
    @ApiModelProperty("推力列表")
    private List<TbmXYVO> thrustList;

    /**
     * 推进速度
     */
    @ApiModelProperty("推进速度")
    private List<TbmXYVO> propSpeedList;

    /**
     * 油缸压力列表
     */
    @ApiModelProperty("油缸压力列表")
    private List<TbmXYVO> cylinderPressureList;

    /**
     * 油箱温度列表
     */
    @ApiModelProperty("油箱温度列表")
    private List<TbmXYVO> oilTempList;

    /**
     * 告警状态，0-正常，1-异常
     */
    @ApiModelProperty("告警状态，0-正常，1-异常")
    private Integer alarmStatus;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("告警id")
    private Long alarmId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd")
    private Timestamp createTime;

    /**
     * 环数
     */
    @ApiModelProperty("环数")
    private Integer ringNum;
}