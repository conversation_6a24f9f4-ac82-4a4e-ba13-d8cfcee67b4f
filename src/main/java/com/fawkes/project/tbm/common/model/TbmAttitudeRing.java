package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version TbmAttitudeRing 2025/6/12 9:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tbm_attitude_ring")
@ApiModel("TBM姿态环数参数表")
public class TbmAttitudeRing implements Serializable {
    /** 主键id */
    private Long id;
    /** 设备编码 */
    private String code;
    /** 环数 */
    private Integer ringNum;
    /** 最新更新时间 */
    private LocalDateTime maxTime;
}
