package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 安全运行配置表
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@ApiModel("安全运行配置VO")
public class SafeRuningVO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 安全运行时间
     */
    @ApiModelProperty("安全运行时间")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd")
    private Date runingStart;

}