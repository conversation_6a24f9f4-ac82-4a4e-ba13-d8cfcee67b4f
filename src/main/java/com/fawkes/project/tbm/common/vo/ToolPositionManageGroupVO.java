package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 刀具位置分组vo
 */
@Data
@ApiModel("刀具位置分组vo")
public class ToolPositionManageGroupVO {

    /**
     * 组id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("组id")
    private Long groupId;

    /**
     * 组名
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("组名")
    private String groupName;

    /**
     * 区域code
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("区域code")
    private String section;
}
