package com.fawkes.project.tbm.common.tools.verify.verifyprovider.impl;

import cn.hutool.core.util.IdcardUtil;
import com.fawkes.project.tbm.common.tools.verify.enums.VerifyType;
import com.fawkes.project.tbm.common.tools.verify.verifyprovider.VerifyProvider;
import org.apache.commons.math3.util.Pair;
import org.springframework.stereotype.Component;

/**
 * 身份证号格式校验
 *
 * <AUTHOR>
 */
@Component
public class IdCardAgeVerifyProvider implements VerifyProvider {
    /**
     * 支持的验证类型
     *
     * @return 支持的验证类型
     */
    @Override
    public VerifyType supportedVerifyType() {
        return VerifyType.IdCardAge;
    }

    /**
     * 验证
     *
     * @param target 验证目标
     * @return 验证结果 true:验证通过  false:验证失败
     * @throws Exception 验证异常
     */
    @Override
    public boolean verify(Object target) throws Exception {
        if (target == null) {
            return true;
        }

        if (target instanceof Pair) {
            Pair<String, Integer> pair = (Pair<String, Integer>) target;
            String left = pair.getKey();
            Integer right = pair.getValue();

            if (left == null) {
                return false;
            }

            if (right == null) {
                return false;
            }

            boolean validCard = IdcardUtil.isValidCard(left);
            if (!validCard) {
                return false;
            }

            int ageByIdCard = IdcardUtil.getAgeByIdCard(left);
            return ageByIdCard == right;
        }

        throw new IllegalArgumentException("身份证号格式校验target类型错误");
    }
}
