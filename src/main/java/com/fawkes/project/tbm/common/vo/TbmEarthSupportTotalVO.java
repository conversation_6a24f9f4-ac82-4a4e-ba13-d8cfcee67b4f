package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * 土仓压力总体数据VO
 *
 * <AUTHOR>
 */
@Data
public class TbmEarthSupportTotalVO {

    List<TbmEarthSupportVO> tbmEarthSupportVOList;

    /**
     * 告警状态，0-正常，1-异常
     */
    @ApiModelProperty("告警状态，0-正常，1-异常")
    private Integer alarmStatus;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("告警id")
    private Long alarmId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd")
    private Timestamp createTime;

    /**
     * 环数
     */
    @ApiModelProperty("环数")
    private Integer ringNum;
}
