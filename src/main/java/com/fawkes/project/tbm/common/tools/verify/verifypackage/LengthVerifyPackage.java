package com.fawkes.project.tbm.common.tools.verify.verifypackage;

import lombok.Getter;
import lombok.Setter;

/**
 * 长度校验参数包
 * <AUTHOR>
 */
@Getter
@Setter
public class LengthVerifyPackage {

    /** 需校验的值 */
    private Object value;

    /** 允许的最小长度 */
    private Integer min;

    /** 允许的最大长度 */
    private Integer max;

    /**
     * 长度校验参数包
     * @param value 需校验的值
     * @param min 允许的最小长度
     * @param max 允许的最大长度
     * @return 长度校验参数包
     */
    public static LengthVerifyPackage valueOf(Object value, Integer min, Integer max) {
        if(max == null && min == null) {
            throw new IllegalArgumentException("长度校验时 最大值与最小值 两项至少需要填一项");
        }

        LengthVerifyPackage verifyPackage = new LengthVerifyPackage();
        verifyPackage.value = value;
        verifyPackage.min = min;
        verifyPackage.max = max;
        return verifyPackage;
    }
}
