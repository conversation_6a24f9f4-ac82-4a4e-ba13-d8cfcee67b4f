package com.fawkes.project.tbm.common.config;

import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.toolkit.GlobalConfigUtils;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.fawkes.project.tbm.common.handler.MybatisPlusHandler;
import com.fawkes.secure.data.interceptor.PrepareInterceptor;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * 数据源配置
 *
 * <AUTHOR>
 * @date 2020/08/05
 */
@Configuration
@MapperScan(basePackages = {"com.fawkes.project.tbm.common.mapper"}, sqlSessionFactoryRef = "fawkesDataSourceFactory")
public class FawkesDataSourceConfig {
    @Resource
    private PrepareInterceptor prepareInterceptor;

    @Resource
    private MybatisPlusInterceptor mybatisPlusInterceptor;

    /**
     * 源
     */
    @Bean(name = "fawkesDataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.tbm")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * 工厂
     */
    @Bean("fawkesDataSourceFactory")
    @Primary
    @DependsOn("fawkesDataSource")
    public SqlSessionFactory dataSourceFactory() throws Exception {
        MybatisSqlSessionFactoryBean factoryBean = new MybatisSqlSessionFactoryBean();
        factoryBean.setDataSource(dataSource());
        factoryBean.setPlugins(new Interceptor[]{prepareInterceptor});
        // TODO mapper.xml文件放在resource下
        factoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/*.xml"));
        //获取mybatis-plus全局配置
        GlobalConfig globalConfig = GlobalConfigUtils.defaults();
        globalConfig.setMetaObjectHandler(new MybatisPlusHandler());
        factoryBean.setGlobalConfig(globalConfig);

        factoryBean.setPlugins(mybatisPlusInterceptor);
        return factoryBean.getObject();
    }


    /**
     * 模板
     */
    @Bean("fawkesSqlSessionTemplate")
    @Primary
    @DependsOn("fawkesDataSourceFactory")
    public SqlSessionTemplate sqlSessionTemplate(
            @Qualifier("fawkesDataSourceFactory") SqlSessionFactory sessionfactory) {
        return new SqlSessionTemplate(sessionfactory);
    }


    /**
     * 事务
     */
    @Bean(name = "fawkesTransactionManager")
    @Primary
    @DependsOn("fawkesDataSource")
    public DataSourceTransactionManager fawkesTransactionManager(@Qualifier("fawkesDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
