package com.fawkes.project.tbm.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * TBM散点图坐标VO
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel("TBM散点图坐标VO")
public class TbmXYVO implements Serializable {

    /**
     * x轴-环数
     */
    @ApiModelProperty("x轴-环数")
    private String ring;

    /**
     * y轴-最大值
     */
    @ApiModelProperty("y轴-最大值")
    private BigDecimal max;

    /**
     * y轴-评价值
     */
    @ApiModelProperty("y轴-平均值")
    private BigDecimal average;

}