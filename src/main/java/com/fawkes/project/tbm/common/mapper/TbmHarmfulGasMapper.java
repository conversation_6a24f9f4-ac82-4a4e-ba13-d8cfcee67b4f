package com.fawkes.project.tbm.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fawkes.project.tbm.common.model.TbmHarmfulGas;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * TBM有害气体参数表(此表为原始表，数据按月分表) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface TbmHarmfulGasMapper extends BaseMapper<TbmHarmfulGas> {

    List<TbmHarmfulGas> selectByCodeInLastOne(@Param("codeCollection")List<String> codeCollection,
                                              @Param("tableName") String tableName);

    /**
     * 是否存在表
     *
     * @param tableName
     * @return
     */
    int existTable(@Param("tableName") String tableName);

    /**
     * 删除表
     *
     * @param tableName
     * @return
     */
    int dropTable(@Param("tableName") String tableName);

    /**
     * 创建表
     *
     * @param tableName
     * @return
     */
    int createTable(@Param("tableName") String tableName);
}
