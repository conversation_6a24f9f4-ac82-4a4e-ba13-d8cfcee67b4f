package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * TBM支撑系统参数表(此表为原始表，数据按月分表)
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tbm_support_system")
@ApiModel("TBM支撑系统参数表(此表为原始表，数据按月分表)")
public class TbmSupportSystem implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 1#上层撑靴压力/Double/bar
     */
    @ApiModelProperty("1#上层撑靴压力/Double/bar")
    private BigDecimal upBracePressure1;

    /**
     * 2#上层撑靴压力/Double/bar
     */
    @ApiModelProperty("2#上层撑靴压力/Double/bar")
    private BigDecimal upBracePressure2;

    /**
     * 3#上层撑靴压力/Double/bar
     */
    @ApiModelProperty("3#上层撑靴压力/Double/bar")
    private BigDecimal upBracePressure3;

    /**
     * 4#上层撑靴压力/Double/bar
     */
    @ApiModelProperty("4#上层撑靴压力/Double/bar")
    private BigDecimal upBracePressure4;

    /**
     * 5#上层撑靴压力/Double/bar
     */
    @ApiModelProperty("5#上层撑靴压力/Double/bar")
    private BigDecimal upBracePressure5;

    /**
     * 6#上层撑靴压力/Double/bar
     */
    @ApiModelProperty("6#上层撑靴压力/Double/bar")
    private BigDecimal upBracePressure6;

    /**
     * 1#下层撑靴压力/Double/bar
     */
    @ApiModelProperty("1#下层撑靴压力/Double/bar")
    private BigDecimal downBracePressure1;

    /**
     * 2#下层撑靴压力/Double/bar
     */
    @ApiModelProperty("2#下层撑靴压力/Double/bar")
    private BigDecimal downBracePressure2;

    /**
     * 3#下层撑靴压力/Double/bar
     */
    @ApiModelProperty("3#下层撑靴压力/Double/bar")
    private BigDecimal downBracePressure3;

    /**
     * 4#下层撑靴压力/Double/bar
     */
    @ApiModelProperty("4#下层撑靴压力/Double/bar")
    private BigDecimal downBracePressure4;

    /**
     * 5#下层撑靴压力/Double/bar
     */
    @ApiModelProperty("5#下层撑靴压力/Double/bar")
    private BigDecimal downBracePressure5;

    /**
     * 6#下层撑靴压力/Double/bar
     */
    @ApiModelProperty("6#下层撑靴压力/Double/bar")
    private BigDecimal downBracePressure6;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Timestamp updateTime;

    /**
     * 环数
     */
    @ApiModelProperty("环数")
    private Integer ringNum;
}