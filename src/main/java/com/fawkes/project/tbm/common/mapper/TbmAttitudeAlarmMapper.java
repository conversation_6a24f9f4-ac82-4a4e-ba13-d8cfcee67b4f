package com.fawkes.project.tbm.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fawkes.project.tbm.common.model.TbmAttitudeAlarm;
import com.fawkes.project.tbm.common.model.TbmEarthSupportAlarm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * TBM姿态参数-告警表(数据按月分表)
 *
 * <AUTHOR>
 */
@Mapper
public interface TbmAttitudeAlarmMapper extends BaseMapper<TbmAttitudeAlarm> {

    /**
     * 是否存在表
     *
     * @param tableName
     * @return
     */
    int existTable(@Param("tableName") String tableName);

    /**
     * 删除表
     *
     * @param tableName
     * @return
     */
    int dropTable(@Param("tableName") String tableName);

    /**
     * 创建表
     *
     * @param tableName
     * @return
     */
    int createTable(@Param("tableName") String tableName);
}
