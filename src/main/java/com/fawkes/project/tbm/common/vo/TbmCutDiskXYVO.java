package com.fawkes.project.tbm.common.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version TbmCutDiskXYVO 2025/6/11 16:37
 */
@Data
public class TbmCutDiskXYVO implements Serializable {
    /**
     * x轴-环数
     */
    private String ring;
    /**
     * y轴-最大
     */
    private BigDecimal cutDiskTorchqueMax;
    /**
     * y轴-最大
     */
    private BigDecimal actualRpmMax;
    /**
     * y轴-最大
     */
    private BigDecimal penetrationMax;
    /**
     * y轴-平均
     */
    private BigDecimal cutDiskTorchqueAverage;
    /**
     * y轴-平均
     */
    private BigDecimal actualRpmAverage;
    /**
     * y轴-平均
     */
    private BigDecimal penetrationAverage;
}
