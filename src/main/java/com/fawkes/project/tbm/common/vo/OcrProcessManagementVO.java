package com.fawkes.project.tbm.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("工序OCR返回VO")
@Data
public class OcrProcessManagementVO {

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.section
     *
     * @mbggenerated
     */
    @ApiModelProperty("区段")
    private String section;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.ring_num
     *
     * @mbggenerated
     */
    @ApiModelProperty("环号")
    private String ringNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.assembly_point
     *
     * @mbggenerated
     */
    @ApiModelProperty("拼装点位")
    private String assemblyPoint;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.tunneling_start_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("掘进开始时间")
    private String tunnelingStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.tunneling_end_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("掘进结束时间")
    private String tunnelingEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.change_steps_start_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("换步开始时间")
    private String changeStepsStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.change_steps_end_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("换步结束时间")
    private String changeStepsEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.segment_assembly_start_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("管片拼装开始时间")
    private String segmentAssemblyStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.segment_assembly_end_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("管片拼装结束时间")
    private String segmentAssemblyEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.others_job_start_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("其他工作开始时间")
    private String othersJobStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.others_job_end_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("其他工作结束时间")
    private String othersJobEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.common_maintenance_and_repair_start_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("常规维修保养开始时间")
    private String commonMaintenanceAndRepairStartTime;

    @ApiModelProperty("常规维修保养开始时间")
    private String commonMaintenanceAndRepairEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.shutdown_maintenance_and_repair_end_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("故障停机维修保养结束时间")
    private String shutdownMaintenanceAndRepairEndTime;

    @ApiModelProperty("故障停机维修保养结束时间")
    private String shutdownMaintenanceAndRepairStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.open_warehouse_change_tool_start_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("开仓换刀开始时间")
    private String openWarehouseChangeToolStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.open_warehouse_change_tool_end_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("开仓换刀结束时间")
    private String openWarehouseChangeToolEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.open_warehouse_change_tool_num
     *
     * @mbggenerated
     */
    @ApiModelProperty("开仓换刀-第几仓")
    private String openWarehouseChangeToolNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.description_of_abnormal_situations
     *
     * @mbggenerated
     */
    @ApiModelProperty("异常情况说明")
    private String descriptionOfAbnormalSituations;

    @ApiModelProperty("渣土性状")
    private String constructionWasteSoilProperties;

    @ApiModelProperty("出渣量")
    private String residueDischargeVolume;

    @ApiModelProperty("管片坡度")
    private String slopeOfTheSegment;

    @ApiModelProperty("泡沫剂使用")
    private String useOfFoamingAgent;

    @ApiModelProperty("油脂使用")
    private String greaseUsage;

    @ApiModelProperty("膨润土")
    private String bentonite;

    @ApiModelProperty("其他")
    private String others;
}