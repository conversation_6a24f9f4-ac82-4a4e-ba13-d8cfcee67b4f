package com.fawkes.project.tbm.common.param;


import com.fawkes.core.pojo.query.FormExportFieldParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description 用于参考的代码模板，导出参数类，其他业务的导出需求可根据此模板修改
 * <AUTHOR>
 * @Date 2020/6/16
 * @Version 1.0
 **/
@Data
public class TemplateExportParam {
    @ApiModelProperty("导出的条目数，默认500条，最大5000条")
    private Integer exportSize = 500;

    @ApiModelProperty("排序方式，不传默认按id排序。id asc,name desc 的形式")
    private String order = "id";

    @ApiModelProperty("需要导出的字段以及对应的中文名,不传默认导所有字段。中文名默认使用数据库注释")
    private List<FormExportFieldParam> exportFields;

    @ApiModelProperty("业务对象名称")
    private String objName;

    @ApiModelProperty("导出的文件名，不含后缀")
    private String excelName;

    @ApiModelProperty("要导出的数据id串 不传默认所有")
    private List<Long> ids;
}
