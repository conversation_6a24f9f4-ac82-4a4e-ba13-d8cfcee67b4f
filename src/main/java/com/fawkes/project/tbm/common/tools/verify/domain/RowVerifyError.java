package com.fawkes.project.tbm.common.tools.verify.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fawkes.project.tbm.common.utils.ListUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NonNull;
import org.springframework.util.Assert;

import javax.validation.ConstraintViolation;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 行验证错误
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@ApiModel("行验证错误")
@Data
public class RowVerifyError<T> {

    /** 错误行号 */
    @ApiModelProperty("错误行号")
    private  Integer rowNo;

    /** 错误对象 */
    private  T errorObject;

    /** 错误信息列表 */
    @JsonIgnore
    private  List<String> errorMsgList;

    public RowVerifyError() {}

    /**
     * 过滤错误数据, 留下正确数据
     * <p>使用序号过滤, 序号不能为空</p>
     * @param importList 导入列表
     * @param verifyErrorList 验证错误列表
     * @return 正确数据列表
     */
    public static <T extends ISerialNumber> List<T> filterErrorData(List<T> importList, List<RowVerifyError<T>> verifyErrorList) {
        if(ListUtil.isEmpty(verifyErrorList)) {
            return importList;
        }
        if(ListUtil.isEmpty(importList)) {
            return ListUtil.emptyList();
        }

        Set<Integer> errorNoSet = ListUtil.toSet(verifyErrorList, RowVerifyError::getRowNo);
        return importList.stream()
                .filter(data -> !errorNoSet.contains(data.getSerialNumber()))
                .collect(Collectors.toList());
    }

    /**
     * 获取错误信息
     * @return 错误信息
     */
    @ApiModelProperty("错误信息")
    public String getErrorInfo() {
        return this.generateErrorMsg();
    }

    public RowVerifyError(Integer rowNo, T errorObject) {
        Assert.notNull(rowNo, "行号不能为null");
        Assert.isTrue(rowNo >= 1, "行号必须为正整数");

        this.rowNo = rowNo;
        this.errorObject = errorObject;
        this.errorMsgList = new ArrayList<>();
    }

    public RowVerifyError(Integer rowNo, T errorObject, List<String> errorMsgList) {
        Assert.notNull(rowNo, "行号不能为null");
        Assert.isTrue(rowNo >= 1, "行号必须为正整数");
        Assert.notEmpty(errorMsgList, "错误列表不能为空");

        this.rowNo = rowNo;
        this.errorObject = errorObject;
        this.errorMsgList = errorMsgList;
    }

    public RowVerifyError(Integer rowNo, T errorObject, String errorMsg) {
        Assert.notNull(rowNo, "行号不能为null");
        Assert.isTrue(rowNo >= 1, "行号必须为正整数");
        Assert.hasText(errorMsg, "错误信息不能为空");

        this.rowNo = rowNo;
        this.errorObject = errorObject;
        this.errorMsgList = new ArrayList<>();
        this.addErrorMsg(errorMsg);
    }

    /**
     * 创建行验证错误
     * @param rowNo 行号
     * @param format 格式化字符串
     * @param args 格式化参数
     * @return 行验证错误
     */
    public static <T> RowVerifyError<T> create(@NonNull Integer rowNo, T errorObject, String format, Object... args) {
        RowVerifyError<T> rowVerifyError = new RowVerifyError<>(rowNo, errorObject);
        rowVerifyError.addErrorMsg(String.format(format, args));
        return rowVerifyError;
    }

    /**
     * 创建行验证错误
     * @param rowNo 行号
     * @param errorMsg 错误信息
     * @return 行验证错误
     */
    public static <T> RowVerifyError<T> create(@NonNull Integer rowNo, T errorObject, String errorMsg) {
//        Assert.hasText(errorMsg, "错误信息不能为空");

        RowVerifyError<T> rowVerifyError = new RowVerifyError<>(rowNo, errorObject);
        rowVerifyError.addErrorMsg(errorMsg);
        return rowVerifyError;
    }

    /**
     * 创建行验证错误
     * @param rowNo 行号
     * @param constraintViolationSet 错误结合
     * @return 行验证错误
     */
    public static <T> RowVerifyError<T> create(int rowNo, T errorObject, Set<ConstraintViolation<?>> constraintViolationSet) {
//        Assert.notEmpty(constraintViolationSet, "错误信息不能为空");

        List<String> errorMsgList = ListUtil.map(constraintViolationSet, ConstraintViolation::getMessageTemplate);
        return new RowVerifyError<>(rowNo, errorObject, errorMsgList);
    }

    /**
     * 合并行验证错误
     * @param listA 行错误列表A
     * @param listB 行错误列表B
     * @return 合并后的列表
     */
    public static <T> List<RowVerifyError<T>> merge(List<RowVerifyError<T>> listA, List<RowVerifyError<T>> listB) {
        List<RowVerifyError<T>> merge = ListUtil.merge(listA, listB);
        return mergeSameRowNoError(merge);
    }

    /**
     * 合并列表中行号一致的错误消息
     * @param list 需合并的列表
     * @return 合并后的列表
     */
    public static <T> List<RowVerifyError<T>> mergeSameRowNoError(List<RowVerifyError<T>> list) {
        if(ListUtil.isEmpty(list)) {
            return ListUtil.emptyList();
        }

        return list.stream().collect(Collectors.groupingBy(RowVerifyError::getRowNo))
                .entrySet().stream()
                .map(group -> {
                    Integer rowNo = group.getKey();
                    List<RowVerifyError<T>> sameRowNoErrorList = group.getValue();

                    if(ListUtil.isSingleton(sameRowNoErrorList)) {
                        return ListUtil.firstOrThrow(sameRowNoErrorList);
                    } else {
                        // 合并错误信息
                        List<String> errorMsgList = sameRowNoErrorList.stream()
                                .flatMap(error -> error.errorMsgList.stream())
                                .distinct()
                                .collect(Collectors.toList());
                        // 获取错误对象
                        T errorObject = ListUtil.firstOrThrow(sameRowNoErrorList).errorObject;
                        return new RowVerifyError<>(rowNo, errorObject, errorMsgList);
                    }
                }).collect(Collectors.toList());
    }

    /**
     * 获取错误列表
     * @return 错误信息列表
     */
    public List<String> getErrorMsgList() {
        return Collections.unmodifiableList(this.errorMsgList);
    }

    /**
     * 添加错误信息
     * @param errorMsg 错误信息
     */
    public void addErrorMsg(String errorMsg) {
//        Assert.hasText(errorMsg, "错误信息不能为空");
        this.errorMsgList.add(errorMsg);
    }

    /**
     * 添加错误信息
     * @param format 格式化字符串
     * @param args 格式化参数
     */
    public void addErrorMsg(String format, Object... args) {
        this.addErrorMsg(String.format(format, args));
    }

    /**
     * 添加错误信息
     * @param errorMsgList 错误信息列表
     */
    public void addErrorMsg(List<String> errorMsgList) {
//        Assert.notEmpty(errorMsgList, "错误信息列表不能为空");
        this.errorMsgList.addAll(errorMsgList);
    }

    /**
     * 是否存在错误
     * @return true: 存在错误
     */
    public boolean hasError() {
        return ListUtil.any(this.getErrorMsgList());
    }

    /**
     * 生成错误信息
     * @return 错误信息
     */
    public String generateErrorMsg() {
        return String.join("; ", this.getErrorMsgList());
    }
}
