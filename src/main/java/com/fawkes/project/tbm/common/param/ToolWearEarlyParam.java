package com.fawkes.project.tbm.common.param;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version ToolWearEarlyParam 2025/6/13 11:22
 */
@Data
public class ToolWearEarlyParam implements Serializable {
    /** 磨损值 */
    private BigDecimal wearValue;
    /** 结束里程 */
    private BigDecimal endMileage;
    /** 设备编码 */
    private String section;
    /** 更换阈值 */
    private BigDecimal changeThreshold;
    /** 位置 */
    private String toolPosition;

    /** 位置id */
    private Long toolPositionManageId;

    /** 是否换刀（1-是，0-否） */
    private Boolean hasToolChanged;
}
