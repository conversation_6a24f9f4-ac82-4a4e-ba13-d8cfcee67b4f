package com.fawkes.project.tbm.common.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

@ApiModel("工序参数")
@Data
public class ProcessManagementParam {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("id")
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.section
     *
     * @mbggenerated
     */
    @ApiModelProperty("区段")
    private String section;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.ring_num
     *
     * @mbggenerated
     */
    @ApiModelProperty("环号")
    private Integer ringNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.assembly_point
     *
     * @mbggenerated
     */
    @ApiModelProperty("拼装点位")
    private BigDecimal assemblyPoint;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.tunneling_start_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("掘进开始时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp tunnelingStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.tunneling_end_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("掘进结束时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp tunnelingEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.change_steps_start_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("换步开始时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp changeStepsStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.change_steps_end_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("换步结束时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp changeStepsEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.segment_assembly_start_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("管片拼装开始时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp segmentAssemblyStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.segment_assembly_end_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("管片拼装结束时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp segmentAssemblyEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.others_job_start_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("其他工作开始时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp othersJobStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.others_job_end_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("其他工作结束时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp othersJobEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.common_maintenance_and_repair_start_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("常规维修保养开始时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp commonMaintenanceAndRepairStartTime;

    @ApiModelProperty("常规维修保养结束时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp commonMaintenanceAndRepairEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.shutdown_maintenance_and_repair_end_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("故障停机维修保养结束时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp shutdownMaintenanceAndRepairEndTime;

    @ApiModelProperty("故障停机维修保养开始时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp shutdownMaintenanceAndRepairStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.open_warehouse_change_tool_start_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("开仓换刀开始时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp openWarehouseChangeToolStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.open_warehouse_change_tool_end_time
     *
     * @mbggenerated
     */
    @ApiModelProperty("开仓换刀结束时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp openWarehouseChangeToolEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.open_warehouse_change_tool_num
     *
     * @mbggenerated
     */
    @ApiModelProperty("开仓换刀-第几仓")
    private Integer openWarehouseChangeToolNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.description_of_abnormal_situations
     *
     * @mbggenerated
     */
    @ApiModelProperty("异常情况说明")
    private String descriptionOfAbnormalSituations;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.attachment
     *
     * @mbggenerated
     */
    @ApiModelProperty("附件token")
    private String attachment;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.remark
     *
     * @mbggenerated
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.process_name
     *
     * @mbggenerated
     */
    @ApiModelProperty("工序名称")
    private String processName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.description_of_construction_situation
     *
     * @mbggenerated
     */
    @ApiModelProperty("施工情况描述 --- 参见 ConstructionSituationDescriptionParam 结构")
    private String descriptionOfConstructionSituation;

    /**
     * 数据来源（0-PC，1-APP）
     */
    private Integer dataSource;
}