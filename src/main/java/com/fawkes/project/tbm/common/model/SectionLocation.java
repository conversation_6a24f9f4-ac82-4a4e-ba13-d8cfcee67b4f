package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 标段位置表
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("section_location")
@ApiModel("标段位置表")
public class SectionLocation extends BaseModel {

    private static final long serialVersionUID = 1L;

    /**
     * 区段
     */
    @ApiModelProperty("区段")
    private String section;

    /**
     * 经度
     */
    @ApiModelProperty("经度-高德")
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度-高德")
    private String latitude;

    /**
     * wgs84经度
     */
    @ApiModelProperty("wgs84经度")
    private String longitudeWgs;

    /**
     * wgs84纬度
     */
    @ApiModelProperty("wgs84纬度")
    private String latitudeWgs;

}