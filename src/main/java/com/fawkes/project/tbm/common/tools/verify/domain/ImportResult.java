package com.fawkes.project.tbm.common.tools.verify.domain;

import com.fawkes.project.tbm.common.utils.ListUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.var;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 导入返回值
 * @param <T> 导入输入类型
 * @param <R> 导入输出返回类型
 * <AUTHOR>
 */
@Getter
@Setter
public class ImportResult<T extends IErrorMsg, R> {

	/** 合法数据列表 */
	private List<R> right = null;

	/** 错误数据列表 */
	private List<T> error = new ArrayList<>();

	/**
	 * 设置错误列表
	 * @param verifyErrorList 验证错误列表
	 */
	public void setErrorList(List<RowVerifyError<T>> verifyErrorList) {
		verifyErrorList.sort(Comparator.comparingInt(RowVerifyError::getRowNo));

		var errorList = ListUtil.map(verifyErrorList, item -> {
			T errorObject = item.getErrorObject();
			errorObject.setErrorMsg(item.generateErrorMsg());
			return errorObject;
		});
		this.setError(errorList);
	}

}
