package com.fawkes.project.tbm.common.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 风险源预警导出VO
 * <AUTHOR>
 * @date 2025-03-28
 */
@Data
@ApiModel("风险源预警导出VO")
public class RiskWarningExportVO implements Serializable {

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    @ExcelProperty(value = "*设备名称", index = 0)
    private String deviceName;

    /**
     * 风险名称
     */
    @ApiModelProperty("风险名称")
    @ExcelProperty(value = "*风险名称", index = 1)
    private String riskName;

    /**
     * 风险起始点距离
     */
    @ApiModelProperty("里程范围起始点距离")
    @ExcelProperty(value = "*里程范围起点距离(M)", index = 2)
    private BigDecimal riskStart;

    /**
     * 风险终点距离
     */
    @ApiModelProperty("里程范围终点距离")
    @ExcelProperty(value = "*里程范围终点距离(M)", index = 3)
    private BigDecimal riskEnd;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注", index = 4)
    private String remark;


}