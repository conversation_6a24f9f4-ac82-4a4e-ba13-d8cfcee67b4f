package com.fawkes.project.tbm.common.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version TbmTappingSystemXYVO 2025/6/11 18:04
 */
@Data
public class TbmTappingSystemXYVO  implements Serializable {
    /**
     * x轴-环数
     */
    private String ring;
    /**
     * y轴-最大
     */
    private BigDecimal snailRotationalSpeedMax;
    /**
     * y轴-最大
     */
    private BigDecimal snailTorchqueMax;
    /**
     * y轴-最大
     */
    private BigDecimal beltRotationalSpeedMax;
    /**
     * y轴-平均
     */
    private BigDecimal snailRotationalSpeedAverage;
    /**
     * y轴-平均
     */
    private BigDecimal snailTorchqueAverage;
    /**
     * y轴-平均
     */
    private BigDecimal beltRotationalSpeedAverage;
}
