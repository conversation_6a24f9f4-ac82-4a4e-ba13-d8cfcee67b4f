package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * TBM出渣系统VO
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel("TBM出渣系统VO")
public class TbmTappingSystemVO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 螺机转速/Double/kw
     */
    @ApiModelProperty("螺机转速/Double/kw")
    private String snailRotationalSpeed;

    /**
     * 螺机扭矩/Double/hz
     */
    @ApiModelProperty("螺机扭矩/Double/hz")
    private String snailTorchque;

    /**
     * 皮带机转速/Double
     */
    @ApiModelProperty("皮带机转速/Double")
    private String beltRotationalSpeed;

    /**
     * 螺机转速列表
     */
    @ApiModelProperty("螺机转速列表")
    private List<TbmXYVO> snailRotationalSpeedList;

    /**
     * 螺机扭矩列表
     */
    @ApiModelProperty("螺机扭矩列表")
    private List<TbmXYVO> snailTorchqueList;

    /**
     * 皮带机转速列表
     */
    @ApiModelProperty("皮带机转速列表")
    private List<TbmXYVO> beltRotationalSpeedList;

    /**
     * 告警状态，0-正常，1-异常
     */
    @ApiModelProperty("告警状态，0-正常，1-异常")
    private Integer alarmStatus;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("告警id")
    private Long alarmId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd")
    private Timestamp createTime;

    /**
     * 环数
     */
    @ApiModelProperty("环数")
    private Integer ringNum;
}