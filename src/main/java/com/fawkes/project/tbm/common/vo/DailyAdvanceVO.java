package com.fawkes.project.tbm.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Date;

/**
 * 掘进日进尺
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@ApiModel("掘进日进尺VO")
public class DailyAdvanceVO implements Serializable {

    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private String date;

    /**
     * 日进尺
     */
    @ApiModelProperty("日进尺")
    private String dailyAdvance;



}