package com.fawkes.project.tbm.common.tools.verify.config;


import com.fawkes.project.tbm.common.tools.verify.domain.ISerialNumber;
import com.fawkes.project.tbm.common.tools.verify.enums.VerifyType;

import java.util.function.Function;

/**
 * 带序列号的验证配置
 * <AUTHOR>
 */
public class SerialNumberVerifyConfig<T extends ISerialNumber>
        extends VerifyConfig<T> {

    /**
     * 添加验证配置, 返回附带序列号的错误提示
     * @param verifyType 验证类型
     * @param contextSelector 提取验证内容的函数
     * @param errorMsg 错误消息
     * @return 验证配置 用于链式调用
     */
    public SerialNumberVerifyConfig<T> addConfigBySerialNumber(VerifyType verifyType, Function<T, Object> contextSelector, String errorMsg) {
        VerifyItem<T> verifyItem = new VerifyItem<>();
        verifyItem.setVerifyType(verifyType);
        verifyItem.setContextSelector(contextSelector);
        verifyItem.setErrorMessageGenerator(item -> String.format("序号为 %s 的数据错误: %s", item.getSerialNumber(), errorMsg));
        this.verifyItemList.add(verifyItem);

        return this;
    }
}
