package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tool_position_manage")
@ApiModel("刀具位置管理")
public class ToolPositionManage extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 刀具名称id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("刀具名称id")
    private Long toolNameManageId;

    /**
     * 刀具位置
     */
    @ApiModelProperty("刀具位置")
    private String toolPosition;

    /**
     * 区段
     */
    @ApiModelProperty("区段")
    private String section;

    /**
     * 组id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("组id")
    private Long groupId;

    /**
     * 组名称
     */
    @ApiModelProperty("组名称")
    private String groupName;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;

}