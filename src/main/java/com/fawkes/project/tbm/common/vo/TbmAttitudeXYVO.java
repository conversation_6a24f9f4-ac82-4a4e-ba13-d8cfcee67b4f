package com.fawkes.project.tbm.common.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version TbmAttitudeXYVO 2025/6/12 10:27
 */
@Data
public class TbmAttitudeXYVO implements Serializable {
    /** Y轴 水平-后/最大 */
    private BigDecimal horizontalEndMax;
    /** Y轴 水平-前/最大 */
    private BigDecimal horizontalFrontMax;
    /** Y轴 垂直-后/最大 */
    private BigDecimal verticalEndMax;
    /** Y轴 垂直-前/最大 */
    private BigDecimal verticalFrontMax;
    /** Y轴 水平-后/平均 */
    private BigDecimal horizontalEndAverage;
    /** Y轴 水平-前/平均 */
    private BigDecimal horizontalFrontAverage;
    /** Y轴 垂直-后/平均 */
    private BigDecimal verticalEndAverage;
    /** Y轴 垂直-前/平均 */
    private BigDecimal verticalFrontAverage;
    /**
     * x轴-环数
     */
    private String ring;
}
