package com.fawkes.project.tbm.common.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class ProcessManagementExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table process_management
     *
     * @mbggenerated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table process_management
     *
     * @mbggenerated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table process_management
     *
     * @mbggenerated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    public ProcessManagementExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table process_management
     *
     * @mbggenerated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSectionIsNull() {
            addCriterion("section is null");
            return (Criteria) this;
        }

        public Criteria andSectionIsNotNull() {
            addCriterion("section is not null");
            return (Criteria) this;
        }

        public Criteria andSectionEqualTo(String value) {
            addCriterion("section =", value, "section");
            return (Criteria) this;
        }

        public Criteria andSectionNotEqualTo(String value) {
            addCriterion("section <>", value, "section");
            return (Criteria) this;
        }

        public Criteria andSectionGreaterThan(String value) {
            addCriterion("section >", value, "section");
            return (Criteria) this;
        }

        public Criteria andSectionGreaterThanOrEqualTo(String value) {
            addCriterion("section >=", value, "section");
            return (Criteria) this;
        }

        public Criteria andSectionLessThan(String value) {
            addCriterion("section <", value, "section");
            return (Criteria) this;
        }

        public Criteria andSectionLessThanOrEqualTo(String value) {
            addCriterion("section <=", value, "section");
            return (Criteria) this;
        }

        public Criteria andSectionLike(String value) {
            addCriterion("section like", value, "section");
            return (Criteria) this;
        }

        public Criteria andSectionNotLike(String value) {
            addCriterion("section not like", value, "section");
            return (Criteria) this;
        }

        public Criteria andSectionIn(List<String> values) {
            addCriterion("section in", values, "section");
            return (Criteria) this;
        }

        public Criteria andSectionNotIn(List<String> values) {
            addCriterion("section not in", values, "section");
            return (Criteria) this;
        }

        public Criteria andSectionBetween(String value1, String value2) {
            addCriterion("section between", value1, value2, "section");
            return (Criteria) this;
        }

        public Criteria andSectionNotBetween(String value1, String value2) {
            addCriterion("section not between", value1, value2, "section");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNull() {
            addCriterion("create_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNotNull() {
            addCriterion("create_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateNameEqualTo(String value) {
            addCriterion("create_name =", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotEqualTo(String value) {
            addCriterion("create_name <>", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThan(String value) {
            addCriterion("create_name >", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_name >=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThan(String value) {
            addCriterion("create_name <", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThanOrEqualTo(String value) {
            addCriterion("create_name <=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLike(String value) {
            addCriterion("create_name like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotLike(String value) {
            addCriterion("create_name not like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameIn(List<String> values) {
            addCriterion("create_name in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotIn(List<String> values) {
            addCriterion("create_name not in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameBetween(String value1, String value2) {
            addCriterion("create_name between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotBetween(String value1, String value2) {
            addCriterion("create_name not between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Timestamp value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Timestamp value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNull() {
            addCriterion("update_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNotNull() {
            addCriterion("update_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameEqualTo(String value) {
            addCriterion("update_name =", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotEqualTo(String value) {
            addCriterion("update_name <>", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThan(String value) {
            addCriterion("update_name >", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_name >=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThan(String value) {
            addCriterion("update_name <", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThanOrEqualTo(String value) {
            addCriterion("update_name <=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLike(String value) {
            addCriterion("update_name like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotLike(String value) {
            addCriterion("update_name not like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIn(List<String> values) {
            addCriterion("update_name in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotIn(List<String> values) {
            addCriterion("update_name not in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameBetween(String value1, String value2) {
            addCriterion("update_name between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotBetween(String value1, String value2) {
            addCriterion("update_name not between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNull() {
            addCriterion("update_date is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNotNull() {
            addCriterion("update_date is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateEqualTo(Timestamp value) {
            addCriterion("update_date =", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("update_date <>", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThan(Timestamp value) {
            addCriterion("update_date >", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("update_date >=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThan(Timestamp value) {
            addCriterion("update_date <", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("update_date <=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIn(List<Timestamp> values) {
            addCriterion("update_date in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("update_date not in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date not between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNull() {
            addCriterion("delete_flag is null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIsNotNull() {
            addCriterion("delete_flag is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagEqualTo(Integer value) {
            addCriterion("delete_flag =", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotEqualTo(Integer value) {
            addCriterion("delete_flag <>", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThan(Integer value) {
            addCriterion("delete_flag >", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("delete_flag >=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThan(Integer value) {
            addCriterion("delete_flag <", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagLessThanOrEqualTo(Integer value) {
            addCriterion("delete_flag <=", value, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagIn(List<Integer> values) {
            addCriterion("delete_flag in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotIn(List<Integer> values) {
            addCriterion("delete_flag not in", values, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagBetween(Integer value1, Integer value2) {
            addCriterion("delete_flag between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andDeleteFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("delete_flag not between", value1, value2, "deleteFlag");
            return (Criteria) this;
        }

        public Criteria andRingNumIsNull() {
            addCriterion("ring_num is null");
            return (Criteria) this;
        }

        public Criteria andRingNumIsNotNull() {
            addCriterion("ring_num is not null");
            return (Criteria) this;
        }

        public Criteria andRingNumEqualTo(Integer value) {
            addCriterion("ring_num =", value, "ringNum");
            return (Criteria) this;
        }

        public Criteria andRingNumNotEqualTo(Integer value) {
            addCriterion("ring_num <>", value, "ringNum");
            return (Criteria) this;
        }

        public Criteria andRingNumGreaterThan(Integer value) {
            addCriterion("ring_num >", value, "ringNum");
            return (Criteria) this;
        }

        public Criteria andRingNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("ring_num >=", value, "ringNum");
            return (Criteria) this;
        }

        public Criteria andRingNumLessThan(Integer value) {
            addCriterion("ring_num <", value, "ringNum");
            return (Criteria) this;
        }

        public Criteria andRingNumLessThanOrEqualTo(Integer value) {
            addCriterion("ring_num <=", value, "ringNum");
            return (Criteria) this;
        }

        public Criteria andRingNumIn(List<Integer> values) {
            addCriterion("ring_num in", values, "ringNum");
            return (Criteria) this;
        }

        public Criteria andRingNumNotIn(List<Integer> values) {
            addCriterion("ring_num not in", values, "ringNum");
            return (Criteria) this;
        }

        public Criteria andRingNumBetween(Integer value1, Integer value2) {
            addCriterion("ring_num between", value1, value2, "ringNum");
            return (Criteria) this;
        }

        public Criteria andRingNumNotBetween(Integer value1, Integer value2) {
            addCriterion("ring_num not between", value1, value2, "ringNum");
            return (Criteria) this;
        }

        public Criteria andAssemblyPointIsNull() {
            addCriterion("assembly_point is null");
            return (Criteria) this;
        }

        public Criteria andAssemblyPointIsNotNull() {
            addCriterion("assembly_point is not null");
            return (Criteria) this;
        }

        public Criteria andAssemblyPointEqualTo(Integer value) {
            addCriterion("assembly_point =", value, "assemblyPoint");
            return (Criteria) this;
        }

        public Criteria andAssemblyPointNotEqualTo(Integer value) {
            addCriterion("assembly_point <>", value, "assemblyPoint");
            return (Criteria) this;
        }

        public Criteria andAssemblyPointGreaterThan(Integer value) {
            addCriterion("assembly_point >", value, "assemblyPoint");
            return (Criteria) this;
        }

        public Criteria andAssemblyPointGreaterThanOrEqualTo(Integer value) {
            addCriterion("assembly_point >=", value, "assemblyPoint");
            return (Criteria) this;
        }

        public Criteria andAssemblyPointLessThan(Integer value) {
            addCriterion("assembly_point <", value, "assemblyPoint");
            return (Criteria) this;
        }

        public Criteria andAssemblyPointLessThanOrEqualTo(Integer value) {
            addCriterion("assembly_point <=", value, "assemblyPoint");
            return (Criteria) this;
        }

        public Criteria andAssemblyPointIn(List<Integer> values) {
            addCriterion("assembly_point in", values, "assemblyPoint");
            return (Criteria) this;
        }

        public Criteria andAssemblyPointNotIn(List<Integer> values) {
            addCriterion("assembly_point not in", values, "assemblyPoint");
            return (Criteria) this;
        }

        public Criteria andAssemblyPointBetween(Integer value1, Integer value2) {
            addCriterion("assembly_point between", value1, value2, "assemblyPoint");
            return (Criteria) this;
        }

        public Criteria andAssemblyPointNotBetween(Integer value1, Integer value2) {
            addCriterion("assembly_point not between", value1, value2, "assemblyPoint");
            return (Criteria) this;
        }

        public Criteria andTunnelingStartTimeIsNull() {
            addCriterion("tunneling_start_time is null");
            return (Criteria) this;
        }

        public Criteria andTunnelingStartTimeIsNotNull() {
            addCriterion("tunneling_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andTunnelingStartTimeEqualTo(Timestamp value) {
            addCriterion("tunneling_start_time =", value, "tunnelingStartTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingStartTimeNotEqualTo(Timestamp value) {
            addCriterion("tunneling_start_time <>", value, "tunnelingStartTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingStartTimeGreaterThan(Timestamp value) {
            addCriterion("tunneling_start_time >", value, "tunnelingStartTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingStartTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("tunneling_start_time >=", value, "tunnelingStartTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingStartTimeLessThan(Timestamp value) {
            addCriterion("tunneling_start_time <", value, "tunnelingStartTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingStartTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("tunneling_start_time <=", value, "tunnelingStartTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingStartTimeIn(List<Timestamp> values) {
            addCriterion("tunneling_start_time in", values, "tunnelingStartTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingStartTimeNotIn(List<Timestamp> values) {
            addCriterion("tunneling_start_time not in", values, "tunnelingStartTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingStartTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("tunneling_start_time between", value1, value2, "tunnelingStartTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingStartTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("tunneling_start_time not between", value1, value2, "tunnelingStartTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingEndTimeIsNull() {
            addCriterion("tunneling_end_time is null");
            return (Criteria) this;
        }

        public Criteria andTunnelingEndTimeIsNotNull() {
            addCriterion("tunneling_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andTunnelingEndTimeEqualTo(Timestamp value) {
            addCriterion("tunneling_end_time =", value, "tunnelingEndTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingEndTimeNotEqualTo(Timestamp value) {
            addCriterion("tunneling_end_time <>", value, "tunnelingEndTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingEndTimeGreaterThan(Timestamp value) {
            addCriterion("tunneling_end_time >", value, "tunnelingEndTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("tunneling_end_time >=", value, "tunnelingEndTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingEndTimeLessThan(Timestamp value) {
            addCriterion("tunneling_end_time <", value, "tunnelingEndTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("tunneling_end_time <=", value, "tunnelingEndTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingEndTimeIn(List<Timestamp> values) {
            addCriterion("tunneling_end_time in", values, "tunnelingEndTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingEndTimeNotIn(List<Timestamp> values) {
            addCriterion("tunneling_end_time not in", values, "tunnelingEndTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("tunneling_end_time between", value1, value2, "tunnelingEndTime");
            return (Criteria) this;
        }

        public Criteria andTunnelingEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("tunneling_end_time not between", value1, value2, "tunnelingEndTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsStartTimeIsNull() {
            addCriterion("change_steps_start_time is null");
            return (Criteria) this;
        }

        public Criteria andChangeStepsStartTimeIsNotNull() {
            addCriterion("change_steps_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andChangeStepsStartTimeEqualTo(Timestamp value) {
            addCriterion("change_steps_start_time =", value, "changeStepsStartTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsStartTimeNotEqualTo(Timestamp value) {
            addCriterion("change_steps_start_time <>", value, "changeStepsStartTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsStartTimeGreaterThan(Timestamp value) {
            addCriterion("change_steps_start_time >", value, "changeStepsStartTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsStartTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("change_steps_start_time >=", value, "changeStepsStartTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsStartTimeLessThan(Timestamp value) {
            addCriterion("change_steps_start_time <", value, "changeStepsStartTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsStartTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("change_steps_start_time <=", value, "changeStepsStartTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsStartTimeIn(List<Timestamp> values) {
            addCriterion("change_steps_start_time in", values, "changeStepsStartTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsStartTimeNotIn(List<Timestamp> values) {
            addCriterion("change_steps_start_time not in", values, "changeStepsStartTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsStartTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("change_steps_start_time between", value1, value2, "changeStepsStartTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsStartTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("change_steps_start_time not between", value1, value2, "changeStepsStartTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsEndTimeIsNull() {
            addCriterion("change_steps_end_time is null");
            return (Criteria) this;
        }

        public Criteria andChangeStepsEndTimeIsNotNull() {
            addCriterion("change_steps_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andChangeStepsEndTimeEqualTo(Timestamp value) {
            addCriterion("change_steps_end_time =", value, "changeStepsEndTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsEndTimeNotEqualTo(Timestamp value) {
            addCriterion("change_steps_end_time <>", value, "changeStepsEndTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsEndTimeGreaterThan(Timestamp value) {
            addCriterion("change_steps_end_time >", value, "changeStepsEndTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("change_steps_end_time >=", value, "changeStepsEndTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsEndTimeLessThan(Timestamp value) {
            addCriterion("change_steps_end_time <", value, "changeStepsEndTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("change_steps_end_time <=", value, "changeStepsEndTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsEndTimeIn(List<Timestamp> values) {
            addCriterion("change_steps_end_time in", values, "changeStepsEndTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsEndTimeNotIn(List<Timestamp> values) {
            addCriterion("change_steps_end_time not in", values, "changeStepsEndTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("change_steps_end_time between", value1, value2, "changeStepsEndTime");
            return (Criteria) this;
        }

        public Criteria andChangeStepsEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("change_steps_end_time not between", value1, value2, "changeStepsEndTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyStartTimeIsNull() {
            addCriterion("segment_assembly_start_time is null");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyStartTimeIsNotNull() {
            addCriterion("segment_assembly_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyStartTimeEqualTo(Timestamp value) {
            addCriterion("segment_assembly_start_time =", value, "segmentAssemblyStartTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyStartTimeNotEqualTo(Timestamp value) {
            addCriterion("segment_assembly_start_time <>", value, "segmentAssemblyStartTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyStartTimeGreaterThan(Timestamp value) {
            addCriterion("segment_assembly_start_time >", value, "segmentAssemblyStartTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyStartTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("segment_assembly_start_time >=", value, "segmentAssemblyStartTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyStartTimeLessThan(Timestamp value) {
            addCriterion("segment_assembly_start_time <", value, "segmentAssemblyStartTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyStartTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("segment_assembly_start_time <=", value, "segmentAssemblyStartTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyStartTimeIn(List<Timestamp> values) {
            addCriterion("segment_assembly_start_time in", values, "segmentAssemblyStartTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyStartTimeNotIn(List<Timestamp> values) {
            addCriterion("segment_assembly_start_time not in", values, "segmentAssemblyStartTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyStartTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("segment_assembly_start_time between", value1, value2, "segmentAssemblyStartTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyStartTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("segment_assembly_start_time not between", value1, value2, "segmentAssemblyStartTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyEndTimeIsNull() {
            addCriterion("segment_assembly_end_time is null");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyEndTimeIsNotNull() {
            addCriterion("segment_assembly_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyEndTimeEqualTo(Timestamp value) {
            addCriterion("segment_assembly_end_time =", value, "segmentAssemblyEndTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyEndTimeNotEqualTo(Timestamp value) {
            addCriterion("segment_assembly_end_time <>", value, "segmentAssemblyEndTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyEndTimeGreaterThan(Timestamp value) {
            addCriterion("segment_assembly_end_time >", value, "segmentAssemblyEndTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("segment_assembly_end_time >=", value, "segmentAssemblyEndTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyEndTimeLessThan(Timestamp value) {
            addCriterion("segment_assembly_end_time <", value, "segmentAssemblyEndTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("segment_assembly_end_time <=", value, "segmentAssemblyEndTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyEndTimeIn(List<Timestamp> values) {
            addCriterion("segment_assembly_end_time in", values, "segmentAssemblyEndTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyEndTimeNotIn(List<Timestamp> values) {
            addCriterion("segment_assembly_end_time not in", values, "segmentAssemblyEndTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("segment_assembly_end_time between", value1, value2, "segmentAssemblyEndTime");
            return (Criteria) this;
        }

        public Criteria andSegmentAssemblyEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("segment_assembly_end_time not between", value1, value2, "segmentAssemblyEndTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobStartTimeIsNull() {
            addCriterion("others_job_start_time is null");
            return (Criteria) this;
        }

        public Criteria andOthersJobStartTimeIsNotNull() {
            addCriterion("others_job_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andOthersJobStartTimeEqualTo(Timestamp value) {
            addCriterion("others_job_start_time =", value, "othersJobStartTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobStartTimeNotEqualTo(Timestamp value) {
            addCriterion("others_job_start_time <>", value, "othersJobStartTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobStartTimeGreaterThan(Timestamp value) {
            addCriterion("others_job_start_time >", value, "othersJobStartTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobStartTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("others_job_start_time >=", value, "othersJobStartTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobStartTimeLessThan(Timestamp value) {
            addCriterion("others_job_start_time <", value, "othersJobStartTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobStartTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("others_job_start_time <=", value, "othersJobStartTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobStartTimeIn(List<Timestamp> values) {
            addCriterion("others_job_start_time in", values, "othersJobStartTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobStartTimeNotIn(List<Timestamp> values) {
            addCriterion("others_job_start_time not in", values, "othersJobStartTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobStartTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("others_job_start_time between", value1, value2, "othersJobStartTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobStartTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("others_job_start_time not between", value1, value2, "othersJobStartTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobEndTimeIsNull() {
            addCriterion("others_job_end_time is null");
            return (Criteria) this;
        }

        public Criteria andOthersJobEndTimeIsNotNull() {
            addCriterion("others_job_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andOthersJobEndTimeEqualTo(Timestamp value) {
            addCriterion("others_job_end_time =", value, "othersJobEndTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobEndTimeNotEqualTo(Timestamp value) {
            addCriterion("others_job_end_time <>", value, "othersJobEndTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobEndTimeGreaterThan(Timestamp value) {
            addCriterion("others_job_end_time >", value, "othersJobEndTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("others_job_end_time >=", value, "othersJobEndTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobEndTimeLessThan(Timestamp value) {
            addCriterion("others_job_end_time <", value, "othersJobEndTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("others_job_end_time <=", value, "othersJobEndTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobEndTimeIn(List<Timestamp> values) {
            addCriterion("others_job_end_time in", values, "othersJobEndTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobEndTimeNotIn(List<Timestamp> values) {
            addCriterion("others_job_end_time not in", values, "othersJobEndTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("others_job_end_time between", value1, value2, "othersJobEndTime");
            return (Criteria) this;
        }

        public Criteria andOthersJobEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("others_job_end_time not between", value1, value2, "othersJobEndTime");
            return (Criteria) this;
        }

        public Criteria andCommonMaintenanceAndRepairStartTimeIsNull() {
            addCriterion("common_maintenance_and_repair_start_time is null");
            return (Criteria) this;
        }

        public Criteria andCommonMaintenanceAndRepairStartTimeIsNotNull() {
            addCriterion("common_maintenance_and_repair_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andCommonMaintenanceAndRepairStartTimeEqualTo(Timestamp value) {
            addCriterion("common_maintenance_and_repair_start_time =", value, "commonMaintenanceAndRepairStartTime");
            return (Criteria) this;
        }

        public Criteria andCommonMaintenanceAndRepairStartTimeNotEqualTo(Timestamp value) {
            addCriterion("common_maintenance_and_repair_start_time <>", value, "commonMaintenanceAndRepairStartTime");
            return (Criteria) this;
        }

        public Criteria andCommonMaintenanceAndRepairStartTimeGreaterThan(Timestamp value) {
            addCriterion("common_maintenance_and_repair_start_time >", value, "commonMaintenanceAndRepairStartTime");
            return (Criteria) this;
        }

        public Criteria andCommonMaintenanceAndRepairStartTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("common_maintenance_and_repair_start_time >=", value, "commonMaintenanceAndRepairStartTime");
            return (Criteria) this;
        }

        public Criteria andCommonMaintenanceAndRepairStartTimeLessThan(Timestamp value) {
            addCriterion("common_maintenance_and_repair_start_time <", value, "commonMaintenanceAndRepairStartTime");
            return (Criteria) this;
        }

        public Criteria andCommonMaintenanceAndRepairStartTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("common_maintenance_and_repair_start_time <=", value, "commonMaintenanceAndRepairStartTime");
            return (Criteria) this;
        }

        public Criteria andCommonMaintenanceAndRepairStartTimeIn(List<Timestamp> values) {
            addCriterion("common_maintenance_and_repair_start_time in", values, "commonMaintenanceAndRepairStartTime");
            return (Criteria) this;
        }

        public Criteria andCommonMaintenanceAndRepairStartTimeNotIn(List<Timestamp> values) {
            addCriterion("common_maintenance_and_repair_start_time not in", values, "commonMaintenanceAndRepairStartTime");
            return (Criteria) this;
        }

        public Criteria andCommonMaintenanceAndRepairStartTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("common_maintenance_and_repair_start_time between", value1, value2, "commonMaintenanceAndRepairStartTime");
            return (Criteria) this;
        }

        public Criteria andCommonMaintenanceAndRepairStartTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("common_maintenance_and_repair_start_time not between", value1, value2, "commonMaintenanceAndRepairStartTime");
            return (Criteria) this;
        }

        public Criteria andShutdownMaintenanceAndRepairEndTimeIsNull() {
            addCriterion("shutdown_maintenance_and_repair_end_time is null");
            return (Criteria) this;
        }

        public Criteria andShutdownMaintenanceAndRepairEndTimeIsNotNull() {
            addCriterion("shutdown_maintenance_and_repair_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andShutdownMaintenanceAndRepairEndTimeEqualTo(Timestamp value) {
            addCriterion("shutdown_maintenance_and_repair_end_time =", value, "shutdownMaintenanceAndRepairEndTime");
            return (Criteria) this;
        }

        public Criteria andShutdownMaintenanceAndRepairEndTimeNotEqualTo(Timestamp value) {
            addCriterion("shutdown_maintenance_and_repair_end_time <>", value, "shutdownMaintenanceAndRepairEndTime");
            return (Criteria) this;
        }

        public Criteria andShutdownMaintenanceAndRepairEndTimeGreaterThan(Timestamp value) {
            addCriterion("shutdown_maintenance_and_repair_end_time >", value, "shutdownMaintenanceAndRepairEndTime");
            return (Criteria) this;
        }

        public Criteria andShutdownMaintenanceAndRepairEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("shutdown_maintenance_and_repair_end_time >=", value, "shutdownMaintenanceAndRepairEndTime");
            return (Criteria) this;
        }

        public Criteria andShutdownMaintenanceAndRepairEndTimeLessThan(Timestamp value) {
            addCriterion("shutdown_maintenance_and_repair_end_time <", value, "shutdownMaintenanceAndRepairEndTime");
            return (Criteria) this;
        }

        public Criteria andShutdownMaintenanceAndRepairEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("shutdown_maintenance_and_repair_end_time <=", value, "shutdownMaintenanceAndRepairEndTime");
            return (Criteria) this;
        }

        public Criteria andShutdownMaintenanceAndRepairEndTimeIn(List<Timestamp> values) {
            addCriterion("shutdown_maintenance_and_repair_end_time in", values, "shutdownMaintenanceAndRepairEndTime");
            return (Criteria) this;
        }

        public Criteria andShutdownMaintenanceAndRepairEndTimeNotIn(List<Timestamp> values) {
            addCriterion("shutdown_maintenance_and_repair_end_time not in", values, "shutdownMaintenanceAndRepairEndTime");
            return (Criteria) this;
        }

        public Criteria andShutdownMaintenanceAndRepairEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("shutdown_maintenance_and_repair_end_time between", value1, value2, "shutdownMaintenanceAndRepairEndTime");
            return (Criteria) this;
        }

        public Criteria andShutdownMaintenanceAndRepairEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("shutdown_maintenance_and_repair_end_time not between", value1, value2, "shutdownMaintenanceAndRepairEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolStartTimeIsNull() {
            addCriterion("open_warehouse_change_tool_start_time is null");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolStartTimeIsNotNull() {
            addCriterion("open_warehouse_change_tool_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolStartTimeEqualTo(Timestamp value) {
            addCriterion("open_warehouse_change_tool_start_time =", value, "openWarehouseChangeToolStartTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolStartTimeNotEqualTo(Timestamp value) {
            addCriterion("open_warehouse_change_tool_start_time <>", value, "openWarehouseChangeToolStartTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolStartTimeGreaterThan(Timestamp value) {
            addCriterion("open_warehouse_change_tool_start_time >", value, "openWarehouseChangeToolStartTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolStartTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("open_warehouse_change_tool_start_time >=", value, "openWarehouseChangeToolStartTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolStartTimeLessThan(Timestamp value) {
            addCriterion("open_warehouse_change_tool_start_time <", value, "openWarehouseChangeToolStartTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolStartTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("open_warehouse_change_tool_start_time <=", value, "openWarehouseChangeToolStartTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolStartTimeIn(List<Timestamp> values) {
            addCriterion("open_warehouse_change_tool_start_time in", values, "openWarehouseChangeToolStartTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolStartTimeNotIn(List<Timestamp> values) {
            addCriterion("open_warehouse_change_tool_start_time not in", values, "openWarehouseChangeToolStartTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolStartTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("open_warehouse_change_tool_start_time between", value1, value2, "openWarehouseChangeToolStartTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolStartTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("open_warehouse_change_tool_start_time not between", value1, value2, "openWarehouseChangeToolStartTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolEndTimeIsNull() {
            addCriterion("open_warehouse_change_tool_end_time is null");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolEndTimeIsNotNull() {
            addCriterion("open_warehouse_change_tool_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolEndTimeEqualTo(Timestamp value) {
            addCriterion("open_warehouse_change_tool_end_time =", value, "openWarehouseChangeToolEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolEndTimeNotEqualTo(Timestamp value) {
            addCriterion("open_warehouse_change_tool_end_time <>", value, "openWarehouseChangeToolEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolEndTimeGreaterThan(Timestamp value) {
            addCriterion("open_warehouse_change_tool_end_time >", value, "openWarehouseChangeToolEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("open_warehouse_change_tool_end_time >=", value, "openWarehouseChangeToolEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolEndTimeLessThan(Timestamp value) {
            addCriterion("open_warehouse_change_tool_end_time <", value, "openWarehouseChangeToolEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("open_warehouse_change_tool_end_time <=", value, "openWarehouseChangeToolEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolEndTimeIn(List<Timestamp> values) {
            addCriterion("open_warehouse_change_tool_end_time in", values, "openWarehouseChangeToolEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolEndTimeNotIn(List<Timestamp> values) {
            addCriterion("open_warehouse_change_tool_end_time not in", values, "openWarehouseChangeToolEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("open_warehouse_change_tool_end_time between", value1, value2, "openWarehouseChangeToolEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("open_warehouse_change_tool_end_time not between", value1, value2, "openWarehouseChangeToolEndTime");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolNumIsNull() {
            addCriterion("open_warehouse_change_tool_num is null");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolNumIsNotNull() {
            addCriterion("open_warehouse_change_tool_num is not null");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolNumEqualTo(Integer value) {
            addCriterion("open_warehouse_change_tool_num =", value, "openWarehouseChangeToolNum");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolNumNotEqualTo(Integer value) {
            addCriterion("open_warehouse_change_tool_num <>", value, "openWarehouseChangeToolNum");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolNumGreaterThan(Integer value) {
            addCriterion("open_warehouse_change_tool_num >", value, "openWarehouseChangeToolNum");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("open_warehouse_change_tool_num >=", value, "openWarehouseChangeToolNum");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolNumLessThan(Integer value) {
            addCriterion("open_warehouse_change_tool_num <", value, "openWarehouseChangeToolNum");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolNumLessThanOrEqualTo(Integer value) {
            addCriterion("open_warehouse_change_tool_num <=", value, "openWarehouseChangeToolNum");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolNumIn(List<Integer> values) {
            addCriterion("open_warehouse_change_tool_num in", values, "openWarehouseChangeToolNum");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolNumNotIn(List<Integer> values) {
            addCriterion("open_warehouse_change_tool_num not in", values, "openWarehouseChangeToolNum");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolNumBetween(Integer value1, Integer value2) {
            addCriterion("open_warehouse_change_tool_num between", value1, value2, "openWarehouseChangeToolNum");
            return (Criteria) this;
        }

        public Criteria andOpenWarehouseChangeToolNumNotBetween(Integer value1, Integer value2) {
            addCriterion("open_warehouse_change_tool_num not between", value1, value2, "openWarehouseChangeToolNum");
            return (Criteria) this;
        }

        public Criteria andDescriptionOfAbnormalSituationsIsNull() {
            addCriterion("description_of_abnormal_situations is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionOfAbnormalSituationsIsNotNull() {
            addCriterion("description_of_abnormal_situations is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionOfAbnormalSituationsEqualTo(String value) {
            addCriterion("description_of_abnormal_situations =", value, "descriptionOfAbnormalSituations");
            return (Criteria) this;
        }

        public Criteria andDescriptionOfAbnormalSituationsNotEqualTo(String value) {
            addCriterion("description_of_abnormal_situations <>", value, "descriptionOfAbnormalSituations");
            return (Criteria) this;
        }

        public Criteria andDescriptionOfAbnormalSituationsGreaterThan(String value) {
            addCriterion("description_of_abnormal_situations >", value, "descriptionOfAbnormalSituations");
            return (Criteria) this;
        }

        public Criteria andDescriptionOfAbnormalSituationsGreaterThanOrEqualTo(String value) {
            addCriterion("description_of_abnormal_situations >=", value, "descriptionOfAbnormalSituations");
            return (Criteria) this;
        }

        public Criteria andDescriptionOfAbnormalSituationsLessThan(String value) {
            addCriterion("description_of_abnormal_situations <", value, "descriptionOfAbnormalSituations");
            return (Criteria) this;
        }

        public Criteria andDescriptionOfAbnormalSituationsLessThanOrEqualTo(String value) {
            addCriterion("description_of_abnormal_situations <=", value, "descriptionOfAbnormalSituations");
            return (Criteria) this;
        }

        public Criteria andDescriptionOfAbnormalSituationsLike(String value) {
            addCriterion("description_of_abnormal_situations like", value, "descriptionOfAbnormalSituations");
            return (Criteria) this;
        }

        public Criteria andDescriptionOfAbnormalSituationsNotLike(String value) {
            addCriterion("description_of_abnormal_situations not like", value, "descriptionOfAbnormalSituations");
            return (Criteria) this;
        }

        public Criteria andDescriptionOfAbnormalSituationsIn(List<String> values) {
            addCriterion("description_of_abnormal_situations in", values, "descriptionOfAbnormalSituations");
            return (Criteria) this;
        }

        public Criteria andDescriptionOfAbnormalSituationsNotIn(List<String> values) {
            addCriterion("description_of_abnormal_situations not in", values, "descriptionOfAbnormalSituations");
            return (Criteria) this;
        }

        public Criteria andDescriptionOfAbnormalSituationsBetween(String value1, String value2) {
            addCriterion("description_of_abnormal_situations between", value1, value2, "descriptionOfAbnormalSituations");
            return (Criteria) this;
        }

        public Criteria andDescriptionOfAbnormalSituationsNotBetween(String value1, String value2) {
            addCriterion("description_of_abnormal_situations not between", value1, value2, "descriptionOfAbnormalSituations");
            return (Criteria) this;
        }

        public Criteria andAttachmentIsNull() {
            addCriterion("attachment is null");
            return (Criteria) this;
        }

        public Criteria andAttachmentIsNotNull() {
            addCriterion("attachment is not null");
            return (Criteria) this;
        }

        public Criteria andAttachmentEqualTo(String value) {
            addCriterion("attachment =", value, "attachment");
            return (Criteria) this;
        }

        public Criteria andAttachmentNotEqualTo(String value) {
            addCriterion("attachment <>", value, "attachment");
            return (Criteria) this;
        }

        public Criteria andAttachmentGreaterThan(String value) {
            addCriterion("attachment >", value, "attachment");
            return (Criteria) this;
        }

        public Criteria andAttachmentGreaterThanOrEqualTo(String value) {
            addCriterion("attachment >=", value, "attachment");
            return (Criteria) this;
        }

        public Criteria andAttachmentLessThan(String value) {
            addCriterion("attachment <", value, "attachment");
            return (Criteria) this;
        }

        public Criteria andAttachmentLessThanOrEqualTo(String value) {
            addCriterion("attachment <=", value, "attachment");
            return (Criteria) this;
        }

        public Criteria andAttachmentLike(String value) {
            addCriterion("attachment like", value, "attachment");
            return (Criteria) this;
        }

        public Criteria andAttachmentNotLike(String value) {
            addCriterion("attachment not like", value, "attachment");
            return (Criteria) this;
        }

        public Criteria andAttachmentIn(List<String> values) {
            addCriterion("attachment in", values, "attachment");
            return (Criteria) this;
        }

        public Criteria andAttachmentNotIn(List<String> values) {
            addCriterion("attachment not in", values, "attachment");
            return (Criteria) this;
        }

        public Criteria andAttachmentBetween(String value1, String value2) {
            addCriterion("attachment between", value1, value2, "attachment");
            return (Criteria) this;
        }

        public Criteria andAttachmentNotBetween(String value1, String value2) {
            addCriterion("attachment not between", value1, value2, "attachment");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andProcessNameIsNull() {
            addCriterion("process_name is null");
            return (Criteria) this;
        }

        public Criteria andProcessNameIsNotNull() {
            addCriterion("process_name is not null");
            return (Criteria) this;
        }

        public Criteria andProcessNameEqualTo(String value) {
            addCriterion("process_name =", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameNotEqualTo(String value) {
            addCriterion("process_name <>", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameGreaterThan(String value) {
            addCriterion("process_name >", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameGreaterThanOrEqualTo(String value) {
            addCriterion("process_name >=", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameLessThan(String value) {
            addCriterion("process_name <", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameLessThanOrEqualTo(String value) {
            addCriterion("process_name <=", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameLike(String value) {
            addCriterion("process_name like", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameNotLike(String value) {
            addCriterion("process_name not like", value, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameIn(List<String> values) {
            addCriterion("process_name in", values, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameNotIn(List<String> values) {
            addCriterion("process_name not in", values, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameBetween(String value1, String value2) {
            addCriterion("process_name between", value1, value2, "processName");
            return (Criteria) this;
        }

        public Criteria andProcessNameNotBetween(String value1, String value2) {
            addCriterion("process_name not between", value1, value2, "processName");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table process_management
     *
     * @mbggenerated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table process_management
     *
     * @mbggenerated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}