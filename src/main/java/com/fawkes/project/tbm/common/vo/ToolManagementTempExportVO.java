package com.fawkes.project.tbm.common.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ToolManagementTempExportVO {

    /**
     * 区段
     */
    private String section;

    /**
     * 区段名称
     */
    private String sectionName;

    /**
     * 环号
     */
    private Integer ringNum;

    /**
     * 开始方位
     */
    private String startOrientation;

    /**
     * 开始里程
     */
    private BigDecimal startMileage;

    /**
     * 结束方位
     */
    private String endOrientation;

    /**
     * 结束里程
     */
    private BigDecimal endMileage;

    /**
     * 里程
     */
    private String mileage;

    /**
     * 开仓开始时间
     */
    private LocalDateTime openWarehouseStartTime;

    /**
     * 开仓开始时间
     */
    private String openWarehouseStartTimeDesc;

    /**
     * 开仓结束时间
     */
    private LocalDateTime openWarehouseEndTime;

    private String openWarehouseEndTimeDesc;

    /**
     * 刀具名称id
     */
    private Long toolNameManageId;

    /**
     * 刀具名称
     */
    private String toolName;

    /**
     * 刀具位置id
     */
    private Long toolPositionManageId;

    /**
     * 刀具位置
     */
    private String toolLocation;

    /**
     * 更换阈值
     */
    private BigDecimal changeThreshold;

    /**
     * 磨损值
     */
    private BigDecimal wearValue;

    /**
     * 是否换刀（true-是，false-否）
     */
    private boolean hasToolChanged;

    private String hasToolChangedValue;

    /**
     * 换刀原因
     */
    private String changeToolReason;

    /**
     * 新装刀具状态
     */
    private String newlyInstalledCuttingToolsStatus;

    /**
     * 换刀图片
     */
    private String changeToolPicture;
}