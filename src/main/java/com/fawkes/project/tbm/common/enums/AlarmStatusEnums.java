package com.fawkes.project.tbm.common.enums;

/**
 * 告警状态枚举
 *
 * <AUTHOR>
 */

public enum AlarmStatusEnums {
    /**
     * 正常
     */
    NORMAL(0, "正常"),
    /**
     * 异常
     */
    ABNORMAL(1, "异常");

    private final Integer code;
    private final String name;

    AlarmStatusEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByValue(Integer code) {
        for (AlarmStatusEnums value : AlarmStatusEnums.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
