package com.fawkes.project.tbm.common.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 表单保存参数
 * <AUTHOR>
 * @date 2019-12-02
 */
@Data
public class FormQueryParam implements Serializable{

    @ApiModelProperty("操作对象实体名")
    private String entityName;

    @ApiModelProperty("明细表对象实体名称列表")
    private List<String> detailEntityNameList;

    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @ApiModelProperty("对象ID")
    private Long id;

}
