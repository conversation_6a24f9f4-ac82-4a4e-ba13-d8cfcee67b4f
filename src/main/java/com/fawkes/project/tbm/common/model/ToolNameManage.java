package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tool_name_manage")
@ApiModel("刀具名称管理")
public class ToolNameManage extends BaseEntity implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 刀具名称
     */
    @ApiModelProperty("刀具名称")
    private String tooName;

    /**
     * 区段
     */
    @ApiModelProperty("区段")
    private String section;
}