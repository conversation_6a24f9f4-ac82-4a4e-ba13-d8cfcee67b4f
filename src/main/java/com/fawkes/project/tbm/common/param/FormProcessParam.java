package com.fawkes.project.tbm.common.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 流程发起参数
 * <AUTHOR>
 * @date 2021-04-14
 */
@Data
public class FormProcessParam implements Serializable {

    @ApiModelProperty("流程模型键值")
    private String modelKey;

    @ApiModelProperty("表单键值")
    private String formKey;

    @ApiModelProperty("表单业务数据ID")
    private String bizId;

    @ApiModelProperty("流程变量/审批人")
    private Map<String,Object> variable;

    @ApiModelProperty("提交意见/审批意见")
    private String comment;

    @ApiModelProperty("暂存标识 0（不暂存）/1（暂存）")
    private String stageFlag;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty("流程实例id")
    private String processInstanceId;
}
