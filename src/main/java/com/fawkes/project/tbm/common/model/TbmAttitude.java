package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * TBM姿态参数表(此表为原始表，数据按月分表)
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tbm_attitude")
@ApiModel("TBM姿态参数表(此表为原始表，数据按月分表)")
public class TbmAttitude implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 水平-后/Double/mm
     */
    @ApiModelProperty("水平-后/Double/mm")
    private BigDecimal horizontalEnd;

    /**
     * 水平-前/Double/mm
     */
    @ApiModelProperty("水平-前/Double/mm")
    private BigDecimal horizontalFront;

    /**
     * 垂直-后/Double/mm
     */
    @ApiModelProperty("垂直-后/Double/mm")
    private BigDecimal verticalEnd;

    /**
     * 垂直-前/Double/mm
     */
    @ApiModelProperty("垂直-前/Double/mm")
    private BigDecimal verticalFront;

    /**
     * 里程/Double/m
     */
    @ApiModelProperty("里程/Double/m")
    private BigDecimal mileage;

    /**
     * 掘进/Double/m
     */
    @ApiModelProperty("掘进/Double/m")
    private BigDecimal tunneling;

    /**
     * 滚动角/Double/mm/m
     */
    @ApiModelProperty("滚动角/Double/mm/m")
    private BigDecimal rollingAngle;

    /**
     * 俯仰角/Double/mm/m
     */
    @ApiModelProperty("俯仰角/Double/mm/m")
    private BigDecimal pitchAngle;

   /**
     * 当前环数
     */
    @ApiModelProperty("当前环数")
    private Integer ringNum;

    /**
     * 运行状态： 掘进中|已停机
     */
    @ApiModelProperty("运行状态： 掘进中|已停机")
    private String runingState;

    /**
     * 经度
     */
    @ApiModelProperty("经度")
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    private String latitude;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Timestamp updateTime;

    /**
     * 盾首坐标X
     */
    @ApiModelProperty("盾首坐标X")
    private String dishCoordinateX;

    /**
     * 盾首坐标Y
     */
    @ApiModelProperty("盾首坐标Y")
    private String dishCoordinateY;

    /**
     * 盾首坐标Z
     */
    @ApiModelProperty("盾首坐标Z")
    private String dishCoordinateZ;

    /**
     * 经度-高德
     */
    @ApiModelProperty("经度-高德")
    private String longitudeGc;

    /**
     * 纬度-高德
     */
    @ApiModelProperty("纬度-高德")
    private String latitudeGc;
}