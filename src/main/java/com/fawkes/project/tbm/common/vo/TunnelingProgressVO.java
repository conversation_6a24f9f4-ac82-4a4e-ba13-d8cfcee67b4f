package com.fawkes.project.tbm.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 掘进进度值对象VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("掘进进度值对象VO")
public class TunnelingProgressVO {

    @ApiModelProperty("总里程")
    private String totalMileage;

    @ApiModelProperty("总环数")
    private String totalRingNum;

    @ApiModelProperty("累计里程")
    private String cumulativeMileage;

    @ApiModelProperty("累计安装环数")
    private String cumulativeRingNum;

    @ApiModelProperty("累计里程占比")
    private String cumulativeMileageRatio;

    @ApiModelProperty("累计安装环数占比")
    private String cumulativeRingNumRatio;
}
