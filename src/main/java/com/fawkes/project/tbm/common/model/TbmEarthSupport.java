package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * TBM土仓压力参数表(此表为原始表，数据按月分表)
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tbm_earth_support")
@ApiModel("TBM土仓压力参数表(此表为原始表，数据按月分表)")
public class TbmEarthSupport implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 压力/Double/bar
     */
    @ApiModelProperty("压力/Double/bar")
    private BigDecimal bracePressure;

    /**
     * 环数
     */
    @ApiModelProperty("环数")
    private Integer ringNum;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Timestamp updateTime;

    /** 位置1# */
    private BigDecimal positionOne;

    /** 位置2# */
    private BigDecimal positionTwo;

    /** 位置3# */
    private BigDecimal positionThree;

    /** 位置4# */
    private BigDecimal positionFour;

    /** 位置5# */
    private BigDecimal positionFive;

    /** 位置6# */
    private BigDecimal positionSix;

    /** 位置5# */
    private BigDecimal positionSeven;

    /** 位置6# */
    private BigDecimal positionEight;

}
