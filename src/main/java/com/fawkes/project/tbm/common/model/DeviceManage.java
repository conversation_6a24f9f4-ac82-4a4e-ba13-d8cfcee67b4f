package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备管理表
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("device_manage")
@ApiModel("设备管理表")
public class DeviceManage extends BaseModel {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编码（不可修改，系统设置的编码）
     */
    @ApiModelProperty("设备编码（不可修改，系统设置的编码）")
    private String code;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String name;

    /**
     * 设备类型
     */
    @ApiModelProperty("设备类型")
    private String type;

    /**
     * 掘进模式
     */
    @ApiModelProperty("掘进模式")
    private String mode;

    /**
     * 施工单位
     */
    @ApiModelProperty("施工单位")
    private String constructionUnit;

    /**
     * 区段
     */
    @ApiModelProperty("区段")
    private String section;

    /**
     * 掘进总里程
     */
    @ApiModelProperty("掘进总里程")
    private String totalMileage;

    /**
     * 总环数
     */
    @ApiModelProperty("总环数")
    private String ringNum;

    /**
     * 环宽
     */
    @ApiModelProperty("环宽")
    private String ringWidth;

    /**
     * 更新人姓名
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新人姓名")
    private String updateByFullname;

    /**
     * 开挖直径
     */
    @ApiModelProperty("开挖直径")
    private String diameter;

    /**
     * 设备编码（可以修改）
     */
    @ApiModelProperty("设备编码（可以修改）")
    private String deviceCode;
}