package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tbm_propulsion_system_alarm")
@ApiModel("TBM推进系统-告警表(数据按月分表)")
public class TbmPropulsionSystemAlarm {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Timestamp updateTime;

    /**
     * 环数
     */
    @ApiModelProperty("环数")
    private Integer ringNum;

    /**
     * 告警标志（0：正常；1：异常）
     */
    @ApiModelProperty("告警标志（0：正常；1：异常）")
    private Integer alarmFlag;

    /**
     * 告警位置
     */
    @ApiModelProperty("告警位置")
    private String alarmLocation;
}
