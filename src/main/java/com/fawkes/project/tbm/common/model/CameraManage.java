package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 摄像头配置表
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("camera_manage")
@ApiModel("摄像头配置表")
public class CameraManage extends BaseModel {

    private static final long serialVersionUID = 1L;

    /**
     * TBM设备code
     */
    @ApiModelProperty("TBM设备code")
    private String deviceCode;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 摄像头名称
     */
    @ApiModelProperty("摄像头名称")
    private String cameraName;

    /**
     * 摄像头ip
     */
    @ApiModelProperty("摄像头ip")
    private String cameraIp;

    /**
     * 在线状态 0在线 -1不在线
     */
    @ApiModelProperty("在线状态 0在线 -1不在线")
    private String onlineStatus;

    /**
     *
     */
    @ApiModelProperty("是否是工序摄像头 0：不是 ，1：是")
    private Integer  processCamera;
}