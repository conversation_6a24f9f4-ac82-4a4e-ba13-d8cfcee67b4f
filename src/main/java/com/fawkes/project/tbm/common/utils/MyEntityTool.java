package com.fawkes.project.tbm.common.utils;
import com.fawkes.core.base.model.CurrentUser;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.core.utils.http.HttpHeaderTool;
import com.fawkes.core.utils.id.IdTool;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.reflect.FieldUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.Collection;

@Log4j2
public class MyEntityTool {
    private static final String ID ="id";
    private static final String CREATE_BY ="createBy";
    private static final String CREATE_DATE ="createDate";
    private static final String UPDATE_BY ="updateBy";
    private static final String UPDATE_DATE ="updateDate";
    private static final String DELETE_FLAG ="deleteFlag";
    private static final String PROCESS_STATE ="processState";
    private static final String CREATE_USER_NAME ="createName";
    private static final String UPDATE_USER_NAME ="updateName";

    /**
     * @param entity 要插入Db的entity集
     * @Description: 插入数据时封装 创建者 更新者 创建时间 更新时间 数据可用标志 ID
     * @return:
     * @auther: zhanghutao
     * @date: 2019-5-5 13:53
     */
    public static void insertEntity(Object entity) throws IllegalAccessException {
        if (entity != null) {
            String userName = HttpHeaderTool.getHeaderValue(HttpHeaderTool.HeaderEnum.AUTH_USERNAME);
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            FieldUtils.writeField(entity,ID, IdTool.getId(),Boolean.TRUE);
            FieldUtils.writeField(entity,CREATE_BY, userName,Boolean.TRUE);
            FieldUtils.writeField(entity,CREATE_DATE, timestamp,Boolean.TRUE);
            FieldUtils.writeField(entity,UPDATE_BY, userName,Boolean.TRUE);
            FieldUtils.writeField(entity,UPDATE_DATE, timestamp,Boolean.TRUE);
            FieldUtils.writeField(entity,DELETE_FLAG, DeleteFlagEnum.DATA_OK.getFlag(),Boolean.TRUE);
        }
    }

    /**
     * @param collection 要批量插入Db的entity集合
     * @Description: 批量插入数据时封装 创建者 更新者 创建时间 更新时间 数据可用标志 ID
     * @return:
     * @auther: zhanghutao
     * @date: 2019-5-5 13:53
     */
    public static void insertEntity(Collection<Object> collection) throws IllegalAccessException {
        if (!CollectionUtils.isEmpty(collection)) {
            for (Object entity : collection) {
                insertEntity(entity);
            }
        }
    }

    /**
     * @Description: 更新数据时封装  更新者 更新时间
     * @param: entity 要更新Db的entity
     * @return:
     * @auther: zhanghutao
     * @date: 2019-5-5 13:54
     */
    public static void updateEntity(Object entity) throws IllegalAccessException {
        if (entity != null) {
            String userName = HttpHeaderTool.getHeaderValue(HttpHeaderTool.HeaderEnum.AUTH_USERNAME);
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            FieldUtils.writeField(entity,UPDATE_BY, userName,Boolean.TRUE);
            FieldUtils.writeField(entity,UPDATE_DATE, timestamp,Boolean.TRUE);
        }
    }

    /**
     * @param collection 要批量更新Db的entity集合
     * @Description: 更新数据时封装  更新者 更新时间
     * @return:
     * @auther: zhanghutao
     * @date: 2019-5-5 13:54
     */
    public static void updateEntity(Collection<Object> collection) throws IllegalAccessException {
        if (!CollectionUtils.isEmpty(collection)) {
            for (Object entity : collection) {
                updateEntity(entity);
            }
        }
    }

    /**
     * @desc: 更新流程状态
     * @author: li_y29
     * @create: 2020-6-11
     */
    public static void updateProcessState(Object entity, String processState) throws IllegalAccessException {
        if (entity != null) {
            FieldUtils.writeField(entity, PROCESS_STATE, processState, Boolean.TRUE);
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            FieldUtils.writeField(entity,UPDATE_DATE, timestamp, Boolean.TRUE);
        }
    }

    /**
     * 插入数据时封装 创建者 更新者 创建时间 更新时间 数据可用标志 ID
     * <p>不抛出具名异常</p>
     * @param entity 要插入Db的entity
     */
    public static void insertEntityWithNameSilent(Object entity){
        try {
            if (entity != null) {
                CurrentUser currentUser = HttpHeaderTool.getCurrentUser();
                String userName = currentUser == null ? StringUtils.EMPTY : currentUser.getUserName();
                String userFullName = currentUser == null ? StringUtils.EMPTY : currentUser.getUserFullname();
                Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                org.apache.commons.lang3.reflect.FieldUtils.writeField(entity,ID, IdTool.getId(),Boolean.TRUE);
                org.apache.commons.lang3.reflect.FieldUtils.writeField(entity,CREATE_BY, userName,Boolean.TRUE);
                org.apache.commons.lang3.reflect.FieldUtils.writeField(entity,CREATE_USER_NAME, userFullName,Boolean.TRUE);
                org.apache.commons.lang3.reflect.FieldUtils.writeField(entity,CREATE_DATE, timestamp,Boolean.TRUE);
                org.apache.commons.lang3.reflect.FieldUtils.writeField(entity,UPDATE_BY, userName,Boolean.TRUE);
                org.apache.commons.lang3.reflect.FieldUtils.writeField(entity,UPDATE_USER_NAME, userFullName,Boolean.TRUE);
                org.apache.commons.lang3.reflect.FieldUtils.writeField(entity,UPDATE_DATE, timestamp,Boolean.TRUE);
                org.apache.commons.lang3.reflect.FieldUtils.writeField(entity,DELETE_FLAG, DeleteFlagEnum.DATA_OK.getFlag(),Boolean.TRUE);
            }
        } catch (IllegalAccessException e) {
            log.error("insertEntitySilent error",e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 更新数据时封装 更新者 更新时间
     * <p>不抛出具名异常</p>
     * @param entity 要插入Db的entity
     */
    public static void updateEntityWithNameSilent(Object entity){
        try {
            if (entity != null) {
                CurrentUser currentUser = HttpHeaderTool.getCurrentUser();
                String userName = currentUser == null ? StringUtils.EMPTY : currentUser.getUserName();
                String userFullName = currentUser == null ? StringUtils.EMPTY : currentUser.getUserFullname();
                Timestamp timestamp = new Timestamp(System.currentTimeMillis());
                org.apache.commons.lang3.reflect.FieldUtils.writeField(entity,UPDATE_BY, userName,Boolean.TRUE);
                org.apache.commons.lang3.reflect.FieldUtils.writeField(entity,UPDATE_USER_NAME, userFullName,Boolean.TRUE);
                org.apache.commons.lang3.reflect.FieldUtils.writeField(entity,UPDATE_DATE, timestamp,Boolean.TRUE);
            }
        } catch (IllegalAccessException e) {
            log.error("insertEntitySilent error",e);
            throw new RuntimeException(e);
        }
    }
}
