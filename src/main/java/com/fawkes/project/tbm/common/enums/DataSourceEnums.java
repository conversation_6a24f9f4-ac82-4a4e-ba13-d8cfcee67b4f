package com.fawkes.project.tbm.common.enums;

/**
 * <AUTHOR>
 * @date 2025/8/13 15:01
 * @description
 */
public enum DataSourceEnums {
    /**
     * PC
     */
    PC(0, "PC"),
    /**
     * APP
     */
    APP(1, "APP");
    private final Integer code;
    private final String name;

    DataSourceEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
