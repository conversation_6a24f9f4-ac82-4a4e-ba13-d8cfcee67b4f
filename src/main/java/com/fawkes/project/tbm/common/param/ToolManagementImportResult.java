package com.fawkes.project.tbm.common.param;

import com.fawkes.project.tbm.common.tools.verify.domain.ImportResult;
import com.fawkes.project.tbm.common.tools.verify.domain.RowVerifyError;
import com.fawkes.project.tbm.common.vo.ToolManagementAddImportVO;
import com.fawkes.project.tbm.common.vo.ToolManagementVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.Assert;

import java.util.List;

/**
 * 刀具管理导入结果
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("工序管理导入结果")
public class ToolManagementImportResult extends ImportResult<ToolManagementAddImportVO, ToolManagementVO> {

    public ToolManagementImportResult() {
        super();
    }

    /**
     * 构造函数
     *
     * @param verifyErrorList 验证错误列表
     */
    public ToolManagementImportResult(List<RowVerifyError<ToolManagementAddImportVO>> verifyErrorList) {
        super();
        Assert.notNull(verifyErrorList, "verifyErrorList must not be null");
        this.setErrorList(verifyErrorList);
    }
}
