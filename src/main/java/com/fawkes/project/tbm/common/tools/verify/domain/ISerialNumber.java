package com.fawkes.project.tbm.common.tools.verify.domain;

import com.fawkes.project.tbm.common.utils.ListUtil;

import java.util.List;

/**
 * 导出用序列号接口
 * <AUTHOR>
 */
public interface ISerialNumber {

    /**
     * 获取序列号
     * @return 序列号
     */
    Integer getSerialNumber();

    /**
     * 设置序列号
     * @param value 设置值
     */
    void setSerialNumber(Integer value);

    /**
     * 初始化序列号
     * @param list 列表
     * @param <T> 泛型
     */
    static <T extends ISerialNumber> void initSerialNumber(List<T> list) {
        ListUtil.forI(list, (index, item) -> item.setSerialNumber(index + 1));
    }
}
