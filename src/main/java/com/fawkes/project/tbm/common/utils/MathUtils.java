package com.fawkes.project.tbm.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 数学工具类
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class MathUtils {

    /**
     * 判断BigDecimal是否相等,
     * @param a 值a
     * @param b 值b
     * @return true: 两值相等, 入参为null必定返回false
     */
    public static boolean equal(BigDecimal a, BigDecimal b) {
        if(a == null || b == null) {
            return false;
        }

        return a.compareTo(b) == 0;
    }

    /**
     * 判断BigDecimal是否相等,都为空相等
     * @param a 值a
     * @param b 值b
     * @return true: 两值相等
     */
    public static boolean equalNull(BigDecimal a, BigDecimal b) {
        if(a == null && b == null) {
            return true;
        }
        if(a != null && b == null) {
            return false;
        }
        if(a == null) {
            return false;
        }

        return a.compareTo(b) == 0;
    }

    /**
     * 判断 Long 是否相等,
     * @param a 值a
     * @param b 值b
     * @return true: 两值相等, 入参为null必定返回false
     */
    public static boolean equal(Long a, Long b) {
        if(a == null && b == null) {
            return true;
        }else if(a == null || b == null){
            return false;
        }

        return a.compareTo(b) == 0;
    }

    /**
     * 判断 a < b 是否成立
     * @param a 值a
     * @param b 值b
     * @return true: a < b
     */
    public static boolean less(BigDecimal a, BigDecimal b) {
        return a.compareTo(b) < 0;
    }

    /**
     * 判断 a > b 是否成立
     * @param a 值a
     * @param b 值b
     * @return true: a > b
     */
    public static boolean greater(BigDecimal a, BigDecimal b) {

        return a.compareTo(b) > 0;
    }

    /**
     * 相加
     * @param a a
     * @param b b
     * @return 和
     */
    public static Long addLong(Long a, Long b) {
        return NullMergeUtils.nullMerge(a, 0L) + NullMergeUtils.nullMerge(b, 0L);
    }

    /**
     * 相加
     * @param a a
     * @param b b
     * @return 和
     */
    public static BigDecimal add(BigDecimal a, BigDecimal b) {
        return NullMergeUtils.nullMerge(a, BigDecimal.ZERO).add(NullMergeUtils.nullMerge(b, BigDecimal.ZERO));
    }

    /**
     * 相加, 如果入参为都是null返回null
     * @param a a
     * @param b b
     * @return 和
     */
    public static BigDecimal addIfNull(BigDecimal a, BigDecimal b) {
        if(a == null && b == null){
            return null;
        }
        if(a == null){
            return b;
        }
        if(b == null){
            return a;
        }
        return a.add(b);
    }

    /**
     * 相加
     * @param a a
     * @param b b
     * @param c c
     * @return 和
     */
    public static BigDecimal addThree(BigDecimal a, BigDecimal b,BigDecimal c) {
        return NullMergeUtils.nullMerge(a, BigDecimal.ZERO)
                .add(NullMergeUtils.nullMerge(b, BigDecimal.ZERO))
                .add(NullMergeUtils.nullMerge(c, BigDecimal.ZERO));
    }

    /**
     * 相加, 如果入参为都是null返回null
     * @param a a
     * @param b b
     * @param c c
     * @return 和
     */
    public static BigDecimal addIfNull(BigDecimal a, BigDecimal b,BigDecimal c) {
        if(a == null && b == null && c == null){
            return null;
        }
        return NullMergeUtils.nullMerge(a, BigDecimal.ZERO)
                .add(NullMergeUtils.nullMerge(b, BigDecimal.ZERO))
                .add(NullMergeUtils.nullMerge(c, BigDecimal.ZERO));
    }


    /**
     * 相减
     * @param a a
     * @param b b
     * @return 和
     */
    public static BigDecimal subtract(BigDecimal a, BigDecimal b) {
        return NullMergeUtils.nullMerge(a, BigDecimal.ZERO).subtract(NullMergeUtils.nullMerge(b, BigDecimal.ZERO));
    }

    /**
     * 相减
     * @param a a
     * @param b b
     * @return 和
     */
    public static Integer subtractInt(Integer a, Integer b) {
        return NullMergeUtils.nullMerge(a, 0) - (NullMergeUtils.nullMerge(b, 0));
    }

    /**
     * 相减
     * @param a a
     * @param b b
     * @return 和
     */
    public static BigDecimal subtractIfNull(BigDecimal a, BigDecimal b) {
        if(a == null && b == null){
            return null;
        }
        if(a == null){
            return b.negate();
        }
        if(b == null){
            return a;
        }
        return a.subtract(b);
    }

    /**
     * 相减
     * @param a a
     * @param b b
     * @param c c
     * @return 和
     */
    public static BigDecimal subtractIfNull(BigDecimal a, BigDecimal b,BigDecimal c) {
        if(a == null && b == null && c == null){
            return null;
        }
        if(a == null){
            return add(b,c).negate();
        }
        if(b == null){
            return a;
        }
        return subtract(a,add(b,c));
    }

    /**
     * 相加
     * @param a a
     * @param b b
     * @return 和
     */
    public static Integer addInteger(Integer a, Integer b) {
        return NullMergeUtils.nullMerge(a, 0) + NullMergeUtils.nullMerge(b, 0);
    }


    /**
     * 相乘
     * @param a a
     * @param b b
     * @return 和
     */
    public static BigDecimal multiply(BigDecimal a, BigDecimal b) {
        if(b == null || b.compareTo(BigDecimal.ZERO) == 0){
            return BigDecimal.ZERO;
        }
        return NullMergeUtils.nullMerge(a,BigDecimal.ZERO)
                .multiply(b);
    }

    /**
     * 相乘
     * @param a a
     * @param b b
     * @return 和
     */
    public static BigDecimal multiplyNull(BigDecimal a, BigDecimal b) {
        if(a == null || b == null || b.compareTo(BigDecimal.ZERO) == 0){
            return null;
        }
        return a.multiply(b);
    }

    /**
     * 除法
     */
    public static BigDecimal divide(BigDecimal a, BigDecimal b) {
        if(b == null || b.compareTo(BigDecimal.ZERO) == 0){
            return null;
        }
        return NullMergeUtils.nullMerge(a,BigDecimal.ZERO)
                .divide(b,2, RoundingMode.DOWN);
    }

    /**
     * 除法
     */
    public static BigDecimal divideHalfUp(BigDecimal a, BigDecimal b) {
        if(b == null || b.compareTo(BigDecimal.ZERO) == 0){
            return null;
        }
        return NullMergeUtils.nullMerge(a,BigDecimal.ZERO)
                .divide(b,2, RoundingMode.HALF_UP);
    }

    /**
     * 除法
     */
    public static BigDecimal divideThree(BigDecimal a, BigDecimal b) {
        if(b == null || b.compareTo(BigDecimal.ZERO) == 0){
            return null;
        }
        return NullMergeUtils.nullMerge(a,BigDecimal.ZERO)
                .divide(b,3, RoundingMode.HALF_UP);
    }

    /**
     * 除法
     */
    public static BigDecimal divideFour(BigDecimal a, BigDecimal b) {
        if(b == null || b.compareTo(BigDecimal.ZERO) == 0){
            return null;
        }
        return NullMergeUtils.nullMerge(a,BigDecimal.ZERO)
                .divide(b,4, RoundingMode.DOWN);
    }

    /**
     * 相加, 如果入参为都是null返回null
     * @param a a
     * @param b b
     * @return 和
     */
    public static Integer addIntIfNull(Integer a, Integer b) {
        if(a == null && b == null){
            return null;
        }
        if(a == null){
            return b;
        }
        if(b == null){
            return a;
        }
        return a+b;
    }
}
