package com.fawkes.project.tbm.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备运行参数
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@ApiModel("设备运行参数VO")
public class DeviceRuningVO implements Serializable {

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String name;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 刀盘转速
     */
    @ApiModelProperty("刀盘转速")
    private String actualRpm;

    /**
     * 刀盘扭矩
     */
    @ApiModelProperty("刀盘扭矩")
    private String cutDiskTorchque;

    /**
     * 贯入度
     */
    @ApiModelProperty("贯入度")
    private String penetration;

    /**
     * 推力
     */
    @ApiModelProperty("推力")
    private String thrust;

    /**
     * 推进速度
     */
    @ApiModelProperty("推进速度")
    private String propSpeed;

    /**
     * 经度
     */
    @ApiModelProperty("经度")
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    private String latitude;

    /**
     * 运行状态： 掘进中|已停机
     */
    @ApiModelProperty("运行状态： 掘进中|已停机")
    private String runingState;

    /**
     * 设备编码（可以修改）
     */
    @ApiModelProperty("设备编码（可以修改）")
    private String deviceCode;

    /**
     * 经度-高德
     */
    @ApiModelProperty("经度-高德")
    private String longitudeGc;

    /**
     * 纬度-高德
     */
    @ApiModelProperty("纬度-高德")
    private String latitudeGc;
}