package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/6/10 10:26
 */
@Data
@ApiModel("工序分布总对象")
public class ProcessTotalDistributionVO {

    @ApiModelProperty("所有工序最早开始时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp startTime;

    @ApiModelProperty("所有工序最晚结束时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp endTime;

    private List<ProcessDistributionVO> processDistributionVOList;
}
