package com.fawkes.project.tbm.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fawkes.project.tbm.common.model.MiddleUserPortal;
import com.fawkes.project.tbm.common.model.SysUser;
import com.fawkes.project.tbm.common.model.UserMirrorImage;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserMirrorImageMapper extends BaseMapper<UserMirrorImage> {

    @Select("select count(*) from fawkes.sys_user su where delete_flag = 0 and user_name = #{userName}")
    int selectUserByUserName(String userName);

    @Select("select count(*) from fawkes.sys_user su where delete_flag = 0 and email = #{email}")
    int selectUserByEmail(String email);

    @Select("select count(*) from user_mirror_image where delete_flag = 0 and phone = #{phone}")
    int selectUserImageByPhone(String phone);

    @Select("select portal_id from fawkes.middle_user_portal mup where user_id = #{sysId}")
    List<Long> selectPortalIdsByUserId(Long sysId);

    @Select("select role_id from fawkes.middle_role_user mru where user_id = #{sysId}")
    List<Long> selectRoleIdsByUserId(Long sysId);

    @Select("select * from fawkes.sys_user su where delete_flag = 0 and user_name = #{userName} and phone = #{phone} limit 1")
    SysUser selectSysUserId(@Param("userName") String userName, @Param("phone") String phone);

    @Insert("insert into fawkes.middle_user_portal (id, user_id, portal_id, tenant_id) values (#{id}, #{userId}, #{portalId}, #{tenantId})")
    void insertUserPortal(MiddleUserPortal middleUserPortal);

    @Delete("delete from fawkes.middle_user_portal where user_id = #{userId} and tenant_id = #{tenantId}")
    void deleteUserPortalByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);

    @Delete("delete from fawkes.middle_role_user where user_id = #{userId} and tenant_id = #{tenantId}")
    void deleteUserRoleByUserId(@Param("userId") Long userId, @Param("tenantId") Long tenantId);
}