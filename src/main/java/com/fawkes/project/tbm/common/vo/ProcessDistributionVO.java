package com.fawkes.project.tbm.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/6/4 14:06
 */
@Data
@ApiModel("工序分布")
public class ProcessDistributionVO {

    @ApiModelProperty("工序名称")
    private String processName;

    @ApiModelProperty("工序开始时间")
    private List<ProcessDistributionTimeVO> processDistributionTimeVOList;
}
