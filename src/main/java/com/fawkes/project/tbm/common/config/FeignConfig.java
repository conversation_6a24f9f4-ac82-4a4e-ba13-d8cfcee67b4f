package com.fawkes.project.tbm.common.config;

import com.fawkes.core.utils.http.HttpHeaderTool;
import com.fawkes.project.tbm.common.utils.ThreadLocalUtil;
import feign.Logger.Level;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class FeignConfig implements RequestInterceptor {


//    @Bean
//    Level feignLoggerLevel() {
//        return Level.FULL;
//    }

    @Autowired
    private Environment environment;

    private final String fawkesAuth = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRJZCI6ImZhd2tlcyIsInVzZXJfbmFtZSI6ImNoZW5ndGlhbnBpbmciLCJzZXgiOiLnlLciLCJ1c2VyTm8iOm51bGwsInVzZXJOYW1lIjoiY2hlbmd0aWFucGluZyIsImNsaWVudF9pZCI6ImZhd2tlcyIsImF2YXRhclRva2VuIjpudWxsLCJsaWNlbnNlIjoicG93ZXJlZCBieSBmYXdrZXMiLCJhdWQiOlsib2F1dGgyLXJlc291cmNlIiwib2F1dGgyLXNlcnZlciJdLCJwaG9uZSI6IjEzNzMyMjUyOTY3Iiwic2NvcGUiOlsiYWxsIl0sInRlbmFudElkIjoxMDAwMDAsInVzZXJGdWxsbmFtZSI6Iueoi-WkqeW5syIsImlkIjoiMTkwNzYwOTk4MTcwODkzMTA3MyIsInVzZXJUeXBlIjoxLCJleHAiOjE3NTE1MzAzNDksImp0aSI6IjE4NDYxN2UzLWFiM2MtNDE1NC05MTBiLTcyMmZkMTZiMjRjOSIsImVtYWlsIjoiMTY1MzQ5MzYzN0BxcS5jb20ifQ.8uH56YDV0ISmUqDNbUI6lcFUasell964vBtMMIeTNHc";
    private final String fawkesBiz = "McpKuHo4bOLjXYw+tTuyARbJZVXJNoWK7egGAlLyd29x0QowIdKWQ1WPS6OCbZWFONLyA7Qu9MpYFoRbH7m32jWQsPGMtL/OccCgaOmiNqcAKehRgMtO3azW0UuwS1FaRhiPwn27sZjN/Bm6FHUF1kz+K4WhQ1WQSkwr1xTnr6KM/LSV1OdVKJeZfuFrO9Wp7ZZJtwEpYGULYAJjdGoGeQrtOiquOrfobk3mdXCFHJsDG5i+1saHR0kxHq/6yKbY0kUfl4gQvMaiTWH21ctGmA==";

    @Override
    public void apply(RequestTemplate requestTemplate) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();

        if (attributes == null) {
            if (!ThreadLocalUtil.get().isEmpty()) {
                requestTemplate.header(ThreadLocalUtil.get(HttpHeaderTool.HeaderEnum.CURRENT_USER.getKey()));
                requestTemplate.header(ThreadLocalUtil.get(HttpHeaderTool.FAWKES_AUTH));
                requestTemplate.header(ThreadLocalUtil.get(HttpHeaderTool.HeaderEnum.TX_ID.getKey()));
                // ThreadLocalUtil.set(HttpHeaderTool.HeaderEnum.CURRENT_USER.getKey(), JsonTool.toJson(MyEntityTool.mockCurrentUser()));
                // ThreadLocalUtil.set(HttpHeaderTool.FAWKES_AUTH, fawkesAuth);
                // ThreadLocalUtil.set(HttpHeaderTool.HeaderEnum.TX_ID.getKey(), "baf20a601db2f58be846d9c3255fb7bc");
                return;
            }
            return;
        }
        HttpServletRequest request = attributes.getRequest();
        requestTemplate.header(HttpHeaderTool.HeaderEnum.CURRENT_USER.getKey(), request.getHeader(HttpHeaderTool.HeaderEnum.CURRENT_USER.getKey()));

        // requestTemplate.header(HttpHeaderTool.FAWKES_AUTH, fawkesAuth);
        // requestTemplate.header(HttpHeaderTool.FAWKES_BIZ, fawkesBiz);
        requestTemplate.header(HttpHeaderTool.FAWKES_AUTH, request.getHeader(HttpHeaderTool.FAWKES_AUTH));
        requestTemplate.header(HttpHeaderTool.FAWKES_BIZ, request.getHeader(HttpHeaderTool.FAWKES_BIZ));

//        requestTemplate.header(HttpHeaderTool.FAWKES_AUTH, fawkesAuth);
//        requestTemplate.header(HttpHeaderTool.FAWKES_BIZ, fawkesBiz);

        requestTemplate.header(HttpHeaderTool.HeaderEnum.TX_ID.getKey(), request.getHeader(HttpHeaderTool.HeaderEnum.TX_ID.getKey()));
        requestTemplate.header(HttpHeaderTool.FAWKES_BACKEND_REQ, HttpHeaderTool.FAWKES_BACKEND_REQ);
    }

    public boolean checkIsDevProfile() {
        String[] activeProfiles = environment.getActiveProfiles();
        if (activeProfiles.length > 0) {
            String currentProfile = activeProfiles[0];
            if ("dev".equals(currentProfile)) {
                log.info("当前环境为开发环境");
                return true;
            }
        }
        return false;
    }

}
