package com.fawkes.project.tbm.common.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.project.tbm.common.mapper.DeviceManageMapper;
import com.fawkes.project.tbm.common.model.DeviceManage;
import com.fawkes.project.tbm.common.vo.OcrProcessManagementVO;
import com.fawkes.project.tbm.common.vo.ProcessManagementVO;
import com.fawkes.project.tbm.common.vo.SysOssVO;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 腾讯云API 工具类
 * @Date 2025/3/20 11:16
 */
@Component
@Slf4j
public class TencentCloudApiTools {

    @Value("${tencentCloud.secretId}")
    private String secretId;

    @Value("${tencentCloud.secretKey}")
    private String secretKey;

    private OcrClient client;

    @Resource
    private TransferFileTool transferFileTool;

    @Resource
    private DeviceManageMapper deviceManageMapper;

    /**
     * 初始化IaiClient
     *
     * @return
     */
    @PostConstruct
    private void initOcrClient() {
        Credential cred = new Credential(secretId, secretKey);
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("ocr.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        client = new OcrClient(cred, "ap-shanghai", clientProfile);
    }

    /**
     * 获取ocr识别结果
     *
     * @param token
     * @return
     */
    public ProcessManagementVO getOcrResult(String token) {
        //使用凤翎文件token下载文件
        List<String> base64List = generateImageBase64(Arrays.asList(token));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(base64List)) {
            return null;
        }

        String base64 = base64List.get(0);
        TableInfo[] tableDetectionArr = recognizeTableAccurateOCR(base64);
        return new ProcessManagementVO();
    }

    /**
     * 获取ocr识别结果
     *
     * @param multipartFile
     * @return
     */
    public OcrProcessManagementVO getOcrResult(MultipartFile multipartFile) {
        try {
            byte[] bytes = multipartFile.getBytes();
            String base64 = Base64Utils.encodeToString(bytes);
            OcrProcessManagementVO ocrProcessManagementVO = generalBasicOCRRequest(base64);
            return ocrProcessManagementVO;
        } catch (IOException e) {
            log.error("getOcrResult error: {}", e);
            e.printStackTrace();
        }

        return new OcrProcessManagementVO();
    }

    /**
     * 表格识别
     *
     * @param base64
     * @return
     */
    private TableInfo[] recognizeTableAccurateOCR(String base64) {
        try {
            // 实例化一个请求对象,每个接口都会对应一个request对象
            RecognizeTableAccurateOCRRequest req = new RecognizeTableAccurateOCRRequest();
            req.setImageBase64(base64);
            // 返回的resp是一个RecognizeTableAccurateOCRResponse的实例，与请求对象对应
            RecognizeTableAccurateOCRResponse resp = client.RecognizeTableAccurateOCR(req);
            TableInfo[] tableDetectionArr = resp.getTableDetections();
            for (TableInfo tableInfo : tableDetectionArr) {
                Long type = tableInfo.getType();
                Coord[] tableCoordPoint = tableInfo.getTableCoordPoint();
                TableCellInfo[] cells = tableInfo.getCells();
            }
            return tableDetectionArr;
        } catch (TencentCloudSDKException e) {
            e.printStackTrace();
            log.error("recognizeTableAccurateOCR error: {}", e);
        }

        return null;
    }

    /**
     * 文档抽取（基础版）
     *
     * @param base64
     */
    private void smartStructuralOCRV2(String base64) {
        try {
            // 实例化一个请求对象,每个接口都会对应一个request对象
            SmartStructuralOCRV2Request req = new SmartStructuralOCRV2Request();
            req.setImageBase64(base64);
            req.setIsPdf(true);
            req.setConfigId("General");
            // 返回的resp是一个SmartStructuralOCRV2Response的实例，与请求对象对应
            SmartStructuralOCRV2Response resp = client.SmartStructuralOCRV2(req);
            GroupInfo[] structuralList = resp.getStructuralList();
            WordItem[] wordList = resp.getWordList();
            for (GroupInfo groupInfo : structuralList) {
            }
        } catch (TencentCloudSDKException e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据区段名称获取区段code
     *
     * @param section
     * @return
     */
    private String getSectionCode(String section){
        LambdaQueryWrapper<DeviceManage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceManage::getSection, section);
        queryWrapper.eq(DeviceManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag());
        List<DeviceManage> deviceManageList = deviceManageMapper.selectList(queryWrapper);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(deviceManageList)) {
            return null;
        }

        return deviceManageList.get(0).getCode();
    }

    /**
     * 通用文字识别
     *
     * @param base64
     */
    private OcrProcessManagementVO generalBasicOCRRequest(String base64) {
        OcrProcessManagementVO ocrProcessManagementVO = new OcrProcessManagementVO();
        try {
            //通用
//            GeneralBasicOCRRequest req = new GeneralBasicOCRRequest();
//            req.setImageBase64(base64);
//            req.setIsPdf(true);

            GeneralAccurateOCRRequest req = new GeneralAccurateOCRRequest();
            req.setImageBase64(base64);
            req.setIsPdf(true);

            //【荐】通用印刷体识别（高精度版）
            // 返回的resp是一个GeneralAccurateOCRResponse的实例，与请求对象对应
            GeneralAccurateOCRResponse resp = client.GeneralAccurateOCR(req);
            TextDetection[] textDetections = resp.getTextDetections();

            // 返回的resp是一个GeneralBasicOCRResponse的实例，与请求对象对应
//            GeneralBasicOCRResponse generalBasicOCRResponse = client.GeneralBasicOCR(req);
//            TextDetection[] textDetections = generalBasicOCRResponse.getTextDetections();
            int index = 0;
            //日期的格式：2025年6月9日
            String date = null;
            for (TextDetection textDetection : textDetections) {
                String detectedText = textDetection.getDetectedText();
                if (org.apache.commons.lang.StringUtils.isNotBlank(detectedText)) {
                    if (detectedText.startsWith("区段:")) {
                        //这里的区段是北段这种，通过这种查询区段code
                        String section = detectedText.substring("区段:".length());
                        //通过区段查询区段code
                        String sectionCode = getSectionCode(section);
                        ocrProcessManagementVO.setSection(sectionCode);
                    }

                    if (detectedText.startsWith("环号:")) {
                        String ringNum = detectedText.substring("环号:".length());
                        if (StringUtils.isEmpty(ringNum)) {
                            //如果环号是空的，环号可能在下个索引3里面
                            TextDetection textDetection3 = textDetections[index + 1];
                            String detectedText3 = textDetection3.getDetectedText();
                            if (!StringUtils.isEmpty(detectedText3)) {
                                if (!detectedText3.startsWith("拼装点位:")) {
                                    ocrProcessManagementVO.setRingNum(detectedText3);
                                }
                            }
                        } else {
                            ocrProcessManagementVO.setRingNum(ringNum);
                        }
                    }

                    if (detectedText.startsWith("拼装点位:")) {
                        String assemblyPoint = detectedText.substring("拼装点位:".length());
                        if (StringUtils.isEmpty(assemblyPoint)) {
                            //如果环号是空的，环号可能在下个索引5里面
                            TextDetection textDetection5 = textDetections[index + 1];
                            String detectedText5 = textDetection5.getDetectedText();
                            if (!StringUtils.isEmpty(detectedText5)) {
                                if (!detectedText5.startsWith("日期:")) {
                                    ocrProcessManagementVO.setAssemblyPoint(detectedText5);
                                }
                            }
                        } else {
                            ocrProcessManagementVO.setAssemblyPoint(assemblyPoint);
                        }
                    }

                    if (detectedText.startsWith("日期:")) {
                        date = detectedText.substring("日期:".length());
                        date = getDate(date);
                    }

                    if (org.apache.commons.lang.StringUtils.equals(detectedText, "掘进")) {
                        TextDetection textDetection13 = textDetections[index + 1];
                        //S 23:00
                        String tunnelingStartTime = textDetection13.getDetectedText();
                        ocrProcessManagementVO.setTunnelingStartTime(getDateTime(date, tunnelingStartTime));

                        TextDetection textDetection14 = textDetections[index + 2];
                        //F 23:33
                        String tunnelingEndTime = textDetection14.getDetectedText();
                        ocrProcessManagementVO.setTunnelingEndTime(getDateTime(date, tunnelingEndTime));
                    }

                    if (detectedText.startsWith("换步")) {
                        TextDetection textDetection20 = textDetections[index + 1];
                        //S 23:00
                        String changeStepsStartTime = textDetection20.getDetectedText();
                        ocrProcessManagementVO.setChangeStepsStartTime(getDateTime(date, changeStepsStartTime));

                        TextDetection textDetection21 = textDetections[index + 2];
                        //F 23:33
                        String changeStepsEndTime = textDetection21.getDetectedText();
                        ocrProcessManagementVO.setChangeStepsEndTime(getDateTime(date, changeStepsEndTime));
                    }

                    if (detectedText.startsWith("管片拼装")) {
                        TextDetection textDetection23 = textDetections[index + 1];
                        //$ 23:33
                        String segmentAssemblyStartTime = textDetection23.getDetectedText();
                        ocrProcessManagementVO.setSegmentAssemblyStartTime(getDateTime(date, segmentAssemblyStartTime));

                        TextDetection textDetection24 = textDetections[index + 2];
                        //F D:20
                        String segmentAssemblyEndTime = textDetection24.getDetectedText();
                        ocrProcessManagementVO.setSegmentAssemblyEndTime(getDateTime(date, segmentAssemblyEndTime));
                    }

                    if (detectedText.startsWith("其它工作")) {
                        TextDetection textDetection27 = textDetections[index + 1];
                        //$ 23:33
                        String othersJobStartTime = textDetection27.getDetectedText();
                        ocrProcessManagementVO.setOthersJobStartTime(getDateTime(date, othersJobStartTime));

                        TextDetection textDetection28 = textDetections[index + 2];
                        //F D:20
                        String othersJobEndTime = textDetection28.getDetectedText();
                        ocrProcessManagementVO.setOthersJobEndTime(getDateTime(date, othersJobEndTime));
                    }

                    if (detectedText.startsWith("常规维保")) {
                        TextDetection textDetection36 = textDetections[index + 1];
                        //S
                        String commonMaintenanceAndRepairStartTime = textDetection36.getDetectedText();
                        ocrProcessManagementVO.setCommonMaintenanceAndRepairStartTime(getDateTime(date, commonMaintenanceAndRepairStartTime));

                        TextDetection textDetection37 = textDetections[index + 2];
                        //F
                        String commonMaintenanceAndRepairEndTime = textDetection37.getDetectedText();
                        ocrProcessManagementVO.setCommonMaintenanceAndRepairEndTime(getDateTime(date, commonMaintenanceAndRepairEndTime));
                    }

                    if (detectedText.startsWith("故障停机")) {
                        TextDetection textDetection41 = textDetections[index + 1];
                        //S
                        String shutdownMaintenanceAndRepairStartTime = textDetection41.getDetectedText();
                        ocrProcessManagementVO.setShutdownMaintenanceAndRepairStartTime(getDateTime(date, shutdownMaintenanceAndRepairStartTime));

                        TextDetection textDetection42 = textDetections[index + 2];
                        //F
                        String shutdownMaintenanceAndRepairEndTime = textDetection42.getDetectedText();
                        ocrProcessManagementVO.setShutdownMaintenanceAndRepairEndTime(getDateTime(date, shutdownMaintenanceAndRepairEndTime));
                    }

                    if (detectedText.startsWith("开仓换刀")) {
                        TextDetection textDetection31 = textDetections[index + 1];
                        //第()仓
                        String openWarehouseChangeToolNum = textDetection31.getDetectedText();
                        ocrProcessManagementVO.setOpenWarehouseChangeToolNum(getOpenWarehouseChangeToolNum(openWarehouseChangeToolNum));

                        TextDetection textDetection32 = textDetections[index + 2];
                        //S
                        String openWarehouseChangeToolStartTime = textDetection32.getDetectedText();
                        ocrProcessManagementVO.setOpenWarehouseChangeToolStartTime((getDateTime(date, openWarehouseChangeToolStartTime)));

                        TextDetection textDetection33 = textDetections[index + 3];
                        //F
                        String openWarehouseChangeToolEndTime = textDetection33.getDetectedText();
                        ocrProcessManagementVO.setOpenWarehouseChangeToolEndTime(getDateTime(date, openWarehouseChangeToolEndTime));
                    }

                    if (detectedText.startsWith("异常情况说明(位置,描述):")) {
                        TextDetection textDetection134 = textDetections[index + 2];
                        String descriptionOfAbnormalSituations = textDetection134.getDetectedText();
                        ocrProcessManagementVO.setDescriptionOfAbnormalSituations(descriptionOfAbnormalSituations);
                    }

                    if (detectedText.startsWith("1.渣土性状:")) {
                        String constructionWasteSoilProperties = detectedText.substring("1.渣土性状:".length());
                        ocrProcessManagementVO.setConstructionWasteSoilProperties(constructionWasteSoilProperties);
                    }

                    if (detectedText.startsWith("2.出渣量:")) {
                        String residueDischargeVolume = detectedText.substring("2.出渣量:".length());
                        ocrProcessManagementVO.setResidueDischargeVolume(residueDischargeVolume);
                    }

                    if (detectedText.startsWith("3.管片坡度:")) {
                        String slopeOfTheSegment = detectedText.substring("3.管片坡度:".length());
                        ocrProcessManagementVO.setSlopeOfTheSegment(slopeOfTheSegment);
                    }

                    if (detectedText.startsWith("4.泡沫剂使用:")) {
                        String useOfFoamingAgent = detectedText.substring("4.泡沫剂使用:".length());
                        ocrProcessManagementVO.setUseOfFoamingAgent(useOfFoamingAgent);
                    }

                    if (detectedText.startsWith("5.油脂使用:")) {
                        String greaseUsage = detectedText.substring("5.油脂使用:".length());
                        ocrProcessManagementVO.setGreaseUsage(greaseUsage);
                    }

                    if (detectedText.startsWith("6.膨润土:")) {
                        String bentonite = detectedText.substring("6.膨润土:".length());
                        ocrProcessManagementVO.setBentonite(bentonite);
                    }

                    if (detectedText.startsWith("7.其他")) {
                        String others = detectedText.substring("7.其他".length());
                        ocrProcessManagementVO.setOthers(others);
                    }
                    index++;
                }
            }
        } catch (TencentCloudSDKException e) {
            log.error("generalBasicOCRRequest error: {}", e);
            e.printStackTrace();
        }

        return ocrProcessManagementVO;
    }

    /**
     * 第几仓数据处理
     *
     * @param openWarehouseChangeToolNum 第()仓
     * @return
     */
    private String getOpenWarehouseChangeToolNum(String openWarehouseChangeToolNum){
        if(StringUtils.isEmpty(openWarehouseChangeToolNum)){
           return null;
        }

        try {
            String replace = openWarehouseChangeToolNum.replace("第(", "").replace(")仓", "");
            return replace;
        }catch (Exception e){
            log.error("getOpenWarehouseChangeToolNum error: {}", e);
            return null;
        }
    }

    /**
     * 日期时间转换
     *
     * @param date
     * @param time
     * @return
     */
    private String getDateTime(String date, String time){
        if(StringUtils.isEmpty(date) || StringUtils.isEmpty(time)){
            return null;
        }

        if(time.contains("S") || time.contains("F") || time.contains("s") || time.contains("f") || time.contains("$")){
            try {
                String S_ = time.replace("S", "");
                String s_ = S_.replace("s", "");
                String F_ = s_.replace("F", "");
                String f_ = F_.replace("f", "");
                String $_ = f_.replace("$", "");
                String D_ = $_ .replace("D", "0");
                String blank_ = D_ .replace(" ", "");
                if(StringUtils.isEmpty(blank_)){
                    return null;
                }

                //补零操作
                String[] dateArr = date.split("-");
                String newDate = "";
                for (String dateStr : dateArr){
                    Integer integer = Integer.valueOf(dateStr);
                    if(integer < 10){
                        dateStr = "0" + dateStr;
                        newDate = newDate + dateStr + "-";
                    }else {
                        newDate = newDate + dateStr + "-";
                    }
                }

                newDate = newDate.substring(0, newDate.lastIndexOf("-"));
                return newDate + " " + blank_;
            }catch (Exception e){
                log.error("getDateTime error: {}", e);
                return null;
            }
        }

        return null;
    }

    /**
     * 日期格式转换
     *
     * @param date 2025年6月9日
     * @return     2025-6-9
     */
    private String getDate(String date){
        if(org.apache.commons.lang.StringUtils.isEmpty(date)){
            return null;
        }

        String replaceYear = date.replace("年", "-");
        String replaceMonth = replaceYear.replace("月", "-");
        String replaceDay = replaceMonth.replace("日", "");
        return replaceDay;
    }

    /**
     * 批量获取base64 数据
     *
     * @param tokenList
     * @return
     */
    private List<String> generateImageBase64(List<String> tokenList) {
        List<String> base64List = new ArrayList<>();
        if (!org.springframework.util.StringUtils.isEmpty(tokenList)) {
            List<SysOssVO> fileInfoByTokenList = transferFileTool.getFileInfoByToken(tokenList, true);
            if (!CollectionUtils.isEmpty(fileInfoByTokenList)) {
                for (SysOssVO sysOssVO : fileInfoByTokenList) {
                    String fileName = sysOssVO.getFileName();
                    if (!org.springframework.util.StringUtils.isEmpty(fileName)) {
                        ByteArrayResource resource = sysOssVO.getResource();
                        if (resource != null) {
                            String base64 = getBase64(resource);
                            base64List.add(base64);
                        }
                    }
                }
            }
        }
        return base64List;
    }

    /**
     * 获取base64数据
     *
     * @param resource
     * @return
     */
    private String getBase64(ByteArrayResource resource) {
        InputStream inputStream = null;
        try {
            inputStream = resource.getInputStream();

            byte[] bytes = inputStreamToByteArray(inputStream);
            String base64 = Base64Utils.encodeToString(bytes);
            return base64;
        } catch (Exception e) {
            log.error("function=getBase64, error:{}", e);
        } finally {
            {
                if (inputStream != null) {
                    try {
                        inputStream.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                        log.error("function=getBase64, inputStream.close() error:{}", e);
                    }
                }
            }
        }

        return null;
    }

    /**
     * 获取InputStream数据的字节数组
     *
     * @param inputStream
     * @return
     * @throws IOException
     */
    private byte[] inputStreamToByteArray(InputStream inputStream) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[4096];
        int length;
        while ((length = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, length);
        }
        outputStream.flush();
        return outputStream.toByteArray();
    }
}
