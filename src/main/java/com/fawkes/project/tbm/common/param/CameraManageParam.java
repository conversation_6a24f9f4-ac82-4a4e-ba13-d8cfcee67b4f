package com.fawkes.project.tbm.common.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 摄像头配置表
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel("摄像头配置参数")
public class CameraManageParam implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备code
     */

    @NotBlank(message = "设备编码不能为空")
    @ApiModelProperty("设备code")
    private String deviceCode;

    /**
     * 设备名称
     */
    @NotBlank(message = "设备名称不能为空")
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 摄像头名称
     */

    @NotBlank(message = "摄像头名称不能为空")
    @Size(min = 1, max = 50, message = "摄像头名称长度不能超过50")
    @ApiModelProperty("摄像头名称")
    private String cameraName;

    /**
     * 摄像头ip
     */
    @NotBlank(message = "摄像头ip不能为空")
    @Size(min = 1, max = 200, message = "摄像头ip长度不能超过200")
    @ApiModelProperty("摄像头ip")
    private String cameraIp;

    @ApiModelProperty("是否是工序摄像头 0：不是 ，1：是")
    private Integer  processCamera;
}