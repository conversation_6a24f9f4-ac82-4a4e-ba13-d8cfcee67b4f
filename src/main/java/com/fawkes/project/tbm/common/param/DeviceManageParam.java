package com.fawkes.project.tbm.common.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 设备管理表
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@ApiModel("设备管理参数")
public class DeviceManageParam implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    @NotBlank(message = "设备编码不能为空")
    @Size(max = 50, message = "设备编码长度不能超过50个字符")
    private String code;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    @NotBlank(message = "设备名称不能为空")
    @Size(max = 50, message = "设备名称长度不能超过50个字符")
    private String name;

    /**
     * 设备类型
     */
    @NotBlank(message = "设备名称不能为空")
    @ApiModelProperty("设备类型")
    private String type;

    /**
     * 掘进模式
     */
    @NotBlank(message = "掘进模式不能为空")
    @ApiModelProperty("掘进模式")
    private String mode;

    /**
     * 施工单位
     */
    @NotBlank(message = "施工单位不能为空")
    @Size(max = 50, message = "施工单位长度不能超过50个字符")
    @ApiModelProperty("施工单位")
    private String constructionUnit;

    /**
     * 区段
     */
    @NotBlank(message = "区段不能为空")
    @ApiModelProperty("区段")
    private String section;

    /**
     * 掘进总里程
     */
    @NotBlank(message = "掘进总里程不能为空")
    @ApiModelProperty("掘进总里程")
    private String totalMileage;

    /**
     * 总环数
     */
    @NotBlank(message = "总环数不能为空")
    @Pattern(regexp = "^[0-9]*$",message = "总环数输入正整数")
    @Size(max = 4, message = "总环数长度不能超过4位数")
    @ApiModelProperty("总环数")
    private String ringNum;

    /**
     * 环宽
     */
    @NotBlank(message = "环宽不能为空")
//    @Pattern(regexp = "^[0-9]*$",message = "环宽输入正整数")
//    @Size(max = 4, message = "环宽长度不能超过4位数")
    @ApiModelProperty("环宽")
    private String ringWidth;

    /**
     * 开挖直径
     */
    @NotBlank(message = "开挖直径不能为空")
    @ApiModelProperty("开挖直径")
    private String diameter;

    /**
     * 设备编码（可以修改）
     */
    @ApiModelProperty("设备编码（可以修改）")
    @NotBlank(message = "设备编码不能为空")
    @Size(max = 50, message = "设备编码长度不能超过50个字符")
    private String deviceCode;
}