package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * TBM土仓压力VO
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel("TBM土仓压力VO")
public class TbmEarthSupportVO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * x轴-环数
     */
    @ApiModelProperty("x轴-环数")
    private String ring;

    /**
     * y轴-最大值
     */
    @ApiModelProperty("y轴-最大值")
    private BigDecimal max;

    /**
     * y轴-评价值
     */
    @ApiModelProperty("y轴-评价值")
    private BigDecimal average;
}