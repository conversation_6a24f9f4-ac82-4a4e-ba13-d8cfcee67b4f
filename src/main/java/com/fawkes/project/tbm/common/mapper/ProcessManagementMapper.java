package com.fawkes.project.tbm.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fawkes.project.tbm.common.model.ProcessManagement;
import com.fawkes.project.tbm.common.model.ProcessManagementExample;
import java.util.List;

import com.fawkes.project.tbm.common.vo.ProcessDistributionVO;
import com.fawkes.project.tbm.common.vo.ProcessManagementRingNumVO;
import com.fawkes.project.tbm.common.vo.ProcessManagementVO;
import com.fawkes.project.tbm.common.vo.RingAbnormalSituationsDescriptionVO;
import org.apache.ibatis.annotations.Param;

public interface ProcessManagementMapper extends BaseMapper<ProcessManagement> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    int countByExample(ProcessManagementExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    int deleteByExample(ProcessManagementExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    int insert(ProcessManagement record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    int insertSelective(ProcessManagement record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    List<ProcessManagement> selectByExampleWithBLOBs(ProcessManagementExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    List<ProcessManagement> selectByExample(ProcessManagementExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    int updateByExampleSelective(@Param("record") ProcessManagement record, @Param("example") ProcessManagementExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    int updateByExampleWithBLOBs(@Param("record") ProcessManagement record, @Param("example") ProcessManagementExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table process_management
     *
     * @mbggenerated
     */
    int updateByExample(@Param("record") ProcessManagement record, @Param("example") ProcessManagementExample example);

    List<ProcessManagementRingNumVO> listProcessManagementRingNum(@Param("section")String section);

    List<RingAbnormalSituationsDescriptionVO> listAllRingAbnormalSituationsDescription(@Param("section")String section);

    List<ProcessManagementVO> listProcessManagementGroupByRingNum(@Param("section")String section);
}