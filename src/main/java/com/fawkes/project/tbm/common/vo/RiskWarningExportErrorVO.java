package com.fawkes.project.tbm.common.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fawkes.project.tbm.common.tools.verify.config.VerifyConfig;
import com.fawkes.project.tbm.common.tools.verify.domain.IErrorMsg;
import com.fawkes.project.tbm.common.tools.verify.domain.ISerialNumber;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 风险源预警导出错误数据VO
 * <AUTHOR>
 * @date 2025-03-28
 */
@Data
@ApiModel("风险源预警导出错误数据VO")
public class RiskWarningExportErrorVO implements ISerialNumber, IErrorMsg {

    /**
     * 序号
     */
    private Integer serialNumber;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    @ExcelProperty(value = "*设备名称", index = 0)
    private String deviceName;

    /**
     * 风险名称
     */
    @ApiModelProperty("风险名称")
    @ExcelProperty(value = "*风险名称", index = 1)
    private String riskName;

    /**
     * 风险起始点距离
     */
    @ApiModelProperty("里程范围起始点距离")
    @ExcelProperty(value = "*里程范围起点距离(M)", index = 2)
    private String riskStart;

    /**
     * 风险终点距离
     */
    @ApiModelProperty("里程范围终点距离")
    @ExcelProperty(value = "*里程范围终点距离(M)", index = 3)
    private String riskEnd;


    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注", index = 4)
    private String remark;

    /**
     * 错误消息
     */
    private String errorMsg;

    /**
     * TBM设备code
     */
    @ApiModelProperty("TBM设备code")
    @ExcelIgnore
    private String deviceCode;

    /**
     * 获取校验配置
     *
     * @return 校验配置
     */
    public static VerifyConfig<RiskWarningExportErrorVO> getVerifyConfig() {
        return new VerifyConfig<RiskWarningExportErrorVO>();
    }
}