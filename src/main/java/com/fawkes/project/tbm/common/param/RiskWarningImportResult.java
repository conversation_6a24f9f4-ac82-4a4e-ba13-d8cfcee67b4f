package com.fawkes.project.tbm.common.param;

import com.fawkes.project.tbm.common.tools.verify.domain.ImportResult;
import com.fawkes.project.tbm.common.tools.verify.domain.RowVerifyError;
import com.fawkes.project.tbm.common.vo.ProcessManagementAddImportVO;
import com.fawkes.project.tbm.common.vo.ProcessManagementVO;
import com.fawkes.project.tbm.common.vo.RiskWarningExportErrorVO;
import com.fawkes.project.tbm.common.vo.RiskWarningVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.Assert;

import java.util.List;

/**
 * 风险源预警导入结果
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("风险源预警导入结果")
public class RiskWarningImportResult
        extends ImportResult<RiskWarningExportErrorVO, RiskWarningVO>
{

    public RiskWarningImportResult() {
        super();
    }

    /**
     * 构造函数
     * @param verifyErrorList 验证错误列表
     */
    public RiskWarningImportResult(List<RowVerifyError<RiskWarningExportErrorVO>> verifyErrorList) {
        super();
        Assert.notNull(verifyErrorList, "verifyErrorList must not be null");
        this.setErrorList(verifyErrorList);
    }
}
