package com.fawkes.project.tbm.common.enums;

/**
 * 盾构机运行状态枚举
 *
 * <AUTHOR>
 */
public enum RunningStateEnums {
    /**
     * 已停机
     */
    SHUT_DOWN("0", "已停机"),
    /**
     * 掘进中
     */
    TUNNELING_IN_PROGRESS("1", "掘进中");

    private final String code;
    private final String name;

    RunningStateEnums(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByValue(String code) {
        for (RunningStateEnums value : RunningStateEnums.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
