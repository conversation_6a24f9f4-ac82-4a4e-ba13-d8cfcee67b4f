package com.fawkes.project.tbm.common.utils;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.regex.Pattern;

/**
 * 时间工具类
 * <AUTHOR>
 */
@Log4j2
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    public final static String YYYY_MM_DD = "yyyy-MM-dd";

    public final static String YYYY_MM = "yyyy-MM";

    public final static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static final String[] PARSE_PATTERNS = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM",
            "yyyy年MM月", "yyyyMMdd","yyyy/M/dd H:mm:ss","yyyy/M/dd HH:mm:ss"};

    /**
     * 日期匹配正则表达式
     */
    private static final Pattern datePattern = Pattern.compile("^(?<year>\\d{4})年?[.\\-/](?<month>\\d{1,2})月?([.\\-/](?<day>\\d{1,2}))?日?$");


    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor) {
        if (temporalAccessor == null) {
            return null;
        }
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor) {
        if (temporalAccessor == null) {
            return null;
        }

        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * date => LocalDateTime
     *
     * @param date date
     * @return LocalDateTime
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * 格式化LocalDateTime
     *
     * @param date    日期
     * @param pattern 格式化字符串
     * @return 格式化结果
     */
    public static String format(LocalDateTime date, String pattern) {
        if (date == null || StringUtils.isBlank(pattern)) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern, Locale.SIMPLIFIED_CHINESE);
        return formatter.format(date);
    }

    /**
     * 格式化LocalDate
     *
     * @param date    日期
     * @param pattern 格式化字符串
     * @return 格式化结果
     */
    public static String format(LocalDate date, String pattern) {
        if (date == null || StringUtils.isBlank(pattern)) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern, Locale.SIMPLIFIED_CHINESE);
        return formatter.format(date);
    }

    /**
     * Date
     *
     * @param date    日期
     * @param pattern 格式化字符串
     * @return 格式化结果
     */
    public static String format(final Date date, final String pattern) {
        return new SimpleDateFormat(pattern).format(date);
    }

    /**
     * 格式化星期
     *
     * @param dayOfWeek 星期
     * @return 格式化结果
     */
    public static String format(DayOfWeek dayOfWeek) {
        switch (dayOfWeek) {
            case MONDAY:
                return "周一";
            case TUESDAY:
                return "周二";
            case WEDNESDAY:
                return "周三";
            case THURSDAY:
                return "周四";
            case FRIDAY:
                return "周五";
            case SATURDAY:
                return "周六";
            case SUNDAY:
                return "周日";
            default:
                throw new UnsupportedOperationException(String.format("枚举: %s对应操作未实现", dayOfWeek));
        }
    }

    /**
     * 将 Date 转换为 LocalDate
     *
     * @param date Date
     * @return LocalDate
     */
    public static LocalDate toLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return LocalDate.of(date.getYear() + 1900, date.getMonth() + 1, date.getDate());
    }

    /**
     * 将字符串转换为 LocalDateTime
     * <p>格式: yyyy-MM-dd HH:mm:ss</p>
     *
     * @param dateStr 时间字符串
     * @return LocalDateTime实例
     */
    public static LocalDateTime toLocalDateTime(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
    }

    /**
     * 将字符串转换为 LocalDate
     * <p>格式: yyyy-MM-dd</p>
     *
     * @param dateStr 时间字符串
     * @return LocalDateTime实例
     */
    public static LocalDate toLocalDate(String dateStr) {
        Assert.hasText(dateStr, "时间字符串不能为空");
        return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(YYYY_MM_DD));
    }

    /**
     * 将字符串转换为 LocalDate
     * <p>格式: yyyy-MM</p>
     *
     * @param dateStr 时间字符串
     * @return LocalDateTime实例
     */
    public static LocalDate toLocalDateMonth(String dateStr) {
        Assert.hasText(dateStr, "时间字符串不能为空");
        return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(YYYY_MM_DD));
    }

    /**
     * string转LocalDate
     *
     * @param dateStr
     * @param formatter
     * @return
     */
    public static LocalDate format(String dateStr, String formatter) {
        if (StringUtils.isBlank(dateStr) || StringUtils.isBlank(formatter)) {
            return null;
        }
        // Excel数值格式日期特殊处理
        if (StringUtils.isNumeric(dateStr)) {
            return LocalDate.of(1900, 1, 1).plusDays(Integer.parseInt(dateStr));
        }

        DateTimeFormatter formatterDate = DateTimeFormatter.ofPattern(formatter);
        try {
            return LocalDate.parse(dateStr, formatterDate);
        } catch (DateTimeParseException parseException) {
            log.warn("日期格式转换异常, dateStr: {}, formatter: {}", dateStr, formatter);
            return null;
        }
    }

    /**
     * 获取输入当天第一秒的时间
     *
     * @param localDateTime 日期
     * @return 当天第一秒的时间
     */
    public static LocalDateTime getDayFirstTime(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return LocalDateTime.of(localDateTime.toLocalDate(), LocalTime.MIN);
    }

    /**
     * 获取输入当天最后一秒的时间
     *
     * @param localDateTime 日期
     * @return 当天最后一秒的时间
     */
    public static LocalDateTime getDayLastTime(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return LocalDateTime.of(localDateTime.toLocalDate(), LocalTime.MAX);
    }

    /**
     * 获取输入当天第一秒的时间
     *
     * @param localDate 日期
     * @return 当天第一秒的时间
     */
    public static LocalDateTime getDayFirstTime(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return LocalDateTime.of(localDate, LocalTime.MIN);
    }

    /**
     * 获取输入当天最后一秒的时间
     *
     * @param localDate 日期
     * @return 当天最后一秒的时间
     */
    public static LocalDateTime getDayLastTime(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        // 取LocalTime.MAX的纳秒值 -> 999999999 时将导致MySQL日期查询判断错误, 会查出下一天
        return LocalDateTime.of(localDate, LocalTime.MAX).withNano(0);
    }

    /**
     * 获取当月最后一秒
     *
     * @param localDate 时间
     * @return
     */
    public static LocalDateTime getMonthLastTime(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        LocalDate date = localDate.with(TemporalAdjusters.lastDayOfMonth());
        return LocalDateTime.of(date, LocalTime.MAX).withNano(0);
    }

    /**
     * 获取当月第一秒
     *
     * @param localDate 时间
     * @return
     */
    public static LocalDateTime getMonthFirstTime(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        LocalDate date = localDate.withDayOfMonth(1);
        return LocalDateTime.of(date, LocalTime.MIN).withNano(0);
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            String string = str.toString();
            string = string.replaceAll("\r\n|\r|\n", "");
            return parseDate(string.trim(), PARSE_PATTERNS);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取指定小时的秒数
     *
     * @param time 小时
     * @return
     */
    public static long halfDaySeconds(Long time) {
        // 半天定义为12小时，每小时60分钟，每分钟60秒，所以总秒数为12 * 3600
        return time * 3600;
    }

    /**
     * 获取两个日期之间的所有日期
     * @param startStr
     * @param endStr
     * @param pattern
     * @return
     * @throws Exception
     */
    public static List<String> getDateList(String startStr, String endStr, String pattern) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        Date startDate = sdf.parse(startStr);
        Date endDate = sdf.parse(endStr);

        List<String> dateList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        while (!calendar.getTime().after(endDate)) { // 包含结束日期
            dateList.add(sdf.format(calendar.getTime()));
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        return dateList;
    }

    /**
     * 获取两个日期之间的所有日期
     * @param startStr
     * @param endStr
     * @param pattern
     * @return
     * @throws Exception
     */
    public static List<String> getMonthDateList(String startStr, String endStr, String pattern) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        Date startDate = sdf.parse(startStr);
        Date endDate = sdf.parse(endStr);

        List<String> dateList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        while (!calendar.getTime().after(endDate)) { // 包含结束日期
            dateList.add(sdf.format(calendar.getTime()));
            calendar.add(Calendar.MONTH, 1);
        }
        return dateList;
    }

    /**
     * 获取前7天的日期
     *
     * @return
     */
    public static List<String> getBeforeSevenDays() {
        List<String> dates = new ArrayList<>();
        LocalDate startDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate old = startDate.minusDays(6);
        for (int i = 0; i < 7; i++) {
            LocalDate date = old.plusDays(i);
            String dateString = date.format(formatter);
            dates.add(dateString);
        }
        return dates;
    }

    /**
     * 当月份+前6个月
     *
     * @param startMonth 开始时间
     * @return 返回当月份+前6个月
     */
    public static List<String> getBeforeSixMonth(String startMonth) {
        LocalDate startMonthLocalDate = toLocalDateMonth(startMonth);
        List<String> months = new ArrayList<>();
        LocalDate startDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        LocalDate old = startDate.minusMonths(6);
        for (int i = 0; i < 7; i++){
            LocalDate date = old.plusMonths(i);
            if (date.isAfter(startMonthLocalDate)){
                String monthString = date.format(formatter);
                months.add(monthString);
            }
        }

        return months;
    }

    /**
     * 获取Date格式最小日期1970-01-01
     * @return 日期
     */
    public static Date getMinDateTime() {
        Date date = new Date(0);
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        String formattedDate = sdf.format(date);
        return parseDate(formattedDate);
    }

    /**
     * 获取日期9999-12-31 23:59:59
     * @return 日期
     */
    public static Date getMaxDateTime() {
        // 创建一个 Calendar 实例，并设置为 9999-12-31 23:59:59
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, 9999);
        calendar.set(Calendar.MONTH, Calendar.DECEMBER);
        calendar.set(Calendar.DAY_OF_MONTH, 31);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);

        return calendar.getTime();
    }

    /**
     * 获取当前年月
     *
     * @return
     */
    public static String getYyyyMM(){
        LocalDateTime now = LocalDateTime.now();
        String year = String.valueOf(now.getYear());
        int month = now.getMonthValue();
        String monthStr = String.valueOf(month);
        if(month < 10){
            monthStr = "0" + month;
        }
        return year + monthStr;
    }

    /**
     * 返回 yy/MM 的格式
     *
     * @param str 2025/07
     * @return
     */
    public static String getYyMm(String str) {
        if (StringUtils.isEmpty(str)) {
            return null;
        }
        return str.substring(2, 4) + "/" + str.split("/")[1];
    }
}
