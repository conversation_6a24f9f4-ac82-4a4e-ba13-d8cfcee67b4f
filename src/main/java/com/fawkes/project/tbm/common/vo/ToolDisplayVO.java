package com.fawkes.project.tbm.common.vo;

import com.fawkes.project.tbm.common.param.ToolInfoParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version ToolDisplayVO 2025/6/13 10:09
 */
@Data
@ApiModel("刀具全局展示VO")
public class ToolDisplayVO implements Serializable {

    /**
     * 换刀总数
     */
    @ApiModelProperty("换刀总数")
    private String changeToolNum;

    /**
     * 换刀总时长
     */
    @ApiModelProperty("换刀总时长")
    private String changeToolTime;

    /**
     * 位置信息
     */
    private List<ToolInfoParam> toolInfo;

}
