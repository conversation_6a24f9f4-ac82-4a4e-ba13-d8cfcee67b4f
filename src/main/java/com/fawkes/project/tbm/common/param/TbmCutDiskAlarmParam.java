package com.fawkes.project.tbm.common.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TbmCutDiskAlarmParam {

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 环数
     */
    @ApiModelProperty("环数")
    private Integer ringNum;

    /**
     * 告警标志（0：正常；1：异常）
     */
    @ApiModelProperty("告警标志（0：正常；1：异常）")
    private Integer alarmFlag;

    /**
     * 告警位置
     */
    @ApiModelProperty("告警位置")
    private String alarmLocation;
}
