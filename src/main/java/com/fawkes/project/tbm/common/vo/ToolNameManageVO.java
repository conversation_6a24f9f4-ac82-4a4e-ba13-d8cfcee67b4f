package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 刀具名称vo
 */
@Data
public class ToolNameManageVO {


    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 刀具名称
     */
    @ApiModelProperty("刀具名称")
    private String tooName;

    /**
     * 区段
     */
    @ApiModelProperty("区段")
    private String section;
}
