<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fawkes.project.tbm.common.mapper.ToolPositionManageMapper" >

    <select id="listGroupVO" resultType="com.fawkes.project.tbm.common.vo.ToolPositionManageGroupVO">
        SELECT group_id   AS groupId,
               group_name AS groupName,
               section
        FROM tool_position_manage
        where delete_flag = 0
          and section = #{section}
        GROUP BY group_id, group_name, section
        ORDER BY group_id;
    </select>
</mapper>