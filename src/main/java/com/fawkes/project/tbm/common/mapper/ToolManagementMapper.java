package com.fawkes.project.tbm.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fawkes.project.tbm.common.model.ToolManagement;
import com.fawkes.project.tbm.common.param.ToolInfoParam;
import com.fawkes.project.tbm.common.param.ToolWearEarlyParam;
import com.fawkes.project.tbm.common.vo.ToolDisplayVO;
import com.fawkes.project.tbm.common.vo.ToolWearCompareVO;
import com.fawkes.project.tbm.common.vo.ToolWearEarlyWarningVO;
import com.fawkes.project.tbm.common.vo.ToolWearRuleVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ToolManagementMapper extends BaseMapper<ToolManagement> {

    /**
     * 获取换刀总数和总时长
     * @param section 设备编码
     * @return
     */
    ToolDisplayVO toolDisplay(@Param("section") String section);

    /**
     * 根据位置分组查询换刀次数
     * @param section 设备编码
     * @return
     */
    List<ToolInfoParam> getPositionNum(@Param("section") String section);

    /**
     * 根据位置分组查询最新的刀具信息
     * @param section 设备编码
     * @return
     */
    List<ToolInfoParam> getToolInfo(@Param("section") String section);

    /**
     * 刀具磨损预警
     */
    List<ToolWearEarlyParam> toolWearEarlyWarning(@Param("section") String section);

    /**
     * 查询累计磨损量
     */
    List<ToolWearEarlyWarningVO> getCumulativeWearAndTear(@Param("section") String section,
                                                          @Param("positionIdList") List<Long> positionIdList);
    /**
     * 根据区域code,分组id,查询最新的的两次换刀数据
     * @param section 区域code
     * @param groupId 分组id
     * @return 刀具磨损比较
     */
    List<ToolWearCompareVO> selectToolWearCompareData(@Param("section") String section, @Param("groupId") Long groupId);


    /**
     * 查询所有的刀具 最近一次磨损值,+最近一次的结束里程
     * @param section 区域code
     * @param groupId 分组id
     * @return 刀具磨损规则
     */
    List<ToolWearRuleVO> toolWearRule(@Param("section") String section, @Param("groupId") Long groupId);


    /**
     * 查询有数据的刀具位置id
     * @param section 区域
     * @return 刀具位置id
     */
    List<Long> findToolPositionManageIdList(@Param("section") String section);
}
