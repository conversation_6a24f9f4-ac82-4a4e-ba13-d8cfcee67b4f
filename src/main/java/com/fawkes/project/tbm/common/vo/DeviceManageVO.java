package com.fawkes.project.tbm.common.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 设备管理
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@ApiModel("设备管理VO")
public class DeviceManageVO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String name;

    /**
     * 设备类型
     */
    @ApiModelProperty("设备类型")
    private String type;

    /**
     * 掘进模式
     */
    @ApiModelProperty("掘进模式")
    private String mode;

    /**
     * 施工单位
     */
    @ApiModelProperty("施工单位")
    private String constructionUnit;

    /**
     * 区段
     */
    @ApiModelProperty("区段")
    private String section;

    /**
     * 掘进总里程
     */
    @ApiModelProperty("掘进总里程")
    private String totalMileage;

    /**
     * 总环数
     */
    @ApiModelProperty("总环数")
    private String ringNum;

    /**
     * 环宽
     */
    @ApiModelProperty("环宽")
    private String ringWidth;

    @ApiModelProperty("操作人")
    private String updateByFullname;

    @ApiModelProperty("操作时间")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private Timestamp updateDate;

    /**
     * 开挖直径
     */
    @ApiModelProperty("开挖直径")
    private String diameter;

    /**
     * 设备编码（可以修改）
     */
    @ApiModelProperty("设备编码（可以修改）")
    private String deviceCode;
}