package com.fawkes.project.tbm.common.query;

import com.fawkes.core.pojo.query.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 风险源预警
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@ApiModel("风险源预警查询参数")
public class RiskWarningQuery extends PageParam {


    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 风险名称
     */
    @ApiModelProperty("风险名称")
    private String riskName;

    /**
     * 风险距离
     */
    @ApiModelProperty("风险距离开始")
    private String riskDistanceStart;

    /**
     * 风险距离
     */
    @ApiModelProperty("风险距离结束")
    private String riskDistanceEnd;

    /** IDList */
    @ApiModelProperty("IDList")
    private List<Long> idList;



}