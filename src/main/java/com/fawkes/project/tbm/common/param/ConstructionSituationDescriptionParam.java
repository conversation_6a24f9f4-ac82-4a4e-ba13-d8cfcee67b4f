package com.fawkes.project.tbm.common.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 施工情况描述参数
 * @Date 2025/6/9 9:37
 */
@Data
@ApiModel("施工情况描述参数")
public class ConstructionSituationDescriptionParam {

    @ApiModelProperty("渣土性状")
    private String constructionWasteSoilProperties;

    @ApiModelProperty("出渣量")
    private String residueDischargeVolume;

    @ApiModelProperty("管片坡度")
    private String slopeOfTheSegment;

    @ApiModelProperty("泡沫剂使用")
    private String useOfFoamingAgent;

    @ApiModelProperty("油脂使用")
    private String greaseUsage;

    @ApiModelProperty("膨润土")
    private String bentonite;

    @ApiModelProperty("其他")
    private String others;
}
