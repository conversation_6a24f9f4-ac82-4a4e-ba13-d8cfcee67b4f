package com.fawkes.project.tbm.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工序code、名称枚举
 */
@Getter
@AllArgsConstructor
public enum ProcessCodeNameEnums {

    /**
     * 掘进
     */
    TUNNELING("tunneling", "掘进"),
    /**
     * 换步
     */
    CHANGE_STEPS("change_steps", "换步"),
    /**
     * 管片拼装
     */
    SEGMENT_ASSEMBLY("segment_assembly", "管片拼装"),
    /**
     * 开仓换刀
     */
    OPEN_WAREHOUSE_CHANGE_TOOL("open_warehouse_change_tool", "开仓换刀"),

    /**
     * 常规保养
     */
    COMMON_MAINTENANCE_AND_REPAIR("common_maintenance_and_repair", "常规保养"),

    /**
     * 故障停机
     */
    SHUTDOWN_MAINTENANCE_AND_REPAIR("shutdown_maintenance_and_repair", "故障停机"),

    /**
     * 其他工作
     */
    OTHERS_JOB("others_job", "其他工作");

    @EnumValue
    private final String code;

    private final String name;

    public static String getNameByValue(Integer code) {
        for (ProcessCodeNameEnums codeNameEnums : ProcessCodeNameEnums.values()) {
            if (codeNameEnums.getCode().equals(code)) {
                return codeNameEnums.getName();
            }
        }
        return null;
    }
}
