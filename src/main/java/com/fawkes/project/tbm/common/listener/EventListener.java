//package com.fawkes.project.tbm.common.listener;
//
//import com.fawkes.core.constants.QueueConstants;
//import com.fawkes.core.utils.StringPool;
//import com.fawkes.core.utils.jackson.JsonTool;
//import com.fawkes.project.tbm.common.constants.FormConstants;
//import com.fawkes.stream.msg.send.bpm.BpmEventMsg;
//import com.rabbitmq.client.Channel;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.amqp.core.Queue;
//import org.springframework.amqp.rabbit.annotation.RabbitListener;
//import org.springframework.amqp.rabbit.connection.Connection;
//import org.springframework.amqp.rabbit.connection.ConnectionFactory;
//import org.springframework.boot.context.event.ApplicationStartedEvent;
//import org.springframework.context.ApplicationEvent;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.event.SmartApplicationListener;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
///**
// * 流程全局事件状态监听
// *
// * <AUTHOR>
// * @date 2021-12-2
// */
//@Slf4j
//@Component
//public class EventListener implements SmartApplicationListener {
//
//    @Resource
//    private ConnectionFactory connectionFactory;
//
//    @Bean
//    public Queue eventQueue() {
//        return new Queue(BPM_EVENT_QUEUE);
//    }
//
//    /**
//     * 创建服务自己的队列
//     */
//    private static final String BPM_EVENT_QUEUE = QueueConstants.SysBpmQueue.SYS_BPM_EVENT_QUEUE + StringPool.DASH + FormConstants.APPLICATION_NAME;
//
//    @RabbitListener(queues = BPM_EVENT_QUEUE)
//    private void listener(byte[] bytes) {
//        try {
//            BpmEventMsg eventMsg = JsonTool.parse(bytes, BpmEventMsg.class);
//            // TODO 监听实现 判断保证是自己流程的监听
//            if (eventMsg.getProcessInstanceId().equals("processInstanceId") && eventMsg.getFormKey().equals("formKey")) {
//                // 监听业务逻辑实现
//            }
//        } catch (Exception e) {
//            log.error("流程全局事件监听处理失败:", e);
//        }
//    }
//
//    @Override
//    public boolean supportsEventType(Class<? extends ApplicationEvent> aClass) {
//        return aClass == ApplicationStartedEvent.class;
//    }
//
//    @Override
//    public void onApplicationEvent(ApplicationEvent applicationEvent) {
//        Connection connection = connectionFactory.createConnection();
//        Channel channel = connection.createChannel(false);
//        try {
//            log.info("流程全局事件监听队列绑定交换机");
//            //实例化一个 持久化 非独占 空闲不删除 无其余参数的队列
//            channel.queueDeclare(BPM_EVENT_QUEUE, true, false, false, null);
//            //绑定队列和交换机
//            channel.queueBind(BPM_EVENT_QUEUE, QueueConstants.SysBpmQueue.SYS_BPM_EVENT_EXCHANGE, StringPool.EMPTY);
//        } catch (Exception e) {
//            log.error(e.getMessage());
//        }
//    }
//}
