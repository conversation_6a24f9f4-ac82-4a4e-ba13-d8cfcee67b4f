package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 有害气体设备管理VO
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel("有害气体设备管理VO")
public class HarmfulGasDeviceVO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String name;

    /**
     * TBM设备id
     */
    @ApiModelProperty("TBM设备code")
    private String deviceCode;

    /**
     * TBM设备名称
     */
    @ApiModelProperty("TBM设备名称")
    private String deviceName;

    /**
     * 甲烷
     */
    @ApiModelProperty("甲烷")
    private BigDecimal ch4Level;

    /**
     * 二氧化碳
     */
    @ApiModelProperty("二氧化碳")
    private BigDecimal h2sLevel;

    /**
     * 氧气
     */
    @ApiModelProperty("氧气")
    private BigDecimal o2Level;

    /**
     * 一氧化碳
     */
    @ApiModelProperty("一氧化碳")
    private BigDecimal coLevel;

    @ApiModelProperty("操作人")
    private String updateByFullname;

    @ApiModelProperty("操作时间")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private Timestamp updateDate;

}