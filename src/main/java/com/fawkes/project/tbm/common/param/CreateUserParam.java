package com.fawkes.project.tbm.common.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CreateUserParam {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private String sex;

    private String userName;

    private String password;

    private String email;

    private Integer tenantId;

    private String phone;

    private String userFullname;

    private Integer accountPeriod;

    private Integer accountStatus;

    private Date lastActiveTime;

    private Boolean isInitPwd;

    private Integer ext1;
}
