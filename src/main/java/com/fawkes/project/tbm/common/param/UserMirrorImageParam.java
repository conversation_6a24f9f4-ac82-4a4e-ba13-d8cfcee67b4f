package com.fawkes.project.tbm.common.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class UserMirrorImageParam {

    private Long id;

    @ApiModelProperty("用户名")
    @NotBlank(
            message = "用户名不能为空"
    )
    private String userName;

    @ApiModelProperty("姓名")
    @NotBlank(
            message = "姓名不能为空"
    )
    private String userFullname;

    @ApiModelProperty("性别")
    @NotBlank(
            message = "性别不能为空"
    )
    private String sex;

    @ApiModelProperty("真实手机号码")
    @NotBlank(
            message = "手机号码不能为空"
    )
    private String phone;

    @ApiModelProperty("邮箱")
    @NotBlank(
            message = "邮箱不能为空"
    )
    private String email;

    @ApiModelProperty("账号周期 1长期 2临时")
    private String accountPeriod;

    @ApiModelProperty("账号状态：1、激活；2、休眠；3、注销；默认传值 1")
    private Integer accountStatus;

    @ApiModelProperty("所属功能管理id,多个以逗号分隔")
    private String functionalIds;

    @ApiModelProperty("角色id")
    private String roleIds;

    @ApiModelProperty("所属标段")
    private String subProjectIds;

    @ApiModelProperty("门户id")
    private String portalIds;
}