package com.fawkes.project.tbm.common.param;

import com.fawkes.project.tbm.common.utils.excel.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version TestDataImportParam 2025/6/10 16:00
 */
@Data
public class TestDataImportParam implements Serializable {

    /** ID **/
    @Excel(name = "ID")
    private String item1;
    /** 日期 **/
    @Excel(name = "日期")
    private Date item2;
    /** 环号 **/
    @Excel(name = "环号")
    private String item3;
    /** 刀盘转速 **/
    @Excel(name = "刀盘转速")
    private String item4;
    /** 刀盘扭矩 **/
    @Excel(name = "刀盘扭矩")
    private String item5;
    /** 贯入度 **/
    @Excel(name = "贯入度")
    private String item6;
    /** 推进速度 **/
    @Excel(name = "推进速度")
    private String item7;
    /** 总推进力 **/
    @Excel(name = "总推进力")
    private String item8;
    /** 1#推进压力 **/
    @Excel(name = "1#推进压力")
    private String item9;
    /** 2#推进压力 **/
    @Excel(name = "2#推进压力")
    private String item10;
    /** 3#推进压力 **/
    @Excel(name = "3#推进压力")
    private String item11;
    /** 4#推进压力 **/
    @Excel(name = "4#推进压力")
    private String item12;
    /** 总功率 **/
    @Excel(name = "总功率")
    private String item13;
    /** 1#电机电流 **/
    @Excel(name = "1#电机电流")
    private String item14;
    /** 2#电机电流 **/
    @Excel(name = "2#电机电流")
    private String item15;
    /** 1#电机温度 **/
    @Excel(name = "1#电机温度")
    private String item16;
    /** 2#电机温度 **/
    @Excel(name = "2#电机温度")
    private String item17;
    /** 螺机转速 **/
    @Excel(name = "螺机转速")
    private String item18;
    /** 螺机扭矩 **/
    @Excel(name = "螺机扭矩")
    private String item19;
    /** 皮带机带速 **/
    @Excel(name = "皮带机带速")
    private String item20;
    /** 滚动角 **/
    @Excel(name = "滚动角")
    private String item21;
    /** 俯仰角 **/
    @Excel(name = "俯仰角")
    private String item22;
    /** 前点水平偏差 **/
    @Excel(name = "前点水平偏差")
    private String item23;
    /** 前点垂直偏差 **/
    @Excel(name = "前点垂直偏差")
    private String item24;
    /** 后点水平偏差 **/
    @Excel(name = "后点水平偏差")
    private String item25;
    /** 后点垂直偏差 **/
    @Excel(name = "后点垂直偏差")
    private String item26;
    /** 1#土仓压力 **/
    @Excel(name = "1#土仓压力")
    private String item27;
    /** 2#土仓压力 **/
    @Excel(name = "2#土仓压力")
    private String item28;
    /** 3#土仓压力 **/
    @Excel(name = "3#土仓压力")
    private String item29;
    /** 4#土仓压力 **/
    @Excel(name = "4#土仓压力")
    private String item30;
    /** 5#土仓压力 **/
    @Excel(name = "5#土仓压力")
    private String item31;
}
