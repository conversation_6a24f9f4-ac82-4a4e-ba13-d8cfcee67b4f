package com.fawkes.project.tbm.common.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.fawkes.project.tbm.common.handler.MonthTableNameHandler;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * Mybatis Plus 配置器
 *
 * <AUTHOR>
 */
@Configuration
@MapperScan("com.fawkes.project.tbm.common.mapper")
public class MyBatisPlusConfig {

    /**
     * 刀盘系统告警表
     */
    public static final String TBM_CUT_DISK_ALARM = "tbm_cut_disk_alarm";

    /**
     * 推进系统告警表
     */
    public static final String TBM_PROPULSION_SYSTEM_ALARM = "tbm_propulsion_system_alarm";

    /**
     * 驱动系统告警表
     */
    public static final String TBM_DRIVE_SYSTEM_ALARM = "tbm_drive_system_alarm";

    /**
     * 土仓压力告警表
     */
    public static final String TBM_EARTH_SUPPORT_ALARM = "tbm_earth_support_alarm";

    /**
     * 出渣系统告警表
     */
    public static final String TBM_TAPPING_SYSTEM_ALARM = "tbm_tapping_system_alarm";

    /**
     * 姿态系统告警表
     */
    public static final String TBM_ATTITUDE_ALARM = "tbm_attitude_alarm";

    /**
     * 支撑系统告警表
     */
    public static final String TBM_SUPPORT_SYSTEM_ALARM = "tbm_support_system_alarm";

    /**
     * 刀盘系统告警表
     */
    public static final String TBM_CUT_DISK_ALARM_DETAILS = "tbm_cut_disk_alarm_details";

    /**
     * 推进系统告警表
     */
    public static final String TBM_PROPULSION_SYSTEM_ALARM_DETAILS = "tbm_propulsion_system_alarm_details";

    /**
     * 驱动系统告警表
     */
    public static final String TBM_DRIVE_SYSTEM_ALARM_DETAILS = "tbm_drive_system_alarm_details";

    /**
     * 土仓压力告警表
     */
    public static final String TBM_EARTH_SUPPORT_ALARM_DETAILS = "tbm_earth_support_alarm_details";

    /**
     * 出渣系统告警表
     */
    public static final String TBM_TAPPING_SYSTEM_ALARM_DETAILS = "tbm_tapping_system_alarm_details";

    /**
     * 姿态系统告警表
     */
    public static final String TBM_ATTITUDE_ALARM_DETAILS = "tbm_attitude_alarm_details";

    /**
     * 支撑系统告警表
     */
    public static final String TBM_SUPPORT_SYSTEM_ALARM_DETAILS = "tbm_support_system_alarm_details";

    /**
     * 刀盘系统告警表
     */
    public static final String TBM_CUT_DISK = "tbm_cut_disk";

    /**
     * 推进系统告警表
     */
    public static final String TBM_PROPULSION_SYSTEM = "tbm_propulsion_system";

    /**
     * 驱动系统告警表
     */
    public static final String TBM_DRIVE_SYSTEM = "tbm_drive_system";

    /**
     * 土仓压力告警表
     */
    public static final String TBM_EARTH_SUPPORT = "tbm_earth_support";

    /**
     * 出渣系统告警表
     */
    public static final String TBM_TAPPING_SYSTEM = "tbm_tapping_system";

    /**
     * 姿态系统告警表
     */
    public static final String TBM_ATTITUDE = "tbm_attitude";

    /**
     * 支撑系统告警表
     */
    public static final String TBM_SUPPORT_SYSTEM = "tbm_support_system";

    /**
     * 有害气气体表
     */
    public static final String TBM_HARMFUL_GAS = "tbm_harmful_gas";

    /**
     * 需要按月分表的原始表名（总表）
     */
    public static final List<String> MONTH_TABLE_NAME_TOTAL_LIST = Arrays.asList(
            TBM_CUT_DISK_ALARM,
            TBM_PROPULSION_SYSTEM_ALARM,
            TBM_DRIVE_SYSTEM_ALARM,
            TBM_EARTH_SUPPORT_ALARM,
            TBM_TAPPING_SYSTEM_ALARM,
            TBM_ATTITUDE_ALARM,
            TBM_SUPPORT_SYSTEM_ALARM,
            TBM_CUT_DISK_ALARM_DETAILS,
            TBM_PROPULSION_SYSTEM_ALARM_DETAILS,
            TBM_DRIVE_SYSTEM_ALARM_DETAILS,
            TBM_EARTH_SUPPORT_ALARM_DETAILS,
            TBM_TAPPING_SYSTEM_ALARM_DETAILS,
            TBM_ATTITUDE_ALARM_DETAILS,
            TBM_SUPPORT_SYSTEM_ALARM_DETAILS,
            TBM_CUT_DISK,
            TBM_PROPULSION_SYSTEM,
            TBM_DRIVE_SYSTEM,
            TBM_EARTH_SUPPORT,
            TBM_TAPPING_SYSTEM,
            TBM_ATTITUDE,
            TBM_SUPPORT_SYSTEM,
            TBM_HARMFUL_GAS
    );

    /**
     * 需要按月分表的原始表名（告警表）
     */
    public static final List<String> MONTH_TABLE_ALARM_NAME_LIST = Arrays.asList(
            TBM_CUT_DISK_ALARM,
            TBM_PROPULSION_SYSTEM_ALARM,
            TBM_DRIVE_SYSTEM_ALARM,
            TBM_EARTH_SUPPORT_ALARM,
            TBM_TAPPING_SYSTEM_ALARM,
            TBM_ATTITUDE_ALARM,
            TBM_SUPPORT_SYSTEM_ALARM
    );

    /**
     * 需要按月分表的原始表名（告警表-详情）
     */
    public static final List<String> MONTH_TABLE_ALARM_DETAILS_NAME_LIST = Arrays.asList(
            TBM_CUT_DISK_ALARM_DETAILS,
            TBM_PROPULSION_SYSTEM_ALARM_DETAILS,
            TBM_DRIVE_SYSTEM_ALARM_DETAILS,
            TBM_EARTH_SUPPORT_ALARM_DETAILS,
            TBM_TAPPING_SYSTEM_ALARM_DETAILS,
            TBM_ATTITUDE_ALARM_DETAILS,
            TBM_SUPPORT_SYSTEM_ALARM_DETAILS
    );

    /**
     * 需要按月生成的表（设备相关的表）
     */
    public static final List<String> MONTH_TABLE_NAME_LIST = Arrays.asList(
            TBM_CUT_DISK,
            TBM_PROPULSION_SYSTEM,
            TBM_DRIVE_SYSTEM,
            TBM_EARTH_SUPPORT,
            TBM_TAPPING_SYSTEM,
            TBM_ATTITUDE,
            TBM_SUPPORT_SYSTEM,
            TBM_HARMFUL_GAS
    );

    @Bean
    @Primary
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        DynamicTableNameInnerInterceptor dynamicTableNameMonthInterceptor = new DynamicTableNameInnerInterceptor();
        dynamicTableNameMonthInterceptor.setTableNameHandlerMap(getTableNameHandlerMap());
        // 以拦截器的方式处理表名称
        interceptor.addInnerInterceptor(dynamicTableNameMonthInterceptor);
        return interceptor;
    }

    /**
     * 构造表名称处理map
     *
     * @return
     */
    private HashMap<String, TableNameHandler> getTableNameHandlerMap() {
        HashMap<String, TableNameHandler> map = new HashMap<>(MONTH_TABLE_NAME_TOTAL_LIST.size());
        for (String tableName : MONTH_TABLE_NAME_TOTAL_LIST) {
            map.put(tableName, new MonthTableNameHandler(tableName));
        }
        return map;
    }
}
