package com.fawkes.project.tbm.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("刀具磨损比较对象VO")
public class ToolWearCompareVO {


    /**
     * 刀具位置
     */
    @ApiModelProperty("刀具位置")
    private String toolLocation;

    /**
     * 本次磨损量
     */
    @ApiModelProperty("本次磨损量")
    private String thisTimeWearValue;

    /**
     * 上次磨损量
     */
    @ApiModelProperty("上次磨损量")
    private String lastTimeWearValue;
}
