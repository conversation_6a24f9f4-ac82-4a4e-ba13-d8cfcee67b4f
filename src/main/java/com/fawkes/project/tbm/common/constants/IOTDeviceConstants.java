package com.fawkes.project.tbm.common.constants;

import lombok.Data;

/**
 * IOT 设备常量
 */
@Data
public class IOTDeviceConstants {

    //----------------------------------------------------- 通用参数 ---------------------------------------------------------------

    /**
     * 环数
     */
    public static final String RING_NUM = "ring_num";

    /**
     * 统计日期
     */
    public static final String STATISTICS_DATE = "statistics_date";

    /**
     * 里程
     */
    public static final String MILEAGE = "mileage";

    /**
     * 推进状态
     */
    public static final String RUNING_STATE = "runing_state";

    /**
     * 拼装状态
     */
    public static final String ASSEMBLY_MODE = "assembly_mode";


    //----------------------------------------------------- 刀盘系统参数 ---------------------------------------------------------------
    /**
     * 刀盘扭矩
     */
    public static final String CUT_DISK_TORCHQUE = "cut_disk_torchque";

    /**
     * 刀盘转速
     */
    public static final String ACTUAL_RPM = "actual_rpm";

    /**
     * 贯入度
     */
    public static final String PENETRATION = "penetration";

    //----------------------------------------------------- 推进系统参数 ---------------------------------------------------------------
    /**
     * 推力
     */
    public static final String THRUST = "thrust";

    /**
     * 推进速度
     */
    public static final String PROP_SPEED = "prop_speed";

    /**
     * 油缸压力（推进油缸压力 就一个）
     */
    public static final String CYLINDER_PRESSURE = "cylinder_pressure";

    /**
     * 油缸压力1#
     */
    public static final String CYLINDER_PRESSURE_ONE = "cylinder_pressure_one";

    /**
     * 油缸压力2#
     */
    public static final String CYLINDER_PRESSURE_TWO = "cylinder_pressure_two";

    /**
     * 油缸压力3#
     */
    public static final String CYLINDER_PRESSURE_THREE = "cylinder_pressure_three";

    /**
     * 油缸压力4#
     */
    public static final String CYLINDER_PRESSURE_FOUR = "cylinder_pressure_four";

    /**
     * 油缸压力5#
     */
    public static final String CYLINDER_PRESSURE_FIVE = "cylinder_pressure_five";

    /**
     * 油缸压力6#
     */
    public static final String CYLINDER_PRESSURE_SIX = "cylinder_pressure_six";

    /**
     * 油缸压力1#
     */
    public static final String CYLINDER_PRESSURE_A = "cylinder_pressure_a";

    /**
     * 油缸压力2#
     */
    public static final String CYLINDER_PRESSURE_B = "cylinder_pressure_b";

    /**
     * 油缸压力3#
     */
    public static final String CYLINDER_PRESSURE_C = "cylinder_pressure_c";

    /**
     * 油缸压力4#
     */
    public static final String CYLINDER_PRESSURE_D = "cylinder_pressure_d";

    /**
     * 油缸压力5#
     */
    public static final String CYLINDER_PRESSURE_E = "cylinder_pressure_e";

    /**
     * 油缸压力6#
     */
    public static final String CYLINDER_PRESSURE_F = "cylinder_pressure_f";

    /**
     * 油箱温度
     */
    public static final String OIL_TEMP = "oil_temp";

    //----------------------------------------------------- 驱动系统参数 ---------------------------------------------------------------
    /**
     * 电机总功率
     */
    public static final String POWER = "power";

    /**
     * 振动频率
     */
    public static final String FREQUENCY = "frequency";

    /**
     * 电机电流1#
     */
    public static final String ELECTRIC_CURRENT_ONE = "electric_current_one";

    /**
     * 电机电流2#
     */
    public static final String ELECTRIC_CURRENT_TWO = "electric_current_two";

    /**
     * 电机电流3#
     */
    public static final String ELECTRIC_CURRENT_THREE = "electric_current_three";

    /**
     * 电机电流4#
     */
    public static final String ELECTRIC_CURRENT_FOUR = "electric_current_four";

    /**
     * 电机电流5#
     */
    public static final String ELECTRIC_CURRENT_FIVE = "electric_current_five";

    /**
     * 电机电流6#
     */
    public static final String ELECTRIC_CURRENT_SIX = "electric_current_six";

    /**
     * 电机电流7#
     */
    public static final String ELECTRIC_CURRENT_SEVEN = "electric_current_seven";

    /**
     * 电机电流8#
     */
    public static final String ELECTRIC_CURRENT_EIGHT = "electric_current_eight";

    /**
     * 电机电流9#
     */
    public static final String ELECTRIC_CURRENT_NINE = "electric_current_nine";

    /**
     * 电机电流10#
     */
    public static final String ELECTRIC_CURRENT_TEN = "electric_current_ten";

    /**
     * 电机电流11#
     */
    public static final String ELECTRIC_CURRENT_ELEVEN = "electric_current_eleven";

    /**
     * 电机电流12#
     */
    public static final String ELECTRIC_CURRENT_TWELVE = "electric_current_twelve";

    /**
     * 电机电流13#
     */
    public static final String ELECTRIC_CURRENT_THIRTEEN = "electric_current_thirteen";

    /**
     * 电机电流14#
     */
    public static final String ELECTRIC_CURRENT_FOURTEEN = "electric_current_fourteen";

    /**
     * 电机温度/Double#1
     */
    public static final String TEMPERATURE_ONE = "temperature_one";
    /**
     * 电机温度/Double#2
     */
    public static final String TEMPERATURE_TWO = "temperature_two";
    /**
     * 电机温度/Double#3
     */
    public static final String TEMPERATURE_THREE = "temperature_three";
    /**
     * 电机温度/Double#4
     */
    public static final String TEMPERATURE_FOUR = "temperature_four";
    /**
     * 电机温度/Double#5
     */
    public static final String TEMPERATURE_FIVE = "temperature_five";
    /**
     * 电机温度/Double#6
     */
    public static final String TEMPERATURE_SIX = "temperature_six";
    /**
     * 电机温度/Double#7
     */
    public static final String TEMPERATURE_SEVEN = "temperature_seven";
    /**
     * 电机温度/Double#8
     */
    public static final String TEMPERATURE_EIGHT = "temperature_eight";
    /**
     * 电机温度/Double#9
     */
    public static final String TEMPERATURE_NINE = "temperature_nine";
    /**
     * 电机温度/Double#10
     */
    public static final String TEMPERATURE_TEN = "temperature_ten";
    /**
     * 电机温度/Double#11
     */
    public static final String TEMPERATURE_ELEVEN = "temperature_eleven";
    /**
     * 电机温度/Double#12
     */
    public static final String TEMPERATURE_TWELVE = "temperature_twelve";
    /**
     * 电机温度/Double#13
     */
    public static final String TEMPERATURE_THIRTEEN = "temperature_thirteen";
    /**
     * 电机温度/Double#14
     */
    public static final String TEMPERATURE_FOURTEEN = "temperature_fourteen";



    //---------------------螺机系统(TBM出渣系统参数)-----------------

    /**
     * 螺机转速
     */
    public static final String SNAIL_ROTATIONAL_SPEED = "snail_rotational_speed";

    /**
     * 螺机扭矩
     */
    public static final String SNAIL_TORCHQUE = "snail_torchque";

    /**
     * 皮带机带速
     */
    public static final String BELT_ROTATIONAL_SPEED = "belt_rotational_speed";

    //------------导向系统---------------------
    /**
     * 前点水平偏差
     */
    public static final String HORIZONTAL_FRONT = "horizontal_front";
    /**
     * 前点垂直偏差
     */
    public static final String VERTICAL_FRONT = "vertical_front";

    /**
     * 后点水平偏差
     */
    public static final String HORIZONTAL_END = "horizontal_end";

    /**
     * 后点垂直偏差
     */
    public static final String VERTICAL_END = "vertical_end";

    /**
     * 滚动角
     */
    public static final String ROLLING_ANGLE = "rolling_angle";
    /**
     * 仰俯角
     */
    public static final String PITCH_ANGLE = "pitch_angle";

    /**
     * 盾首坐标X
     */
    public static final String DISH_COORDINATE_X = "dish_coordinate_x";

    /**
     * 盾首坐标Y
     */
    public static final String DISH_COORDINATE_Y = "dish_coordinate_y";

    /**
     * 盾首坐标Z
     */
    public static final String DISH_COORDINATE_Z = "dish_coordinate_z";

    //----------------------------------------------------- 土仓压力参数 ---------------------------------------------------------------
    /**
     * 压力
     */
    public static final String BRACE_PRESSURE = "brace_pressure";

    /**
     * 位置1
     */
    public static final String POSITION_ONE = "brace_pressure_one";

    /**
     * 位置2
     */
    public static final String POSITION_TWO = "brace_pressure_two";

    /**
     * 位置3
     */
    public static final String POSITION_THREE = "brace_pressure_three";

    /**
     * 位置4
     */
    public static final String POSITION_FOUE = "brace_pressure_four";

    /**
     * 位置5
     */
    public static final String POSITION_FIVE = "brace_pressure_five";

    /**
     * 位置6
     */
    public static final String POSITION_SIX = "brace_pressure_six";

    /**
     * Position seven
     */
    public static final String POSITION_SEVEN = "brace_pressure_seven";

    /**
     * Position eight
     */
    public static final String POSITION_EIGHT = "brace_pressure_eight";

    //----------------------------------------------------- 有害气体参数 ---------------------------------------------------------------

    public static final String CH4_LEVEL = "ch4_level";

    public static final String CO2_LEVEL = "co2_level";

    public static final String H2S_LEVEL = "h2s_level";

    public static final String O2_LEVEL = "o2_level";

    public static final String CO_LEVEL = "co_level";

    //----------------------------------------------------- 支撑系统参数 ---------------------------------------------------------------

    public static final String UP_BRACE_PRESSURE1 = "up_brace_pressure1";

    public static final String UP_BRACE_PRESSURE2 = "up_brace_pressure2";

    public static final String UP_BRACE_PRESSURE3 = "up_brace_pressure3";

    public static final String UP_BRACE_PRESSURE4 = "up_brace_pressure4";

    public static final String UP_BRACE_PRESSURE5 = "up_brace_pressure5";

    public static final String UP_BRACE_PRESSURE6 = "up_brace_pressure6";

    public static final String DOWN_BRACE_PRESSURE1 = "down_brace_pressure1";

    public static final String DOWN_BRACE_PRESSURE2 = "down_brace_pressure2";

    public static final String DOWN_BRACE_PRESSURE3 = "down_brace_pressure3";

    public static final String DOWN_BRACE_PRESSURE4 = "down_brace_pressure4";

    public static final String DOWN_BRACE_PRESSURE5 = "down_brace_pressure5";

    public static final String DOWN_BRACE_PRESSURE6 = "down_brace_pressure6";
}