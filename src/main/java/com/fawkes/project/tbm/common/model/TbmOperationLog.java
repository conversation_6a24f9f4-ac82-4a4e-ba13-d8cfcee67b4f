package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 操作记录表
 * <AUTHOR>
 * @date 2025-01-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "tbm_operation_log")
@ApiModel("操作记录表")
public class TbmOperationLog extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模块类型（TOOL_MANAGEMENT-刀具管理，PROCESS_MANAGEMENT-工序管理）
     */
    @ApiModelProperty("模块类型（TOOL_MANAGEMENT-刀具管理，PROCESS_MANAGEMENT-工序管理）")
    private String moduleType;

    /**
     * 记录ID（刀具管理记录ID或工序管理记录ID）
     */
    @ApiModelProperty("记录ID（刀具管理记录ID或工序管理记录ID）")
    private Long bizId;

    /**
     * 操作类型（ADD-新增，UPDATE-修改，DELETE-删除）
     */
    @ApiModelProperty("操作类型（ADD-新增，UPDATE-修改，DELETE-删除）")
    private String operationType;

    @ApiModelProperty("数据源,数据来源（0-PC，1-APP）")
    private Integer dataSource;

    @ApiModelProperty("修改内容（json）")
    private String operationContent;

    private String createName;

    private String updateName;
} 