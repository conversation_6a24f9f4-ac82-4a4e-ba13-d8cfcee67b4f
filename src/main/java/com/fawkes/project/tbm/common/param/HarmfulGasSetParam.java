package com.fawkes.project.tbm.common.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 有害气体设备管理表
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel("有害气体设置参数")
public class HarmfulGasSetParam implements Serializable {
    @NotNull(message = "ID不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 甲烷
     */
     @ApiModelProperty("甲烷")
    private BigDecimal ch4Level;

    /**
     * 二氧化碳
     */
    @ApiModelProperty("二氧化碳")
    private BigDecimal h2sLevel;

    /**
     * 氧气
     */
    @ApiModelProperty("氧气")
    private BigDecimal o2Level;

    /**
     * 一氧化碳
     */
    @ApiModelProperty("一氧化碳")
    private BigDecimal coLevel;
    

}