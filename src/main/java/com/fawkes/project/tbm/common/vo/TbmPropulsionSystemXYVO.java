package com.fawkes.project.tbm.common.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version TbmPropulsionSystemXYVO 2025/6/11 17:03
 */
@Data
public class TbmPropulsionSystemXYVO implements Serializable {
    /**
     * x轴-环数
     */
    private String ring;
    /**
     * y轴-最大
     */
    private BigDecimal thrustMax;
    /**
     * y轴-最大
     */
    private BigDecimal propSpeedMax;
    /**
     * y轴-最大
     */
    private BigDecimal cylinderPressureMax;
    /**
     * y轴-平均
     */
    private BigDecimal thrustAverage;
    /**
     * y轴-平均
     */
    private BigDecimal propSpeedAverage;
    /**
     * y轴-平均
     */
    private BigDecimal cylinderPressureAverage;
}
