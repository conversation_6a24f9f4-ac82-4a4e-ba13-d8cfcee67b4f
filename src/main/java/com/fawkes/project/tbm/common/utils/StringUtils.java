package com.fawkes.project.tbm.common.utils;

import cn.hutool.core.text.StrFormatter;
import com.fawkes.project.tbm.common.constants.FormConstants;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils {

    /**
     * 新行
     */
    public static final String NEW_LINE = "<p>";

    /**
     * 空内容
     */
    public static final String EMPTY_CONTENT = "--";

    /**
     * 特殊字符匹配正则表达式
     * <p>匹配指定字符外的字符</p>
     */
    public static final Pattern SPECIAL_SYMBOL_REGEX = Pattern.compile("[^0-9a-zA-Z\\u4e00-\\u9fa5]");

    /**
     * 数值匹配正则表达式
     */
    public static final Pattern NUMBER_REGEX = Pattern.compile("^(?<integer>-?\\d)+(\\.(?<decimal>\\d+))?$");

    /**
     * 存在中文正则表达式
     */
    public static final String CHINESE_EXISTS_REGEX = "[\u4e00-\u9fa5]";

    /**
     * 空字符串
     */
    private static final String NULLSTR = "";

    /**
     * 下划线
     */
    private static final char SEPARATOR = '_';

    /**
     * 金额为0或空的展示内容
     */
    private static final String MONEY_ZERO = "¥0.00";

    /**
     * 获取参数不为空值
     *
     * @param value defaultValue 要判断的value
     * @return value 返回值
     */
    public static <T> T nvl(T value, T defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * * 判断一个Collection是否为空， 包含List，Set，Queue
     *
     * @param coll 要判断的Collection
     * @return true：为空 false：非空
     */
    public static boolean isEmpty(Collection<?> coll) {
        return isNull(coll) || coll.isEmpty();
    }

    /**
     * * 判断一个Collection是否非空，包含List，Set，Queue
     *
     * @param coll 要判断的Collection
     * @return true：非空 false：空
     */
    public static boolean isNotEmpty(Collection<?> coll) {
        return !isEmpty(coll);
    }

    /**
     * * 判断一个对象数组是否为空
     *
     * @param objects 要判断的对象数组 * @return true：为空 false：非空
     */
    public static boolean isEmpty(Object[] objects) {
        return isNull(objects) || (objects.length == 0);
    }

    /**
     * * 判断一个对象数组是否非空
     *
     * @param objects 要判断的对象数组
     * @return true：非空 false：空
     */
    public static boolean isNotEmpty(Object[] objects) {
        return !isEmpty(objects);
    }

    /**
     * * 判断一个Map是否为空
     *
     * @param map 要判断的Map
     * @return true：为空 false：非空
     */
    public static boolean isEmpty(Map<?, ?> map) {
        return isNull(map) || map.isEmpty();
    }

    /**
     * * 判断一个Map是否为空
     *
     * @param map 要判断的Map
     * @return true：非空 false：空
     */
    public static boolean isNotEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }

    /**
     * * 判断一个字符串是否为空串
     *
     * @param str String
     * @return true：为空 false：非空
     */
    public static boolean isEmpty(String str) {
        return isNull(str) || NULLSTR.equals(str.trim());
    }

    /**
     * * 判断一个字符串是否为非空串
     *
     * @param str String
     * @return true：非空串 false：空串
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * * 判断一个对象是否为空
     *
     * @param object Object
     * @return true：为空 false：非空
     */
    public static boolean isNull(Object object) {
        return object == null;
    }

    /**
     * * 判断一个对象是否非空
     *
     * @param object Object
     * @return true：非空 false：空
     */
    public static boolean isNotNull(Object object) {
        return !isNull(object);
    }

    /**
     * * 判断一个对象是否是数组类型（Java基本型别的数组）
     *
     * @param object 对象
     * @return true：是数组 false：不是数组
     */
    public static boolean isArray(Object object) {
        return isNotNull(object) && object.getClass().isArray();
    }

    /**
     * 去空格
     */
    public static String trim(String str) {
        return (str == null ? "" : str.trim());
    }

    /**
     * 截取字符串
     *
     * @param str   字符串
     * @param start 开始
     * @return 结果
     */
    public static String substring(final String str, int start) {
        if (str == null) {
            return NULLSTR;
        }

        if (start < 0) {
            start = str.length() + start;
        }

        if (start < 0) {
            start = 0;
        }
        if (start > str.length()) {
            return NULLSTR;
        }

        return str.substring(start);
    }

    /**
     * 截取字符串
     *
     * @param str   字符串
     * @param start 开始
     * @param end   结束
     * @return 结果
     */
    public static String substring(final String str, int start, int end) {
        if (str == null) {
            return NULLSTR;
        }

        if (end < 0) {
            end = str.length() + end;
        }
        if (start < 0) {
            start = str.length() + start;
        }

        if (end > str.length()) {
            end = str.length();
        }

        if (start > end) {
            return NULLSTR;
        }

        if (start < 0) {
            start = 0;
        }
        if (end < 0) {
            end = 0;
        }

        return str.substring(start, end);
    }

    /**
     * 判断是否为空，并且不是空白字符
     *
     * @param str 要判断的value
     * @return 结果
     */
    public static boolean hasText(String str) {
        return (str != null && !str.isEmpty() && containsText(str));
    }

    private static boolean containsText(CharSequence str) {
        int strLen = str.length();
        for (int i = 0; i < strLen; i++) {
            if (!Character.isWhitespace(str.charAt(i))) {
                return true;
            }
        }
        return false;
    }

    /**
     * 格式化文本, {} 表示占位符<br> 此方法只是简单将占位符 {} 按照顺序替换为参数<br> 如果想输出 {} 使用 \\转义 { 即可，如果想输出 {} 之前的 \ 使用双转义符 \\\\ 即可<br> 例：<br>
     * 通常使用：format("this is {} for {}", "a", "b") -> this is a for b<br> 转义{}： format("this is \\{} for {}", "a", "b")
     * -> this is \{} for a<br> 转义\： format("this is \\\\{} for {}", "a", "b") -> this is \a for b<br>
     *
     * @param template 文本模板，被替换的部分用 {} 表示
     * @param params   参数值
     * @return 格式化后的文本
     */
    public static String format(String template, Object... params) {
        if (isEmpty(params) || isEmpty(template)) {
            return template;
        }
        return StrFormatter.format(template, params);
    }

    /**
     * 是否为http(s)://开头
     *
     * @param link 链接
     * @return 结果
     */
    public static boolean ishttp(String link) {
        return StringUtils.startsWithAny(link, FormConstants.HTTP, FormConstants.HTTPS);
    }

    /**
     * 字符串转set
     *
     * @param str 字符串
     * @param sep 分隔符
     * @return set集合
     */
    public static Set<String> str2Set(String str, String sep) {
        return new HashSet<>(str2List(str, sep, true, false));
    }

    /**
     * 字符串转list
     *
     * @param str         字符串
     * @param sep         分隔符
     * @param filterBlank 过滤纯空白
     * @param trim        去掉首尾空白
     * @return list集合
     */
    public static List<String> str2List(String str, String sep, boolean filterBlank, boolean trim) {
        List<String> list = new ArrayList<>();
        if (StringUtils.isEmpty(str)) {
            return list;
        }

        // 过滤空白字符串
        if (filterBlank && StringUtils.isBlank(str)) {
            return list;
        }
        String[] split = str.split(sep);
        for (String string : split) {
            if (filterBlank && StringUtils.isBlank(string)) {
                continue;
            }
            if (trim) {
                string = string.trim();
            }
            list.add(string);
        }

        return list;
    }

    /**
     * 查找指定字符串是否包含指定字符串列表中的任意一个字符串同时串忽略大小写
     *
     * @param cs                  指定字符串
     * @param searchCharSequences 需要检查的字符串数组
     * @return 是否包含任意一个字符串
     */
    public static boolean containsAnyIgnoreCase(CharSequence cs, CharSequence... searchCharSequences) {
        if (isEmpty(cs) || isEmpty(searchCharSequences)) {
            return false;
        }
        for (CharSequence testStr : searchCharSequences) {
            if (containsIgnoreCase(cs, testStr)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 驼峰转下划线命名
     */
    public static String toUnderScoreCase(String str) {
        if (str == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        // 前置字符是否大写
        boolean preCharIsUpperCase;
        // 当前字符是否大写
        boolean curreCharIsUpperCase;
        // 下一字符是否大写
        boolean nexteCharIsUpperCase = true;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (i > 0) {
                preCharIsUpperCase = Character.isUpperCase(str.charAt(i - 1));
            } else {
                preCharIsUpperCase = false;
            }

            curreCharIsUpperCase = Character.isUpperCase(c);

            if (i < (str.length() - 1)) {
                nexteCharIsUpperCase = Character.isUpperCase(str.charAt(i + 1));
            }

            if (preCharIsUpperCase && curreCharIsUpperCase && !nexteCharIsUpperCase) {
                sb.append(SEPARATOR);
            } else if ((i != 0 && !preCharIsUpperCase) && curreCharIsUpperCase) {
                sb.append(SEPARATOR);
            }
            sb.append(Character.toLowerCase(c));
        }

        return sb.toString();
    }

    /**
     * 是否包含字符串
     *
     * @param str  验证字符串
     * @param strs 字符串组
     * @return 包含返回true
     */
    public static boolean inStringIgnoreCase(String str, String... strs) {
        if (str != null && strs != null) {
            for (String s : strs) {
                if (str.equalsIgnoreCase(trim(s))) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 将下划线大写方式命名的字符串转换为驼峰式。如果转换前的下划线大写方式命名的字符串为空，则返回空字符串。 例如：HELLO_WORLD->HelloWorld
     *
     * @param name 转换前的下划线大写方式命名的字符串
     * @return 转换后的驼峰式命名的字符串
     */
    public static String convertToCamelCase(String name) {
        StringBuilder result = new StringBuilder();
        // 快速检查
        if (name == null || name.isEmpty()) {
            // 没必要转换
            return "";
        } else if (!name.contains("_")) {
            // 不含下划线，仅将首字母大写
            return name.substring(0, 1).toUpperCase() + name.substring(1);
        }
        // 用下划线将原始字符串分割
        String[] camels = name.split("_");
        for (String camel : camels) {
            // 跳过原始字符串中开头、结尾的下换线或双重下划线
            if (camel.isEmpty()) {
                continue;
            }
            // 首字母大写
            result.append(camel.substring(0, 1).toUpperCase());
            result.append(camel.substring(1).toLowerCase());
        }
        return result.toString();
    }

    /**
     * 驼峰式命名法 例如：user_name->userName
     */
    public static String toCamelCase(String s) {
        if (s == null) {
            return null;
        }
        s = s.toLowerCase();
        StringBuilder sb = new StringBuilder(s.length());
        boolean upperCase = false;
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);

            if (c == SEPARATOR) {
                upperCase = true;
            } else if (upperCase) {
                sb.append(Character.toUpperCase(c));
                upperCase = false;
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 查找指定字符串是否匹配指定字符串列表中的任意一个字符串
     *
     * @param str  指定字符串
     * @param strs 需要检查的字符串数组
     * @return 是否匹配
     */
    public static boolean matches(String str, List<String> strs) {
        if (isEmpty(str) || isEmpty(strs)) {
            return false;
        }
        for (String pattern : strs) {
            if (isMatch(pattern, str)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断url是否与规则配置: ? 表示单个字符; * 表示一层路径内的任意字符串，不可跨层级; ** 表示任意层路径;
     *
     * @param pattern 匹配规则
     * @param url     需要匹配的url
     * @return true: 匹配
     */
    public static boolean isMatch(String pattern, String url) {
        AntPathMatcher matcher = new AntPathMatcher();
        return matcher.match(pattern, url);
    }

    @SuppressWarnings("unchecked")
    public static <T> T cast(Object obj) {
        return (T) obj;
    }

    /**
     * 数字左边补齐0，使之达到指定长度。注意，如果数字转换为字符串后，长度大于size，则只保留 最后size个字符。
     *
     * @param num  数字对象
     * @param size 字符串指定长度
     * @return 返回数字的字符串格式，该字符串为指定长度。
     */
    public static String padl(final Number num, final int size) {
        return padl(num.toString(), size, '0');
    }

    /**
     * 字符串左补齐。如果原始字符串s长度大于size，则只保留最后size个字符。
     *
     * @param s    原始字符串
     * @param size 字符串指定长度
     * @param c    用于补齐的字符
     * @return 返回指定长度的字符串，由原字符串左补齐或截取得到。
     */
    public static String padl(final String s, final int size, final char c) {
        final StringBuilder sb = new StringBuilder(size);
        if (s != null) {
            final int len = s.length();
            if (s.length() <= size) {
                for (int i = size - len; i > 0; i--) {
                    sb.append(c);
                }
                sb.append(s);
            } else {
                return s.substring(len - size, len);
            }
        } else {
            for (int i = size; i > 0; i--) {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 格式化金额
     * <p>money = 0时, ¥0.00</p>
     * <p>money > 0时, ¥xx.xx</p>
     * <p>money < 0时, -¥xx.xx</p>
     *
     * @param money 金额
     * @return 格式化结果
     */
    public static String formatMoney(BigDecimal money) {
        if (money == null) {
            return "--";
        }

        if (MathUtils.equal(money, BigDecimal.ZERO)) {
            return MONEY_ZERO;
        }
        if (MathUtils.less(money, BigDecimal.ZERO)) {
            return String.format("-¥%.2f", money.multiply(BigDecimal.valueOf(-1)));
        } else {
            return String.format("¥%.2f", money);
        }
    }

    /**
     * 判断字符串中是否存在特殊字符
     *
     * @param str 字符串
     * @return true: 字符串中存在特殊字符
     */
    public static boolean hasSpecialSymbol(String str) {
        if (isBlank(str)) {
            return true;
        }

        return SPECIAL_SYMBOL_REGEX.matcher(str).find();
    }

    /**
     * 是否为数值(包含整数和小数)
     *
     * @param str 字符串
     * @return true: 字符串为数值
     */
    public static boolean isNumber(String str) {
        if (isBlank(str)) {
            return false;
        }
        str = str.replaceAll(",", "");
        return NUMBER_REGEX.matcher(str).matches();
    }

    /**
     * 获取数值字符串中的小数部分
     *
     * @param str 字符串
     * @return 小数部分
     */
    public static String getDecimalPart(String str) {
        if (isBlank(str)) {
            return null;
        }

        Matcher matcher = NUMBER_REGEX.matcher(str);
        boolean isMatches = matcher.matches();
        if (isMatches == false) {
            return null;
        }

        return matcher.group("decimal");
    }

    /**
     * 转换字符串列表
     * <p>以 , 进行评拼接</p>
     *
     * @param strList 字符串列表
     * @return 合并后的字符串
     */
    public static String convertStringList(List<String> strList) {
        return convertStringList(strList, ",");
    }

    /**
     * 转换字符串列表
     * <p>以 , 进行评拼接</p>
     *
     * @param strList 字符串列表
     * @return 合并后的字符串
     */
    public static String convertStringList(List<String> strList, String delimiter) {
        return NullMergeUtils.emptyMerge(strList, list -> String.join(delimiter, list), StringUtils.EMPTY);
    }

    /**
     * 转换字符串列表
     * <p>以 / 进行评拼接</p>
     *
     * @param strList 字符串列表
     * @return 合并后的字符串
     */
    public static String convertSlashList(List<String> strList) {
        return convertStringList(strList, "/");
    }

    /**
     * 将对象列表转换为以 ',' 拼接的字符串
     *
     * @param list             对象列表
     * @param contextConverter 对象列表内容转换器
     * @param <T>              对象类型
     * @return 拼接后的字符串
     */
    public static <T> String convertStringList(List<T> list, Function<T, String> contextConverter, String delimiter) {
        List<String> stringList = ListUtil.map(list, item -> {
            String result = contextConverter.apply(item);
            Assert.notNull(result, "转换结果不能为空");
            Assert.doesNotContain(result, ",", "转换结果不能包含 ','");
            return result;
        });
        return convertStringList(stringList, delimiter);
    }

    /**
     * 将对象列表转换为以 ',' 拼接的字符串
     *
     * @param list             对象列表
     * @param contextConverter 对象列表内容转换器
     * @param <T>              对象类型
     * @return 拼接后的字符串
     */
    public static <T> String convertStringList(List<T> list, Function<T, String> contextConverter) {
        List<String> stringList = ListUtil.map(list, item -> {
            String result = contextConverter.apply(item);
            Assert.notNull(result, "转换结果不能为空");
            Assert.doesNotContain(result, ",", "转换结果不能包含 ','");
            return result;
        });
        return convertStringList(stringList);
    }

    /**
     * 将列表转换为以 ',' 拼接的字符串
     *
     * @param list long列表
     * @return 拼接后的字符串
     */
    public static String convertLongList(List<Long> list) {
        return convertStringList(list, x -> NullMergeUtils.nullMerge(x, String::valueOf, StringUtils.EMPTY));
    }

    /**
     * 拆分字符串列表
     * <p>字符串列表需以 , 进行拼接</p>
     *
     * @param str 字符串列表
     * @return 拆分后的字符串列表
     */
    public static List<String> convertStringList(String str) {
        if (isBlank(str)) {
            return ListUtil.emptyList();
        }

        return Arrays.stream(str.split(","))
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.toList());
    }

    /**
     * 拆分字符串列表
     * <p>字符串列表需以 , 进行拼接</p>
     *
     * @param str 字符串列表
     * @return 拆分后的字符串列表
     */
    public static List<String> convertStringList(String str, String regex) {
        if (isBlank(str)) {
            return ListUtil.emptyList();
        }

        return Arrays.stream(str.split(regex))
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.toList());
    }

    /**
     * 拆分字符串列表, 转换为指定类型列表
     * <p>字符串列表需以 , 进行拼接</p>
     *
     * @param str       字符串列表
     * @param converter 转换函数
     * @param <T>       转换后的类型
     * @return 拆分后的字符串列表
     */
    public static <T> List<T> convertStringList(String str, Function<String, T> converter) {
        if (isBlank(str)) {
            return ListUtil.emptyList();
        }

        return Arrays.stream(str.split(","))
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .map(converter)
                .collect(Collectors.toList());
    }

    /**
     * 拆分字符串列表, 转换为Long类型列表
     * <p>字符串列表需以 , 进行拼接</p>
     *
     * @param str 字符串列表
     * @return 拆分后的字符串列表
     */
    public static List<Long> convertLongList(String str) {
        return convertStringList(str, Long::valueOf);
    }

    /**
     * 格式化金额
     *
     * @param value 金额
     */
    public static String formatBigDecimal(BigDecimal value) {
        return formatBigDecimal(value, EMPTY);
    }

    /**
     * 格式化金额
     *
     * @param value        金额
     * @param defaultValue 默认值, 金额为null时返回
     */
    public static String formatBigDecimal(BigDecimal value, String defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        BigDecimal resultValue = value.setScale(2, BigDecimal.ROUND_HALF_UP);
        String result = String.format("%,.2f", resultValue);
        // 获取小数部分,
        String fractional = result.substring(result.indexOf(".") + 1);
        // 如果小数部分为00或0, 则去掉小数部分
        if ("00".equals(fractional) || "0".equals(fractional)) {
            result = result.substring(0, result.indexOf("."));
            // 如果小数部分为[1-9]0, 则去掉小数部分最后一位
        } else if (fractional.length() > 1 && '0' == fractional.charAt(1)) {
            // 如果小数部分为0, 则去掉小数部分最后一位
            result = result.substring(0, result.length() - 1);
        }

        return result;
    }

    /**
     * 格式化金额
     *
     * @param value 金额
     */
    public static String formatThreeBigDecimal(BigDecimal value) {
        BigDecimal resultValue = value.setScale(3, BigDecimal.ROUND_HALF_UP);
        String result = String.format("%,.3f", resultValue);
        // 获取小数部分,
        String fractional = result.substring(result.indexOf(".") + 1);
        if (fractional.endsWith("000")) {
            // 如果小数部分为0, 则去掉小数部分最后一位
            result = result.substring(0, result.length() - 4);
        } else if (fractional.endsWith("00")) {
            // 如果小数部分为0, 则去掉小数部分最后一位
            result = result.substring(0, result.length() - 2);
        } else if (fractional.endsWith("0")) {
            // 如果小数部分为0, 则去掉小数部分最后一位
            result = result.substring(0, result.length() - 1);
        }

        return result;
    }

    /**
     * 格式化数字为千分位
     *
     * @param text
     * @return String 返回类型
     * @Title: fmtMicrometer
     * @Description: 格式化数字为千分位
     */
    public static String fmtMicrometer(String text) {
        if (StringUtils.isBlank(text) || text.contains("null")) {
            return null;
        }
        DecimalFormat df = null;
        if (text.indexOf(".") > 0) {
            if (text.length() - text.indexOf(".") - 1 == 0) {
                df = new DecimalFormat("###,##0.");
            } else if (text.length() - text.indexOf(".") - 1 == 1) {
                df = new DecimalFormat("###,##0.0");
            } else {
                df = new DecimalFormat("###,##0.00");
            }
        } else {
            df = new DecimalFormat("###,##0");
        }
        double number;
        try {
            number = Double.parseDouble(text);
        } catch (Exception e) {
            return null;
        }
        String format = df.format(number);
        return formatGoZero(format);
    }

    /**
     * 格式化金额
     *
     * @param value 金额
     */
    public static String formatGoZero(String value) {

        // 获取小数部分,
        String fractional = "";
        if (value.contains(".")) {
            fractional = value.substring(value.indexOf(".") + 1);
            // 如果小数部分为00或0, 则去掉小数部分
            if ("00".equals(fractional) || "0".equals(fractional)) {
                value = value.substring(0, value.indexOf("."));
                // 如果小数部分为[1-9]0, 则去掉小数部分最后一位
            } else if (fractional.length() > 1 && '0' == fractional.charAt(1)) {
                // 如果小数部分为0, 则去掉小数部分最后一位
                value = value.substring(0, value.length() - 1);
            }
        }

        return value;
    }

    /**
     * 去除空格和换行
     *
     * @param value 值
     * @return 去除后值
     */
    public static String removeSpaces(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }
        value = value.replaceAll("\r\n|\r|\n", "");
        return value.trim();
    }

    /**
     * 将数字格式化为百分比字符串
     *
     * @param value
     * @return
     */
    public static String toLocalePercentageFormat(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        double number = Double.parseDouble(value.replace("%", ""));
        NumberFormat format = NumberFormat.getPercentInstance(Locale.US);
        return format.format(number);
    }

    /**
     * 替换Windows文件系统禁用的字符
     *
     * @param str         需处理的字符串
     * @param replacement 要为禁用字符替换的字符串
     * @return 替换结果
     */
    public static String replaceWindowsFileSystemDisableChar(String str, String replacement) {
        Assert.notNull(replacement, "替换的字符串不能为null");
        if (isBlank(str)) {
            return str;
        }

        return str.replaceAll("[\\\\/:*?\"<>|]", replacement);
    }
}
