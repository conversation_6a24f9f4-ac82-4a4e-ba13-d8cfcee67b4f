package com.fawkes.project.tbm.common.enums;

/**
 * 盾构机运行状态枚举
 *
 * <AUTHOR>
 */
public enum AssemblyModeEnums {
    /**
     * 未拼装
     */
    ASSEMBLY_NO_IN_PROGRESS("0", "未拼装"),

    /**
     * 拼装中
     */
    ASSEMBLY_IN_PROGRESS("1", "拼装中");

    private final String code;
    private final String name;

    AssemblyModeEnums(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByValue(String code) {
        for (AssemblyModeEnums value : AssemblyModeEnums.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
