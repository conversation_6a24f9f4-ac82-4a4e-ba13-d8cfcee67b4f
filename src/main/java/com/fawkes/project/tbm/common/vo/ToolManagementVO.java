package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@ApiModel("工序返回VO")
public class ToolManagementVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("填报时间")
    private Timestamp updateDate;

    /**
     * 区段
     */
    @ApiModelProperty("区段")
    private String section;

    /**
     * 区段名称
     */
    @ApiModelProperty("区段名称")
    private String sectionName;

    /**
     * 填报人
     */
    @ApiModelProperty("填报人")
    private String updateName;

    /**
     * 刀具名称id
     */
    @ApiModelProperty("刀具名称id")
    private Long toolNameManageId;

    /**
     * 刀具名称
     */
    @ApiModelProperty("刀具名称")
    private String toolName;


    /**
     * 刀具位置id
     */
    @ApiModelProperty("刀具位置id")
    private Long toolPositionManageId;

    /**
     * 刀具位置
     */
    @ApiModelProperty("刀具位置")
    private String toolLocation;

    /**
     * 环号
     */
    @ApiModelProperty("环号")
    private Integer ringNum;

    /**
     * 磨损值
     */
    @ApiModelProperty("磨损值")
    private BigDecimal wearValue;

    /**
     * 开始方位
     */
    @ApiModelProperty("开始方位")
    private String startOrientation;

    /**
     * 开始里程
     */
    @ApiModelProperty("开始里程")
    private BigDecimal startMileage;

    /**
     * 结束方位
     */
    @ApiModelProperty("结束方位")
    private String endOrientation;

    /**
     * 结束里程
     */
    @ApiModelProperty("结束里程")
    private BigDecimal endMileage;

    /**
     * 里程
     */
    @ApiModelProperty("里程")
    private String mileage;

    /**
     * 开仓开始时间
     */
    @ApiModelProperty("开仓开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Timestamp openWarehouseStartTime;

    /**
     * 开仓结束时间
     */
    @ApiModelProperty("开仓结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Timestamp openWarehouseEndTime;

    /**
     * 新装刀具状态
     */
    @ApiModelProperty("新装刀具状态")
    private String newlyInstalledCuttingToolsStatus;

    /**
     * 更换阈值
     */
    @ApiModelProperty("更换阈值")
    private BigDecimal changeThreshold;

    /**
     * 换刀原因
     */
    @ApiModelProperty("换刀原因")
    private String changeToolReason;

    /**
     * 是否换刀（true-是，false-否）
     */
    @ApiModelProperty("是否换刀（true-是，false-否）")
    private boolean hasToolChanged;

    /**
     * 附件
     */
    @ApiModelProperty("附件")
    private String attachment;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /***
     * 换刀图片
     */
    @ApiModelProperty("换刀图片")
    private String changeToolPicture;

    /***
     * changeToolPicturef8s
     */
    @ApiModelProperty("changeToolPicturef8s")
    private String changeToolPictureF8s;
}