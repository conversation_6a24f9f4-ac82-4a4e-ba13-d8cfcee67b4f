package com.fawkes.project.tbm.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @Description 工序环号
 * @Date 2025/6/4 11:56
 */
@Data
@ApiModel("工序环号")
public class ProcessManagementRingNumVO {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.section
     *
     * @mbggenerated
     */
    @ApiModelProperty("区段")
    private String section;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.ring_num
     *
     * @mbggenerated
     */
    @ApiModelProperty("环号")
    private Integer ringNum;

    @ApiModelProperty("更新时间")
    private Timestamp updateTime;

    @ApiModelProperty("是否是最新一条数据的环号，0：否；1：是")
    private Integer dataLastRingNum;
}
