package com.fawkes.project.tbm.common.vo.app;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@ApiModel("APP工序列表返回VO")
public class ToolManagementAppVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键Id */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("id")
    private Long id;

    /** 区段 */
    @ApiModelProperty("区段")
    private String section;

    /** 区段名称 */
    @ApiModelProperty("区段名称")
    private String sectionName;

    /** 刀具名称id */
    @ApiModelProperty("刀具名称id")
    private Long toolNameManageId;

    /** 刀具名称 */
    @ApiModelProperty("刀具名称")
    private String toolName;

    /** 刀具位置id */
    @ApiModelProperty("刀具位置id")
    private Long toolPositionManageId;

    /** 刀具位置 */
    @ApiModelProperty("刀具位置")
    private String toolLocation;

    /** 磨损值 */
    @ApiModelProperty("磨损值")
    private BigDecimal wearValue;
}