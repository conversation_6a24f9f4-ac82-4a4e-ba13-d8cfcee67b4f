package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 摄像头配置表
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel("摄像头配置VO")
public class CameraManageVO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备code
     */
    @ApiModelProperty("设备code")
    private String deviceCode;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 摄像头名称
     */
    @ApiModelProperty("摄像头名称")
    private String cameraName;

    /**
     * 摄像头ip
     */
    @ApiModelProperty("摄像头ip")
    private String cameraIp;

    /**
     * 摄像头url
     */
    @ApiModelProperty("摄像头url")
    private String cameraUrl;
    /**
     * 在线状态 0在线 -1不在线
     */
    @ApiModelProperty("在线状态 0在线 -1不在线")
    private String onlineStatus;

    @ApiModelProperty("是否是工序摄像头 0：不是 ，1：是")
    private Integer  processCamera;
}