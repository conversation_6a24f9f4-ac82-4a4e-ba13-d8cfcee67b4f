package com.fawkes.project.tbm.common.tools.verify.verifyprovider.impl;

import com.fawkes.project.tbm.common.tools.verify.VerifyTools;
import com.fawkes.project.tbm.common.tools.verify.enums.VerifyType;
import com.fawkes.project.tbm.common.tools.verify.verifypackage.LengthVerifyPackage;
import com.fawkes.project.tbm.common.tools.verify.verifyprovider.VerifyProvider;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * 长度校验
 * <AUTHOR>
 * @date 2022/6/21 16:25
 */
@Component
public class LengthVerifyProvider implements VerifyProvider {


    @Override
    public VerifyType supportedVerifyType() {
        return VerifyType.Length;
    }

    /**
     * 验证
     * @param target 验证目标
     * @return 验证结果 true:验证通过  false:验证失败
     */
    @Override
    public boolean verify(Object target) throws Exception {
        if(target == null) {
            return false;
        }

        if(!(target instanceof LengthVerifyPackage)) {
            throw new IllegalArgumentException("长度校验入参应使用 LabelVerifyPackage.valueOf 作为入参");
        }

        LengthVerifyPackage verifyPackage = (LengthVerifyPackage) target;
        if(verifyPackage.getValue() == null) {
            if(verifyPackage.getMin() == null || verifyPackage.getMin() == 0) {
                return true;
            }
        }

        if(verifyPackage.getValue() instanceof CharSequence) {
            CharSequence charSequence = (CharSequence) verifyPackage.getValue();
            return VerifyTools.lengthVerify(charSequence.length(), verifyPackage.getMin(), verifyPackage.getMax());

        } else if(verifyPackage.getValue() instanceof Collection) {
            Collection<?> collection = (Collection<?>) verifyPackage.getValue();
            return VerifyTools.lengthVerify(collection.size(), verifyPackage.getMin(), verifyPackage.getMax());

        } else if(verifyPackage.getValue() instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) verifyPackage.getValue();
            return VerifyTools.lengthVerify(map.size(), verifyPackage.getMin(), verifyPackage.getMax());

        } else if(verifyPackage.getValue().getClass().isArray()) {
            Object[] array = (Object[]) verifyPackage.getValue();
            return VerifyTools.lengthVerify(array.length, verifyPackage.getMin(), verifyPackage.getMax());

        } else {
            throw new IllegalArgumentException("长度校验仅支持 CharSequence, Collection, Map, 数组");
        }
    }




}
