package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * TBM推进系统参数(此表为原始表，数据按月分表)
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tbm_propulsion_system")
@ApiModel("TBM推进系统参数(此表为原始表，数据按月分表)")
public class TbmPropulsionSystem implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 推力/Double/kN
     */
    @ApiModelProperty("推力/Double/kN")
    private BigDecimal thrust;

    /**
     * 推进速度/Double/mm/M
     */
    @ApiModelProperty("推进速度/Double/mm/M")
    private BigDecimal propSpeed;

    /**
     * 油缸压力/bar
     */
    @ApiModelProperty("油缸压力/bar")
    private BigDecimal cylinderPressure;

    /**
     * 油箱温度/Double/℃
     */
    @ApiModelProperty("油箱温度/Double/℃")
    private BigDecimal oilTemp;

    /**
     * 环数
     */
    @ApiModelProperty("环数")
    private Integer ringNum;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Timestamp updateTime;

    /** 油缸压力1# */
    private BigDecimal cylinderPressureOne;

    /** 油缸压力2# */
    private BigDecimal cylinderPressureTwo;

    /** 油缸压力3# */
    private BigDecimal cylinderPressureThree;

    /** 油缸压力4# */
    private BigDecimal cylinderPressureFour;

    /** 油缸压力5# */
    private BigDecimal cylinderPressureFive;

    /** 油缸压力6# */
    private BigDecimal cylinderPressureSix;

}
