package com.fawkes.project.tbm.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fawkes.project.tbm.common.model.TbmEarthSupport;
import com.fawkes.project.tbm.common.vo.TbmXYVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * TBM土仓压力参数表(此表为原始表，数据按月分表) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface TbmEarthSupportMapper extends BaseMapper<TbmEarthSupport> {

    /**
     * 获取最后十个环号对应的数据
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     * @param position 位置
     * @param tableName 表名
     */
    List<TbmXYVO> getRingNumTbmEarthSupport(@Param("deviceCode") String deviceCode,
                                            @Param("ringNumList") List<Integer> ringNumList,
                                            @Param("position") String position,
                                            @Param("tableName") String tableName);

    /**
     * 是否存在表
     *
     * @param tableName
     * @return
     */
    int existTable(@Param("tableName") String tableName);

    /**
     * 删除表
     *
     * @param tableName
     * @return
     */
    int dropTable(@Param("tableName") String tableName);

    /**
     * 创建表
     *
     * @param tableName
     * @return
     */
    int createTable(@Param("tableName") String tableName);
}
