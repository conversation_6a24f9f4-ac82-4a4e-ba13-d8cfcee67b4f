package com.fawkes.project.tbm.common.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version ToolInfoParam 2025/6/13 10:11
 */
@Data
public class ToolInfoParam implements Serializable {

    /** ID */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /** 设备编码 */
    private String section;
    /** 刀具种类 */
    private String toolName;
    /** 位置 */
    private String toolLocation;
    /** 次数 */
    private String changeToolNum;
    /** 原因 */
    private String changeToolReason;
    /** 图片 */
    private String changeToolPicture;

    /**
     * 图片字节流f8s
     */
    private String changeToolPictureF8s;
}
