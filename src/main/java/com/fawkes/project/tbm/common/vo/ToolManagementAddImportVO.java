package com.fawkes.project.tbm.common.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fawkes.project.tbm.common.tools.verify.config.VerifyConfig;
import com.fawkes.project.tbm.common.tools.verify.domain.IErrorMsg;
import com.fawkes.project.tbm.common.tools.verify.domain.ISerialNumber;
import lombok.Data;


@Data
@ExcelIgnoreUnannotated
public class ToolManagementAddImportVO implements ISerialNumber, IErrorMsg {

    /**序号*/
    private Integer serialNumber;

    /**错误消息*/
    private String errorMsg;

    /**区段*/
    @ExcelProperty(value = "*区段", index = 0)
    private String sectionName;

    /**区段code*/
    private String section;

    /**环号*/
    @ExcelProperty(value = "*环号", index = 1)
    private String ringNum;

    /**开始方位*/
    @ExcelProperty(value = "*开始方位", index = 2)
    private String startOrientation;

    /**开始里程*/
    @ExcelProperty(value = "*开始里程", index = 3)
    private String startMileage;

    /**结束方位**/
    @ExcelProperty(value = "*结束方位", index = 4)
    private String endOrientation;

    /**结束里程*/
    @ExcelProperty(value = "*结束里程", index = 5)
    private String endMileage;

    /**开仓开始时间*/
    @ExcelProperty(value = "*开仓开始时间", index = 6)
    private String openWarehouseStartTime;

    /**开仓结束时间*/
    @ExcelProperty(value = "*开仓结束时间", index = 7)
    private String openWarehouseEndTime;


    /**刀具名称id*/
    private Long toolNameManageId;

    /**刀具名称*/
    @ExcelProperty(value = "*刀具名称", index = 8)
    private String toolName;

    /**刀具位置id*/
    private Long toolPositionManageId;

    /**刀具位置*/
    @ExcelProperty(value = "*刀具位置", index = 9)
    private String toolLocation;

    /**更换阈值*/
    @ExcelProperty(value = "*更换阈值", index = 10)
    private String changeThreshold;

    /**磨损值*/
    @ExcelProperty(value = "*磨损值", index = 11)
    private String wearValue;

    /**换刀原因*/
    @ExcelProperty(value = "换刀原因", index = 12)
    private String changeToolReason;

    /**是否换刀（true-是，false-否）*/
    private boolean hasToolChanged;

    /**新装刀具状态*/
    @ExcelProperty(value = "新装刀具状态", index = 13)
    private String newlyInstalledCuttingToolsStatus;

    /**
     * 获取校验配置
     *
     * @return 校验配置
     */
    public static VerifyConfig<ToolManagementAddImportVO> getVerifyConfig() {
        return new VerifyConfig<ToolManagementAddImportVO>();
    }
}
