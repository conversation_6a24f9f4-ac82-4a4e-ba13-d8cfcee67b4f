package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@EqualsAndHashCode
@ToString
public class BaseModel implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;


    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;


    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Timestamp createDate;

    @ApiModelProperty("更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Timestamp updateDate;

    @ApiModelProperty("数据状态 0可用 -1不可用")
    @TableField(fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleteFlag;
}
