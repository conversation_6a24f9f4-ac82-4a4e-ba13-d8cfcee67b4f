package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 刀具位置vo
 */
@Data
public class ToolPositionManageVO {


    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 刀具名称id
     */
    @ApiModelProperty("刀具名称id")
    private String toolNameManageId;

    /**
     * 刀具位置
     */
    @ApiModelProperty("刀具位置")
    private String toolPosition;

    /**
     * 区段
     */
    @ApiModelProperty("区段")
    private String section;
}
