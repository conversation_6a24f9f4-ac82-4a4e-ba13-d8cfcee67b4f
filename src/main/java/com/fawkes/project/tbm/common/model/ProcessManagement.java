package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fawkes.core.base.model.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

@TableName("process_management")
@Data
public class ProcessManagement extends BaseEntity implements Serializable {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.section
     *
     * @mbggenerated
     */
    private String section;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.create_name
     *
     * @mbggenerated
     */
    private String createName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.update_name
     *
     * @mbggenerated
     */
    private String updateName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.ring_num
     *
     * @mbggenerated
     */
    private Integer ringNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.assembly_point
     *
     * @mbggenerated
     */
    private BigDecimal assemblyPoint;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.tunneling_start_time
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp tunnelingStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.tunneling_end_time
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp tunnelingEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.change_steps_start_time
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp changeStepsStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.change_steps_end_time
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp changeStepsEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.segment_assembly_start_time
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp segmentAssemblyStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.segment_assembly_end_time
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp segmentAssemblyEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.others_job_start_time
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp othersJobStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.others_job_end_time
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp othersJobEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.common_maintenance_and_repair_start_time
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp commonMaintenanceAndRepairStartTime;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp commonMaintenanceAndRepairEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.shutdown_maintenance_and_repair_end_time
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp shutdownMaintenanceAndRepairEndTime;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp shutdownMaintenanceAndRepairStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.open_warehouse_change_tool_start_time
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp openWarehouseChangeToolStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.open_warehouse_change_tool_end_time
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Timestamp openWarehouseChangeToolEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.open_warehouse_change_tool_num
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer openWarehouseChangeToolNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.description_of_abnormal_situations
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String descriptionOfAbnormalSituations;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.attachment
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String attachment;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.remark
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.process_name
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String processName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column process_management.description_of_construction_situation
     *
     * @mbggenerated
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String descriptionOfConstructionSituation;
}