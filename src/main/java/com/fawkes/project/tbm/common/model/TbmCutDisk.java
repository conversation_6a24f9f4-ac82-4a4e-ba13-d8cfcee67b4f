package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * TBM刀盘系统参数表(此表为原始表，数据按月分表)
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tbm_cut_disk")
@ApiModel("TBM刀盘系统参数表(此表为原始表，数据按月分表)")
public class TbmCutDisk implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 刀盘扭矩/Double/kN.M
     */
    @ApiModelProperty("刀盘扭矩/Double/kN.M")
    private BigDecimal cutDiskTorchque;

    /**
     * 刀盘转速/Double/rpm
     */
    @ApiModelProperty("刀盘转速/Double/rpm")
    private BigDecimal actualRpm;

    /**
     * 贯入度/Double/mm/rev
     */
    @ApiModelProperty("贯入度/Double/mm/rev")
    private BigDecimal penetration;

    /**
     * 环数
     */
    @ApiModelProperty("环数")
    private Integer ringNum;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Timestamp updateTime;

}