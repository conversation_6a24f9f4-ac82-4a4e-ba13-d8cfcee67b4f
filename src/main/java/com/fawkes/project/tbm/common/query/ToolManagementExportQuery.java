package com.fawkes.project.tbm.common.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel("刀具管理导出")
public class ToolManagementExportQuery {

    /** 工序管理导出ID */
    @ApiModelProperty("刀具管理导出ID")
    @NotEmpty(message = "刀具管理ID不能为空")
    private List<Long> exportIdList;
}
