package com.fawkes.project.tbm.common.tools.verify.verifyprovider.impl;

import com.fawkes.project.tbm.common.tools.verify.enums.VerifyType;
import com.fawkes.project.tbm.common.tools.verify.verifyprovider.VerifyProvider;
import org.springframework.stereotype.Component;

/**
 * 输入为False验证提供者
 * <AUTHOR>
 */
@Component
public class IsFalseVerifyProvider implements VerifyProvider {

	/**
	 * 支持的验证类型
	 * @return 支持的验证类型
	 */
	@Override
	public VerifyType supportedVerifyType() {
		return VerifyType.IsFalse;
	}

	/**
	 * 验证
	 * @param target 验证目标
	 * @return 验证结果 true:验证通过  false:验证失败
	 */
	@Override
	public boolean verify(Object target) throws IllegalArgumentException {
		if(target == null) {
			return false;
		}

		if(target instanceof Boolean == false) {
			throw new IllegalArgumentException("只有布尔值才能使用 VerifyType.IsTrue 验证");
		}
		return (boolean) target == false;
	}
}
