package com.fawkes.project.tbm.common.vo;

import com.fawkes.project.tbm.common.utils.excel.Excel;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ToolManagementExportVO {

    /**
     * 区段名称
     */
    @Excel(name = "区段", sort = 0)
    private String sectionName;

    /**
     * 环号
     */
    @Excel(name = "环号", sort = 1)
    private Integer ringNum;

    /**
     * 里程
     */
    @Excel(name = "里程", sort = 2)
    private String mileage;

    /**
     * 开仓开始时间
     */
    @Excel(name = "开仓开始时间", sort = 3)
    private String openWarehouseStartTimeDesc;

    /**
     * 开仓结束时间
     */
    @Excel(name = "开仓结束时间", sort = 4)
    private String openWarehouseEndTimeDesc;

    /**
     * 刀具名称
     */
    @Excel(name = "刀具名称", sort = 5)
    private String toolName;

    /**
     * 刀具位置
     */
    @Excel(name = "刀具位置", sort = 6)
    private String toolLocation;

    /**
     * 更换阈值
     */
    @Excel(name = "更换阈值", sort = 7)
    private BigDecimal changeThreshold;

    /**
     * 磨损值
     */
    @Excel(name = "磨损值", sort = 8)
    private BigDecimal wearValue;

    @Excel(name = "是否换刀", sort = 9)
    private String hasToolChangedValue;

    /**
     * 换刀原因
     */
    @Excel(name = "换刀原因", sort = 10)
    private String changeToolReason;

    /**
     * 新装刀具状态
     */
    @Excel(name = "新装刀具状态", sort = 11)
    private String newlyInstalledCuttingToolsStatus;

    /**
     * 换刀图片
     */
//    @Excel(name = "换刀图片", sort = 12, cellType = Excel.ColumnType.IMAGE, imageStorageLocation = ImageStorageLocation.FAWKES)
    @Excel(name = "换刀图片", sort = 12)
    private String changeToolPicture;
}