package com.fawkes.project.tbm.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fawkes.project.tbm.common.model.StatisticsComprehensiveIndex;
import com.fawkes.project.tbm.common.vo.MonthAdvanceVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 综合指标统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-02
 */
public interface StatisticsComprehensiveIndexMapper extends BaseMapper<StatisticsComprehensiveIndex> {

    List<MonthAdvanceVO> listMonthPropulsionFootage(@Param("deviceCode") String deviceCode, @Param("monthList") List<String> monthList);
}
