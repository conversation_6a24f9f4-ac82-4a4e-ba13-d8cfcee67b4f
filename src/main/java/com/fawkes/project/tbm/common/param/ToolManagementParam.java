package com.fawkes.project.tbm.common.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@ApiModel("刀具参数")
public class ToolManagementParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("id")
    private Long id;

    /**
     * 区段
     */
    @ApiModelProperty("区段")
    @NotBlank(message = "请选择区段")
    private String section;

    /**
     * 填报人
     */
    @ApiModelProperty("填报人")
    private String createName;

    /**
     * 刀具名称id
     */
    @ApiModelProperty("刀具名称id")
    @NotNull(message = "请选择刀具名称")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long toolNameManageId;

    /**
     * 刀具位置id
     */
    @ApiModelProperty("刀具位置id")
    @NotNull(message = "请选择刀具位置")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long toolPositionManageId;


    /**
     * 环号
     */
    @ApiModelProperty("环号")
    @NotNull(message = "请填写环号")
    @Digits(integer = 8, fraction = 0, message = "环号不能超过8位数字")
    private Integer ringNum;

    /**
     * 磨损值
     */
    @ApiModelProperty("磨损值")
    @DecimalMin(value = "0", message = "磨损值不能小于0")
    @Digits(integer = 5, fraction = 3, message = "磨损值整数部分不能超过5位，小数部分不能超过3位")
    private BigDecimal wearValue;

    /**
     * 开始方位
     */
    @ApiModelProperty("开始方位")
    private String startOrientation;

    /**
     * 开始里程
     */
    @ApiModelProperty("开始里程")
    @Digits(integer = 12, fraction = 3, message = "开始里程整数位不能超过12位，小数位不能超过3位")
    @PositiveOrZero(message = "开始里程必须 ≥ 0")
    private BigDecimal startMileage;

    /**
     * 结束方位
     */
    @ApiModelProperty("结束方位")
    private String endOrientation;

    /**
     * 结束里程
     */
    @ApiModelProperty("结束里程")
    @Digits(integer = 12, fraction = 3, message = "结束里程整数位不能超过12位，小数位不能超过3位")
    @PositiveOrZero(message = "结束里程必须 ≥ 0")
    private BigDecimal endMileage;

    /**
     * 开仓开始时间
     */
    @ApiModelProperty("开仓开始时间")
    @NotNull(message = "开仓开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Timestamp openWarehouseStartTime;

    /**
     * 开仓结束时间
     */
    @ApiModelProperty("开仓结束时间")
    @NotNull(message = "开仓结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Timestamp openWarehouseEndTime;

    /**
     * 新装刀具状态
     */
    @ApiModelProperty("新装刀具状态")
    private String newlyInstalledCuttingToolsStatus;

    /**
     * 更换阈值
     */
    @ApiModelProperty("更换阈值")
    @Digits(integer = 8, fraction = 3, message = "更换阈值数位不能超过8位，小数位不能超过3位")
    private BigDecimal changeThreshold;

    /**
     * 换刀原因
     */
    @ApiModelProperty("换刀原因")
    private String changeToolReason;


    /**
     * 是否换刀（true-是，false-否）
     */
    @ApiModelProperty("是否换刀（true-是，false-否）")
    private boolean hasToolChanged;

    /**
     * 附件
     */
    @ApiModelProperty("附件")
    private String attachment;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @Pattern(regexp = "^(?!.*[\\uD800-\\uDBFF][\\uDC00-\\uDFFF])[^\\uD800-\\uDFFF]{0,200}$",
            message = "不能包含表情符号，且长度不超过200个字符")
    private String remark;

    /**
     * 换刀图片
     */
    @ApiModelProperty("换刀图片")
    private String changeToolPicture;

    /**
     * 数据来源（0-PC，1-APP）
     */
    private Integer dataSource;
}