package com.fawkes.project.tbm.common.utils.excel;

import cn.hutool.core.util.ReflectUtil;
import com.fawkes.core.exception.BusinessException;
import com.fawkes.project.tbm.common.utils.*;
import com.fawkes.project.tbm.common.utils.spring.SpringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ooxml.POIXMLDocumentPart;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.openxmlformats.schemas.drawingml.x2006.spreadsheetDrawing.CTMarker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.server.ServerErrorException;
import rx.functions.Action2;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.Color;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel相关处理
 * <AUTHOR>
 */
public class ExcelUtil<T> {

    public static final String[] FORMULA_STR = {"=", "-", "+", "@"};
    /** Excel sheet最大行数，默认65536 */
    public static final int SHEET_SIZE = 65536;
    private static final Logger log = LoggerFactory.getLogger(ExcelUtil.class);
    /** 数字格式 */
    private static final DecimalFormat DOUBLE_FORMAT = new DecimalFormat("######0.00");
    /** 统计列表 */
    private final Map<Integer, Double> statistics = new HashMap<>();
    /** 实体对象 */
    public Class<T> clazz;
    /** 工作表名称 */
    private String sheetName;
    /** 工作薄对象 */
    private Workbook wb;
    /** 导出类型（EXPORT:导出数据；IMPORT：导入模板） */
    private Excel.Type type;
    /** 工作表对象 */
    private Sheet sheet;
    /** 样式列表 */
    private Map<String, CellStyle> styles;
    /** 导入导出数据列表 */
    private List<T> list;
    /** 注解列表 */
    private List<Object[]> fields;
    /** 当前行号 */
    private int rowNum;
    /** 标题 */
    private String title;
    /** 最大高度 */
    private short maxHeight;
    /** 需设置行号的字段 */
    private Field needSetRowIndexField;
    /** 动态表尾处理器 */
    private DynamicFooterHandler<T> dynamicFooterHandler;

    public ExcelUtil(Class<T> clazz) {
        this.clazz = clazz;
    }

    /**
     * 获取Excel2003图片
     * @param sheet 当前sheet对象
     * @param workbook 工作簿对象
     * @return Map key:图片单元格索引（1_1）String，value:图片流PictureData
     */
    private static Map<String, PictureData> getSheetPictures03(HSSFSheet sheet, HSSFWorkbook workbook) {
        Map<String, PictureData> sheetIndexPicMap = new HashMap<>();
        List<HSSFPictureData> pictures = workbook.getAllPictures();
        if(!pictures.isEmpty()) {
            for(HSSFShape shape : sheet.getDrawingPatriarch().getChildren()) {
                HSSFClientAnchor anchor = (HSSFClientAnchor) shape.getAnchor();
                if(shape instanceof HSSFPicture) {
                    HSSFPicture pic = (HSSFPicture) shape;
                    int pictureIndex = pic.getPictureIndex() - 1;
                    HSSFPictureData picData = pictures.get(pictureIndex);
                    String picIndex = anchor.getRow1() + "_" + String.valueOf(anchor.getCol1());
                    sheetIndexPicMap.put(picIndex, picData);
                }
            }
            return sheetIndexPicMap;
        } else {
            return sheetIndexPicMap;
        }
    }

    /**
     * 获取sheet中的最大列号
     * @param sheet sheet
     * @return 最大列号
     */
    private static int findMaxCol(Sheet sheet) {
        int lastRowNum = sheet.getLastRowNum();

        int maxCol = 0;
        for(int rowIndex = 0; rowIndex < lastRowNum; rowIndex++) {
            maxCol = Math.max(sheet.getRow(rowIndex).getLastCellNum(), maxCol);
        }
        return maxCol;
    }

    /**
     * 获取画布
     */
    private static Drawing<?> getDrawingPatriarch(Sheet sheet) {
        if(sheet.getDrawingPatriarch() == null) {
            sheet.createDrawingPatriarch();
        }
        return sheet.getDrawingPatriarch();
    }

    /**
     * 解析导出值 0=男,1=女,2=未知
     * @param propertyValue 参数值
     * @param converterExp 翻译注解
     * @param separator 分隔符
     * @return 解析后值
     */
    private static String convertByExp(String propertyValue, String converterExp, String separator) {
        StringBuilder propertyString = new StringBuilder();
        String[] convertSource = converterExp.split(",");
        for(String item : convertSource) {
            String[] itemArray = item.split("=");
            if(StringUtils.containsAny(separator, propertyValue)) {
                for(String value : propertyValue.split(separator)) {
                    if(itemArray[0].equals(value)) {
                        propertyString.append(itemArray[1]).append(separator);
                        break;
                    }
                }
            } else {
                if(itemArray[0].equals(propertyValue)) {
                    return itemArray[1];
                }
            }
        }
        return StringUtils.stripEnd(propertyString.toString(), separator);
    }

    /**
     * 反向解析值 男=0,女=1,未知=2
     * @param propertyValue 参数值
     * @param converterExp 翻译注解
     * @param separator 分隔符
     * @return 解析后值
     */
    private static String reverseByExp(String propertyValue, String converterExp, String separator) {
        StringBuilder propertyString = new StringBuilder();
        String[] convertSource = converterExp.split(",");
        for(String item : convertSource) {
            String[] itemArray = item.split("=");
            if(StringUtils.containsAny(separator, propertyValue)) {
                for(String value : propertyValue.split(separator)) {
                    if(itemArray[1].equals(value)) {
                        propertyString.append(itemArray[0]).append(separator);
                        break;
                    }
                }
            } else {
                if(itemArray[1].equals(propertyValue)) {
                    return itemArray[0];
                }
            }
        }
        return StringUtils.stripEnd(propertyString.toString(), separator);
    }


    /**
     * 获取Excel2007图片
     * @param sheet 当前sheet对象
     * @return Map key:图片单元格索引（1_1）String，value:图片流PictureData
     */
    private static Map<String, PictureData> getSheetPictures07(XSSFSheet sheet) {
        Map<String, PictureData> sheetIndexPicMap = new HashMap<>();
        for(POIXMLDocumentPart dr : sheet.getRelations()) {
            if(dr instanceof XSSFDrawing) {
                XSSFDrawing drawing = (XSSFDrawing) dr;
                List<XSSFShape> shapes = drawing.getShapes();
                for(XSSFShape shape : shapes) {
                    if(shape instanceof XSSFPicture) {
                        XSSFPicture pic = (XSSFPicture) shape;
                        XSSFClientAnchor anchor = null;
                        try {
                            anchor = pic.getClientAnchor();
                        } catch(Exception e){
                            log.error("获取图片异常: ", e);
                            throw new ServerErrorException("图片格式错误");
                        }
                        CTMarker ctMarker = anchor.getFrom();
                        String picIndex = ctMarker.getRow() + "_" + ctMarker.getCol();
                        try {
                            sheetIndexPicMap.put(picIndex, pic.getPictureData());
                        }catch(Exception e){
                            log.error("获取图片异常: ", e);
                        }
                    }
                }
            }
        }
        return sheetIndexPicMap;
    }

    private void init(List<T> list, String sheetName, String title) {
        if(list == null) {
            list = new ArrayList<>();
        }
        this.list = list;
        this.sheetName = sheetName;
        this.title = title;
        createExcelField();
        createWorkbook();
        createTitle();
    }

    private void init(List<T> list, String sheetName, String title, Excel.Type type) {
        if(list == null) {
            list = new ArrayList<>();
        }
        this.list = list;
        this.sheetName = sheetName;
        this.type = type;
        this.title = title;
        createExcelField();
        createWorkbook();
        createTitle();
    }

    /**
     * 创建excel第一行标题
     */
    private void createTitle() {
        if(StringUtils.isNotEmpty(title)) {
            Row titleRow = sheet.createRow(rowNum == 0 ? rowNum++ : 0);
            titleRow.setHeightInPoints(30);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellStyle(styles.get("title"));
            titleCell.setCellValue(title);
            sheet.addMergedRegion(new CellRangeAddress(titleRow.getRowNum(), titleRow.getRowNum(), titleRow.getRowNum(),
                    this.fields.size() - 1));
        }
    }

    /**
     * 对excel表单默认第一个索引名转换成list
     * @param is 输入流
     * @param titleNum 标题占用行数
     * @return 转换后集合
     */
    public List<T> importExcel(InputStream is, int titleNum) throws Exception {
        return importExcel(StringUtils.EMPTY, is, titleNum);
    }

    /**
     * 判断sheet页是否存在
     * @param sheetName 表格名
     * @param is 输入流
     * @return true 不存在，false 存在
     */
    public Boolean isSheetName(String sheetName, InputStream is) throws Exception{
        Sheet sheet = WorkbookFactory.create(is).getSheet(sheetName);
        return sheet == null;
    }


    /**
     * 对excel表单指定表格索引名转换成list
     * @param sheetName 表格索引名
     * @param titleNum 标题占用行数
     * @param is 输入流
     * @return 转换后集合
     */
    public List<T> importExcel(String sheetName, InputStream is, int titleNum) throws Exception {
        this.wb = WorkbookFactory.create(is);
        // 如果指定sheet名,则取指定sheet中的内容 否则默认指向第1个sheet
        Sheet sheet = StringUtils.isNotEmpty(sheetName) ? wb.getSheet(sheetName) : wb.getSheetAt(0);
        if(sheet == null) {
            return ListUtil.emptyList();
        }
        boolean isXSSFWorkbook = !(wb instanceof HSSFWorkbook);
        Map<String, PictureData> pictures;
        if(isXSSFWorkbook) {
            pictures = getSheetPictures07((XSSFSheet) sheet);
        } else {
            pictures = getSheetPictures03((HSSFSheet) sheet, (HSSFWorkbook) wb);
        }
        // 获取最后一个非空行的行下标，比如总行数为n，则返回的为n-1
        int rows = sheet.getLastRowNum();

        List<T> list = new ArrayList<>();

        // 定义一个map用于存放excel列的序号和field.
        Map<String, Integer> cellMap = this.initCellMap(titleNum, sheet);
        // 有数据时才处理 得到类的所有field.
        Map<Integer, Object[]> fieldsMap = initColumnFieldMap(cellMap, sheetName);

        if(rows <= 0) {
            return ListUtil.emptyList();
        }
        for(int i = titleNum + 1; i <= rows; i++) {
            // 从第2行开始取数据,默认第一行是表头.
            Row row = sheet.getRow(i);
            // 判断当前行是否是空行
            if(isRowEmpty(row)) {
                continue;
            }
            T entity = this.getEntity(row, fieldsMap, pictures);
            if(this.needHandleDynamicFooter(row)) {
                this.handleDynamicFooter(entity, row);
            }

            list.add(entity);
        }
        return list;
    }

    /**
     * 对excel表单指定表格索引名转换成list（只一行，不校验模板）(计量管理表四专用)
     * @param sheetName 表格索引名
     * @param titleNum 标题占用行数
     * @param is 输入流
     * @return 转换后集合
     */
    public String importNullForm(String sheetName, InputStream is, int titleNum) throws Exception {
        this.wb = WorkbookFactory.create(is);
        // 如果指定sheet名,则取指定sheet中的内容 否则默认指向第1个sheet
        Sheet sheet = StringUtils.isNotEmpty(sheetName) ? wb.getSheet(sheetName) : wb.getSheetAt(0);
        if(sheet == null) {
            return null;
        }
        // 定义一个map用于存放excel列的序号和field.
        Map<String, Integer> cellMap = this.initCellMapSpecifiedLines(titleNum, sheet);
        Set<String> stringSet = cellMap.keySet();
        if(!stringSet.contains("单位/子单位工程")){
            throw new BusinessException("单位/子单位工程 在模板中未匹配到");
        }
//        if(!stringSet.contains("！提示：不要调整表格宽度")){
//            throw new BusinessException("！提示：不要调整表格宽度 在模板中未匹配到");
//        }
        stringSet.remove("单位/子单位工程");
        stringSet.remove("！提示：不要调整表格宽度");
        stringSet.remove("");
        return ListUtil.first(stringSet);
    }

    /**
     * 处理动态表尾
     * @param entity 该行解析出的实体
     * @param row 需处理动态表尾的行
     */
    private void handleDynamicFooter(T entity, Row row) throws Exception {
        entity = (entity == null ? clazz.newInstance() : entity);

        int dynamicFooterIndex = this.dynamicFooterHandler.getDynamicFooterIndex();
        short lastCellNum = row.getLastCellNum();

        List<String> dynamicFooterList = new ArrayList<>(lastCellNum - dynamicFooterIndex);
        for(int index = dynamicFooterIndex; index < lastCellNum; index++) {
            Object cellValue = this.getCellValue(row, index);
            dynamicFooterList.add(NullMergeUtils.nullMerge(cellValue, Object::toString));
        }
        this.dynamicFooterHandler.handle(entity, dynamicFooterList);
    }

    /**
     * 该行是否需要处理动态表尾
     * @param row 数据行
     * @return true: 需要处理动态表尾
     */
    private boolean needHandleDynamicFooter(Row row) {
        if(this.dynamicFooterHandler == null) {
            return false;
        }

        return row.getLastCellNum() > this.dynamicFooterHandler.getDynamicFooterIndex();
    }

    /**
     * 初始化列号字段对应Map
     * @param cellMap 单元格列表对应Map
     * @param sheetName sheet名
     * @return (列号, 对应导出元数据)
     */
    private Map<Integer, Object[]> initColumnFieldMap(Map<String, Integer> cellMap, String sheetName) {
        List<String> errorList = ListUtil.emptyList();
        Map<Integer, Object[]> fieldsMap = new HashMap<>();
        List<Object[]> fields = this.getFields();
        for(Object[] objects : fields) {
            Excel attr = (Excel) objects[1];
            if(attr.setRowIndex()) {
                this.needSetRowIndexField = (Field) objects[0];
                continue;
            }

            Integer column = cellMap.get(attr.name());
            if(column != null) {
                fieldsMap.put(column, objects);
            }else{
                errorList.add(attr.name());
            }
        }

        if (ListUtil.any(errorList)){
            if(StringUtils.isBlank(sheetName)){
                throw new BusinessException(String.format(" %s 在模板中未匹配到",String.join(",", errorList)));
            }else {
                throw new BusinessException(String.format("在 %s 中未匹配到 %s", sheetName,String.join(",", errorList)));
            }
        }

        return fieldsMap;
    }

    /**
     * 从行中解析实体
     * @param row 表格行
     * @param fieldsMap 列号字段对应Map
     * @param pictures 图片Map
     * @return 解析的实体
     */
    private T getEntity(Row row, Map<Integer, Object[]> fieldsMap, Map<String, PictureData> pictures)
            throws Exception {
        T entity = null;
        for(Map.Entry<Integer, Object[]> entry : fieldsMap.entrySet()) {
            // 从map中得到对应列的field.
            Field field = (Field) entry.getValue()[0];
            Excel attr = (Excel) entry.getValue()[1];
            int fieldColumn = entry.getKey();
            entity = (entity == null ? clazz.newInstance() : entity);

            Object val = this.getFieldValue(row, fieldColumn, field);

            //校验传入的值是否为空或者为/或者为--设置为空
            if (val instanceof String){
                String value = (String) val;
                value = value.replaceAll("\r\n|\r|\n", "");
                value = value.trim();
                if (StringUtils.isBlank(value) || "/".equals(val) || "--".equals(val)){
                    val = "";
                }else{
                    val = value;
                }
            }

            String propertyName = field.getName();
            if(StringUtils.isNotEmpty(attr.targetAttr())) {
                propertyName = field.getName() + "." + attr.targetAttr();
            } else if(StringUtils.isNotEmpty(attr.readConverterExp())) {
                val = reverseByExp(Convert.toStr(val), attr.readConverterExp(), attr.separator());
            } else if(!attr.handler().equals(ExcelHandlerAdapter.class)) {
                val = dataFormatHandlerAdapter(val, attr);
            }
            ReflectUtils.invokeSetter(entity, propertyName, val);

            if(StringUtils.isNotBlank(attr.cellDataFormat())){
                try {
                    Cell cell = row.getCell(fieldColumn);
                    ReflectUtils.invokeSetter(entity, attr.cellDataFormat(), cell.getCellStyle().getDataFormat());
                }catch(Exception e){
                    log.error(String.format("特殊字段处理错误类：%s , 字段：%s",clazz.getName(),field.getName()));
                }
            }
        }
        if(entity != null && this.needSetRowIndexField != null) {
            ReflectUtil.setFieldValue(entity, this.needSetRowIndexField, row.getRowNum());
        }

        return entity;
    }


    /**
     * 获取行中字段对应的值
     * @param row 表格行
     * @param fieldColumn 字段列号
     * @param field 字段
     * @return 字段在该行中的值
     */
    private Object getFieldValue(Row row, Integer fieldColumn, Field field) {
        Object val = this.getCellValue(row, fieldColumn);
        // 如果不存在实例则新建.
        // 取得类型,并根据对象类型设置值.
        Class<?> fieldType = field.getType();
        if(String.class == fieldType) {
            String s = Convert.toStr(val);
            if(StringUtils.endsWith(s, ".0")) {
                val = StringUtils.substringBefore(s, ".0");
            } else {
                String dateFormat = field.getAnnotation(Excel.class).dateFormat();
                if(StringUtils.isNotEmpty(dateFormat)) {
                    val = parseDateToStr(dateFormat, val);
                } else {
                    val = Convert.toStr(val);
                }
            }
        } else if((Integer.TYPE == fieldType || Integer.class == fieldType) && StringUtils.isNumeric(Convert.toStr(val))) {
            val = Convert.toInt(val);
        } else if((Long.TYPE == fieldType || Long.class == fieldType) && StringUtils.isNumeric(Convert.toStr(val))) {
            val = Convert.toLong(val);
        } else if(Double.TYPE == fieldType || Double.class == fieldType) {
            val = Convert.toDouble(val);
        } else if(Float.TYPE == fieldType || Float.class == fieldType) {
            val = Convert.toFloat(val);
        } else if(BigDecimal.class == fieldType) {
            val = Convert.toBigDecimal(val);
        } else if(Date.class == fieldType) {
            if(val instanceof String) {
                val = DateUtils.parseDate(val);
            } else if(val instanceof Double) {
                val = DateUtil.getJavaDate((Double) val);
            }
        } else if(LocalDate.class == fieldType) {
            if(val instanceof String) {
                val = DateUtils.toLocalDate(DateUtils.parseDate(val));
            } else if(val instanceof Double) {
                val = DateUtils.toLocalDate(DateUtil.getJavaDate((Double) val));
            } else if(val instanceof Date) {
                val = DateUtils.toLocalDate((Date) val);
            }
        } else if(Boolean.TYPE == fieldType || Boolean.class == fieldType) {
            val = Convert.toBool(val, false);
        }else  if (LocalDateTime.class == fieldType){
            if (val instanceof String) {
                val = DateUtils.toLocalDateTime((String) val);
            }else {
                log.warn("excel当前导入字段为LocalDateTime转义发生错误");
            }

        }
        return val;
    }

    /**
     * 初始化一个map用于存放excel列的序号和field
     * @param titleNum 标题占用行数
     * @param sheet 表格Sheet名
     * @return (表头名称, 列号)
     */
    private Map<String, Integer> initCellMap(int titleNum, Sheet sheet) {
        Map<String, Integer> cellMap = new HashMap<>();
        // 获取表头
        Row heard = sheet.getRow(titleNum);
        for(int i = 0; i < heard.getPhysicalNumberOfCells(); i++) {
            Cell cell = heard.getCell(i);
            if(cell != null) {
                String value = this.getCellValue(heard, i).toString();
                cellMap.put(value, i);
            } else {
                cellMap.put(null, i);
            }
        }
        return cellMap;
    }

    /**
     * 初始化一个map用于存放excel列的序号和field
     * @param titleNum 标题占用行数
     * @param sheet 表格Sheet名
     * @return (表头名称, 列号)
     */
    private Map<String, Integer> initCellMapSpecifiedLines(int titleNum, Sheet sheet) {
        Map<String, Integer> cellMap = new HashMap<>();
        // 获取指定行
        Row heard = sheet.getRow(titleNum);
        for(int i = 0; i < heard.getPhysicalNumberOfCells(); i++) {
            Cell cell = heard.getCell(i);
            if(cell != null) {
                String value = this.getCellValue(heard, i).toString();
                cellMap.put(value, i);
            } else {
                cellMap.put(null, i);
            }
        }
        return cellMap;
    }

    /**
     * 创建表格样式
     * @param wb 工作薄对象
     * @return 样式列表
     */
    private Map<String, CellStyle> createStyles(Workbook wb) {
        // 写入各条记录,每条记录对应excel表中的一行
        Map<String, CellStyle> styles = new HashMap<>();
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = wb.createFont();
        titleFont.setFontName("Arial");
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setBold(true);
        style.setFont(titleFont);
        styles.put("title", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        Font dataFont = wb.createFont();
        dataFont.setFontName("Arial");
        dataFont.setFontHeightInPoints((short) 10);
        style.setFont(dataFont);
        styles.put("data", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font headerFont = wb.createFont();
        headerFont.setFontName("Arial");
        headerFont.setFontHeightInPoints((short) 10);
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(headerFont);
        styles.put("header", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font totalFont = wb.createFont();
        totalFont.setFontName("Arial");
        totalFont.setFontHeightInPoints((short) 10);
        style.setFont(totalFont);
        styles.put("total", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.LEFT);
        styles.put("data1", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.CENTER);
        styles.put("data2", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.RIGHT);
        styles.put("data3", style);

        return styles;
    }

    /**
     * 创建单元格
     */
    private Cell createCell(Excel attr, Row row, int column) {
        // 创建列
        Cell cell = row.createCell(column);
        // 写入列信息
        cell.setCellValue(attr.name());
        setDataValidation(attr, row, column);
        cell.setCellStyle(styles.get("header"));
        return cell;
    }

    /**
     * 设置单元格信息
     * @param value 单元格值
     * @param attr 注解相关
     * @param cell 单元格信息
     */
    private void setCellVo(Object value, Excel attr, Cell cell) {
        if(Excel.ColumnType.STRING == attr.cellType()) {
            String cellValue = Convert.toStr(value);
            // 对于任何以表达式触发字符 =-+@开头的单元格，直接使用tab字符作为前缀，防止CSV注入。
            if(StringUtils.isNotBlank(cellValue) && cellValue.startsWith("=")){
                cellValue = StringUtils.replaceEach(cellValue, FORMULA_STR, new String[]{"\t=", "\t-", "\t+", "\t@"});
            }
            cell.setCellValue(cellValue == null ? attr.defaultValue() : cellValue + attr.suffix());
            if (StringUtils.isBlank(cellValue)){
                cell.setCellValue("--");
            }
        } else if(Excel.ColumnType.NUMERIC == attr.cellType()) {
            if(value != null) {
                cell.setCellValue(StringUtils.contains(Convert.toStr(value), ".") ? Convert.toDouble(value) : Convert.toInt(value));
            }else{
                cell.setCellValue("--");
            }
        }else if(Excel.ColumnType.IMAGE == attr.cellType()) {
            ClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) cell.getColumnIndex(), cell.getRow().getRowNum(), (short) (cell.getColumnIndex() + 1), cell.getRow().getRowNum() + 1);
            anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);
            String imagePath = Convert.toStr(value);
            if(StringUtils.isNotEmpty(imagePath)) {
                byte[] data;
                switch(attr.imageStorageLocation()){
                    case FAWKES:
                        data = getImageDataByFawkes(imagePath);
                        break;
                    default:
                        throw new UnsupportedOperationException(String.format("枚举: %s对应操作未实现", attr.imageStorageLocation()));
                }

                getDrawingPatriarch(cell.getSheet())
                        .createPicture(anchor, cell.getSheet().getWorkbook().addPicture(data, 6));
            }
        }
    }

    /**
     * 获取图片类型,设置图片插入类型
     */
    private int getImageType(byte[] value) {
        String type = FileTypeUtils.getFileExtendName(value);
        if("JPG".equalsIgnoreCase(type)) {
            return Workbook.PICTURE_TYPE_JPEG;
        } else if("PNG".equalsIgnoreCase(type)) {
            return Workbook.PICTURE_TYPE_PNG;
        }
        return Workbook.PICTURE_TYPE_JPEG;
    }

    /**
     * 通过凤翎获取图片字节流
     * @param imagePath 图片OssKey
     * @return 图片字节流
     */
    private byte[] getImageDataByFawkes(String imagePath) {
        TransferFileTool transferFileTool = SpringUtils.getBean(TransferFileTool.class);
        return transferFileTool.download(imagePath);
    }

    /**
     * 填充excel数据
     * @param index 序号
     * @param row 单元格行
     */
    private void fillExcelData(int index, Row row) {
        int startNo = index * SHEET_SIZE;
        int endNo = Math.min(startNo + SHEET_SIZE, list.size());
        for(int i = startNo; i < endNo; i++) {
            row = sheet.createRow(i + 1 + rowNum - startNo);
            // 得到导出对象.
            T vo = list.get(i);
            int column = 0;
            for(Object[] os : fields) {
                Field field = (Field) os[0];
                Excel excel = (Excel) os[1];
                this.addCell(excel, row, vo, field, column++);
            }
        }
    }

    /**
     * 创建表格样式
     */
    private void setDataValidation(Excel attr, Row row, int column) {
        if(attr.name().contains("注：")) {
            sheet.setColumnWidth(column, 6000);
        } else {
            // 设置列宽
            sheet.setColumnWidth(column, (int) ((attr.width() + 0.72) * 256));
        }
        // 如果设置了提示信息则鼠标放上去提示.
        if(StringUtils.isNotEmpty(attr.prompt())) {
            // 这里默认设了2-101列提示.
            setXSSFPrompt(sheet, "", attr.prompt(), 1, 100, column, column);
        }
        // 如果设置了combo属性则本列只能选择不能输入
        if(attr.combo().length > 0) {
            // 这里默认设了2-101列只能选择不能输入.
            setXSSFValidation(sheet, attr.combo(), 1, 100, column, column);
        }
    }

    /**
     * 设置 POI XSSFSheet 单元格提示
     * @param sheet 表单
     * @param promptTitle 提示标题
     * @param promptContent 提示内容
     * @param firstRow 开始行
     * @param endRow 结束行
     * @param firstCol 开始列
     * @param endCol 结束列
     */
    private void setXSSFPrompt(Sheet sheet, String promptTitle, String promptContent, int firstRow, int endRow,
                               int firstCol, int endCol) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createCustomConstraint("DD1");
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        dataValidation.createPromptBox(promptTitle, promptContent);
        dataValidation.setShowPromptBox(true);
        sheet.addValidationData(dataValidation);
    }

    /**
     * 设置某些列的值只能输入预制的数据,显示下拉框.
     * @param sheet 要设置的sheet.
     * @param textlist 下拉框显示的内容
     * @param firstRow 开始行
     * @param endRow 结束行
     * @param firstCol 开始列
     * @param endCol 结束列
     */
    private void setXSSFValidation(Sheet sheet, String[] textlist, int firstRow, int endRow, int firstCol, int endCol) {
        DataValidationHelper helper = sheet.getDataValidationHelper();
        // 加载下拉列表内容
        DataValidationConstraint constraint = helper.createExplicitListConstraint(textlist);
        // 设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        // 数据有效性对象
        DataValidation dataValidation = helper.createValidation(constraint, regions);
        // 处理Excel兼容性问题
        if(dataValidation instanceof XSSFDataValidation) {
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }

        sheet.addValidationData(dataValidation);
    }

    /** 添加单元格 */
    public Cell addCell(Excel attr, Row row, T vo, Field field, int column) {
        Cell cell = null;
        try {
            // 设置行高
            row.setHeight(maxHeight);
            // 根据Excel中设置情况决定是否导出,有些情况需要保持为空,希望用户填写这一列.
            if(attr.isExport()) {
                // 创建cell
                cell = row.createCell(column);
                int align = attr.align().value();
                cell.setCellStyle(styles.get("data" + (align >= 1 && align <= 3 ? align : "")));

                // 用于读取对象中的属性
                Object value = getTargetValue(vo, field, attr);
                String dateFormat = attr.dateFormat();
                String readConverterExp = attr.readConverterExp();
                String separator = attr.separator();
                String dictType = attr.dictType();
                if(StringUtils.isNotEmpty(dateFormat) && value  != null) {
                    cell.setCellValue(parseDateToStr(dateFormat, value));
                } else if(StringUtils.isNotEmpty(readConverterExp) && value  != null) {
                    cell.setCellValue(convertByExp(Convert.toStr(value), readConverterExp, separator));
                } else if(value instanceof BigDecimal && -1 != attr.scale()) {
                    cell.setCellValue((((BigDecimal) value).setScale(attr.scale(), attr.roundingMode())).toString());
                } if(!attr.handler().equals(ExcelHandlerAdapter.class)) {
                    cell.setCellValue(dataFormatHandlerAdapter(value, attr));
                } else {
                    // 设置列类型
                    setCellVo(value, attr, cell);
                }
                addStatisticsData(column, Convert.toStr(value), attr);
            }
        } catch(Exception e) {
            log.error("导出Excel失败: ", e);
        }
        return cell;
    }

    /** 合计统计信息 */
    private void addStatisticsData(Integer index, String text, Excel entity) {
        if(entity != null && entity.isStatistics()) {
            Double temp = 0D;
            if(!statistics.containsKey(index)) {
                statistics.put(index, temp);
            }
            try {
                temp = Double.valueOf(text);
            } catch(NumberFormatException ignored) {
            }
            statistics.put(index, statistics.get(index) + temp);
        }
    }

    /**
     * 创建统计行
     */
    private void addStatisticsRow() {
        if(statistics.size() > 0) {
            Row row = sheet.createRow(sheet.getLastRowNum() + 1);
            Set<Integer> keys = statistics.keySet();
            Cell cell = row.createCell(0);
            cell.setCellStyle(styles.get("total"));
            cell.setCellValue("合计");

            for(Integer key : keys) {
                cell = row.createCell(key);
                cell.setCellStyle(styles.get("total"));
                cell.setCellValue(DOUBLE_FORMAT.format(statistics.get(key)));
            }
            statistics.clear();
        }
    }

    /**
     * 数据处理器
     * @param value 数据值
     * @param excel 数据注解
     */
    private String dataFormatHandlerAdapter(Object value, Excel excel) {
        try {
            Object instance = excel.handler().newInstance();
            Method formatMethod = excel.handler().getMethod("format", Object.class, String[].class);
            value = formatMethod.invoke(instance, value, excel.args());

            if (value == null){
                value = "--";
            }
        } catch(Exception e) {
            log.error("不能格式化数据 " + excel.handler() + ":{}", e.getMessage());
        }
        return Convert.toStr(value);
    }

    /**
     * 获取bean中的属性值
     * @param vo 实体对象
     * @param field 字段
     * @param excel 注解
     * @return 最终的属性值
     */
    private Object getTargetValue(T vo, Field field, Excel excel) throws Exception {
        Object o = field.get(vo);
        if(StringUtils.isNotEmpty(excel.targetAttr())) {
            String target = excel.targetAttr();
            if(target.contains(".")) {
                String[] targets = target.split("[.]");
                for(String name : targets) {
                    o = getValue(o, name);
                }
            } else {
                o = getValue(o, target);
            }
        }
        return o;
    }

    /**
     * 以类的属性的get方法方法形式获取值
     * @return value
     */
    private Object getValue(Object o, String name) throws Exception {
        if(o != null && StringUtils.isNotEmpty(name)) {
            Class<?> clazz = o.getClass();
            Field field = clazz.getDeclaredField(name);
            field.setAccessible(true);
            o = field.get(o);
        }
        return o;
    }

    /** 得到所有定义字段 */
    private void createExcelField() {
        this.fields = getFields();
        this.fields = this.fields.stream().sorted(Comparator.comparing(objects -> ((Excel) objects[1]).sort())).collect(Collectors.toList());
        this.maxHeight = getRowHeight();
    }

    /**
     * 获取字段注解信息
     */
    private List<Object[]> getFields() {
        List<Object[]> fields = new ArrayList<>();
        List<Field> tempFields = new ArrayList<>();
        tempFields.addAll(Arrays.asList(clazz.getSuperclass().getDeclaredFields()));
        tempFields.addAll(Arrays.asList(clazz.getDeclaredFields()));
        for(Field field : tempFields) {
            // 单注解
            if(field.isAnnotationPresent(Excel.class)) {
                Excel attr = field.getAnnotation(Excel.class);
                if(attr != null && attr.type() == Excel.Type.ALL) {
                    field.setAccessible(true);
                    fields.add(new Object[]{field, attr});
                }
            }

            // 多注解
            if(field.isAnnotationPresent(Excels.class)) {
                Excels attrs = field.getAnnotation(Excels.class);
                Excel[] excels = attrs.value();
                for(Excel attr : excels) {
                    if(attr != null && attr.type() == Excel.Type.ALL) {
                        field.setAccessible(true);
                        fields.add(new Object[]{field, attr});
                    }
                }
            }
        }
        return fields;
    }

    /**
     * 根据注解获取最大行高
     */
    private short getRowHeight() {
        double maxHeight = 0;
        for(Object[] os : this.fields) {
            Excel excel = (Excel) os[1];
            maxHeight = Math.max(maxHeight, excel.height());
        }
        return (short) (maxHeight * 20);
    }

    /**
     * 创建一个工作簿
     */
    private void createWorkbook() {
        this.wb = new SXSSFWorkbook(500);
        this.sheet = wb.createSheet();
        wb.setSheetName(0, sheetName);
        this.styles = createStyles(wb);
    }

    /**
     * 创建工作表
     * @param sheetNo sheet数量
     * @param index 序号
     */
    private void createSheet(int sheetNo, int index) {
        // 设置工作表的名称.
        if(sheetNo > 1 && index > 0) {
            this.sheet = wb.createSheet();
            this.createTitle();
            wb.setSheetName(index, sheetName + index);
        }
    }

    /**
     * 获取单元格值
     * @param row 获取的行
     * @param column 获取单元格列号
     * @return 单元格值
     */
    private Object getCellValue(Row row, int column) {
        if(row == null) {
            return row;
        }
        Object val = "";
        try {
            Cell cell = row.getCell(column);
            if(cell != null) {
                if(cell.getCellType() == CellType.NUMERIC || cell.getCellType() == CellType.FORMULA) {
                    val = cell.getNumericCellValue();
                    if(DateUtil.isCellDateFormatted(cell)) {
                        val = DateUtil.getJavaDate((Double) val); // POI Excel 日期格式转换
                    } else {
                        if((Double) val % 1 != 0) {
                            val = new BigDecimal(val.toString());
                        } else {
                            val = new DecimalFormat("0").format(val);
                        }
                    }
                } else if(cell.getCellType() == CellType.STRING) {
                    val = cell.getStringCellValue();
                } else if(cell.getCellType() == CellType.BOOLEAN) {
                    val = cell.getBooleanCellValue();
                } else if(cell.getCellType() == CellType.ERROR) {
                    val = cell.getErrorCellValue();
                }

            }
        } catch(Exception e) {
            return val;
        }
        return val;
    }

    /**
     * 判断是否是空行
     * @param row 判断的行
     */
    private boolean isRowEmpty(Row row) {
        if(row == null) {
            return true;
        }
        for(int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if(cell != null && cell.getCellType() != CellType.BLANK) {
                return false;
            }
        }
        return true;
    }

    /**
     * 编码文件名
     */
    private String encodingFilename(String filename) {
        filename = UUID.randomUUID() + "_" + filename + ".xlsx";
        return filename;
    }

    /**
     * 格式化不同类型的日期对象
     * @param dateFormat 日期格式
     * @param val 被格式化的日期对象
     * @return 格式化后的日期字符
     */
    private String parseDateToStr(String dateFormat, Object val) {
        if(val == null) {
            return "";
        }
        String str;
        if(val instanceof Date) {
            str = DateUtils.parseDateToStr(dateFormat, (Date) val);
        } else if(val instanceof LocalDateTime) {
            str = DateUtils.parseDateToStr(dateFormat, DateUtils.toDate((LocalDateTime) val));
        } else if(val instanceof LocalDate) {
            str = DateUtils.parseDateToStr(dateFormat, DateUtils.toDate((LocalDate) val));
        } else {
            str = val.toString();
        }
        return str;
    }

    /**
     * 设置动态表尾
     * @param dynamicFooterIndex 动态表尾开始的位置
     * @param handleCallback 表尾处理回调
     */
    public void setDynamicFooterHandler(int dynamicFooterIndex, Action2<T, List<String>> handleCallback) {
        this.dynamicFooterHandler = new DynamicFooterHandler<>(dynamicFooterIndex, handleCallback);
    }


    /**
     * 验证并获取excel单元格内的值
     *
     * @param columnData   列值,object类型
     * @param rowIndex     行号
     * @param columnIndex  列号
     * @param fieldName    字段名称
     * @param lengthLimit  限制长度数（null时不做判断）
     * @param ifJudgeEmpty 是否需要判空（默认是）
     * @return 字符串格式值
     * @throws Exception 逻辑异常
     */
    public static String checkValue(Object columnData, int rowIndex, int columnIndex, String fieldName, Integer lengthLimit,
                                    Boolean ifJudgeEmpty) throws Exception {
        String value = getStringValue(columnData);
        ifJudgeEmpty = null == ifJudgeEmpty ? true : ifJudgeEmpty;
        if (ifJudgeEmpty) {
            //需要判空
            if (StringUtils.isEmpty(value)) {
                throw new Exception("第" + (rowIndex + 1) + "行，第" + (columnIndex + 1) + "列，" + fieldName + "不能为空");
            }
        }
        if (null != lengthLimit && lengthLimit > 0) {
            //需要判断字符长度
            if (StringUtils.isNotEmpty(value)) {
                if (value.length() > lengthLimit) {
                    throw new Exception("第" + (rowIndex + 1) + "行，第" + (columnIndex + 1) + "列，" + fieldName + "不能超过" + lengthLimit + "个字符");
                }
            }
        }
        return value;
    }

    /**
     * String => LocalDate
     * 入参str和pattern格式需要对应
     *
     * @param str
     * @return LocalDate
     */
    public static LocalDate str2LocalDate(String str) {
        if (StringUtils.isEmpty(str)) {
            return null;
        }
        if (str.indexOf("-") != -1 || str.indexOf("/") != -1) {
            String pattern = str.indexOf("/") != -1 ? "yyyy/MM/dd" : "yyyy-MM-dd";
            try {
                //测试日期字符串是否符合日期
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
                return LocalDate.parse(str, dateTimeFormatter);
            } catch (Exception e) {
                pattern = str.indexOf("/") != -1 ? "yyyy/M/d" : "yyyy-M-d";
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
                return LocalDate.parse(str, dateTimeFormatter);
            }
        } else {
            Calendar calendar = new GregorianCalendar(1900, 0, -1);
            Date date = calendar.getTime();
            int amount = Integer.parseInt(str);
            if (amount > 0) {
                Calendar calendar1 = Calendar.getInstance();
                calendar1.setTime(date);
                calendar1.add(Calendar.DAY_OF_YEAR, amount);
                date = calendar.getTime();
            }
            return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        }
    }

    /**
     * 获取String类型的值
     *
     * @param columnData 列值,object类型
     * @return 字符串格式值
     */
    public static String getStringValue(Object columnData) {
        if (columnData == null) {
            return null;
        } else {
            String res = columnData.toString().replace("[\\t\\n\\r]", "").trim();
            return res;
//            //判断是否是科学计数法  true是科学计数法,false不是科学计数法
//            boolean isMache=SCIENTIFIC_COUNTING_METHOD_PATTERN.matcher(res).matches();
//            if(isMache){
//                BigDecimal resDecimal = new BigDecimal(res);
//                return resDecimal.toPlainString();
//            }else {
//                return res;
//            }
        }
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     * @param response 返回数据
     * @param list 导出数据集合
     * @param sheetName 工作表的名称
     */
    public void exportExcel(HttpServletResponse response, List<T> list, String sheetName) {
        try {
            exportExcel(response, list, sheetName, StringUtils.EMPTY);
        } catch(Exception e) {
            e.printStackTrace();
            log.error("导出Excel异常{}", e.getMessage());
        }
    }

    /**
     *
     * @param excelFilePath excel地址
     * @param imageFilePath 图片地址
     * @throws IOException
     */
    public static void convertExcelToImage(String excelFilePath, String imageFilePath) throws IOException {
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(new FileInputStream(excelFilePath));
        } catch(EncryptedDocumentException e) {
            e.printStackTrace();
        }
        Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表

        // 获取工作表的尺寸
        int width = sheet.getColumnWidth(sheet.getRow(0).getLastCellNum());
        int height = sheet.getLastRowNum() * 30;

        // 创建一个BufferedImage作为画布
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();

        // 设置背景颜色
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0,0,width,height);


        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING,RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING,RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setColor(Color.BLACK);
        g2d.drawString("计量支付审批表", width / 2,23 );
        // 绘制单元格
        for (Row row : sheet) {
            for (Cell cell : row) {
                // 获取单元格的值并将其绘制到图片上
                String value = cell.getStringCellValue();
                g2d.setColor(Color.BLACK);
                int columnIndex = cell.getColumnIndex();
                int rowNum = row.getRowNum();
                g2d.drawString(value, (columnIndex + 1) * 200, (rowNum + 2) * 23);
            }
        }

        // 释放资源
        g2d.dispose();

        // 将BufferedImage保存为图片文件
        ImageIO.write(image, "png", new File(imageFilePath));
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     * @param response 返回数据
     * @param list 导出数据集合
     * @param sheetName 工作表的名称
     * @param title 标题
     */
    public void exportExcel(HttpServletResponse response, List<T> list, String sheetName, String title) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("content-Type", "application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(sheetName + ".xlsx", "UTF-8"));
        this.init(list, sheetName, title, Excel.Type.EXPORT);
        exportExcel(response);
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     */
    public void exportExcel(HttpServletResponse response) {
        try {
            writeSheet();
            ServletOutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
        } catch(Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(wb);
        }
    }

    /**
     * 创建写入数据到Sheet
     */
    private void writeSheet() {
        // 取出一共有多少个sheet.
        int sheetNo = Math.max(1, (int) Math.ceil(list.size() * 1.0 / SHEET_SIZE));
        for(int index = 0; index < sheetNo; index++) {
            createSheet(sheetNo, index);

            // 产生一行
            Row row = sheet.createRow(rowNum);
            int column = 0;
            // 写入各个字段的列头名称
            for(Object[] os : fields) {
                Excel excel = (Excel) os[1];
                this.createCell(excel, row, column);
                this.setDictDropDownList(excel, row, column);
                column++;
            }
            if(Excel.Type.EXPORT.equals(type)) {
                fillExcelData(index, row);
                addStatisticsRow();
            }
        }
    }

    /**
     * 设置字典下来列表
     * @param excel 导出注解
     * @param row 行
     * @param column 列
     */
    private void setDictDropDownList(Excel excel, Row row, int column) {
        List<String> dictList = this.analyzeDictList(excel);
        if(ListUtil.isEmpty(dictList)) {
            return;
        }
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint capacityConstraint = helper.createExplicitListConstraint(dictList.toArray(new String[0]));
        //需要被设置为下拉数据的单元格范围
        CellRangeAddressList capacityList = new CellRangeAddressList(1, 5000, column, column);
        DataValidation capacityDataValidation = helper.createValidation(capacityConstraint, capacityList);
        sheet.addValidationData(capacityDataValidation);
    }

    /**
     * 解析该字典配置的字典列表, 没有返回空
     * @param excel 导出注解
     * @return 字典列表
     */
    private List<String> analyzeDictList(Excel excel) {
        if(StringUtils.isNotBlank(excel.dictType())) {
           return ListUtil.emptyList();
        }
        return ListUtil.emptyList();
    }
}
