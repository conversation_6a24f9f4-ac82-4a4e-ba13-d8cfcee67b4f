package com.fawkes.project.tbm.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.core.io.ByteArrayResource;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class SysOssVO {
    @ApiModelProperty("文件地址")
    private String link;

    @ApiModelProperty("文件token")
    private String fileToken;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("文件扩展名")
    private String extName;

    @ApiModelProperty("文件组token")
    private String groupToken;

    @ApiModelProperty("文件的目录")
    private String dir;

    @ApiModelProperty("文件的大小")
    private String size;

    @ApiModelProperty("文件的版本")
    private String version;

    @ApiModelProperty("oss对象名")
    private String objectName;

    @ApiModelProperty("Byte流")
    private ByteArrayResource resource;
}
