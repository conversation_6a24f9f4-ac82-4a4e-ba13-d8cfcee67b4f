package com.fawkes.project.tbm.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fawkes.project.tbm.common.model.TbmAttitude;
import com.fawkes.project.tbm.common.vo.TbmAttitudeXYVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * TBM姿态参数表(此表为原始表，数据按月分表) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface TbmAttitudeMapper extends BaseMapper<TbmAttitude> {

    /**
     * 获取最后十个环号对应的数据
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     */
    List<TbmAttitudeXYVO> getRingNumTbmAttitude(@Param("deviceCode") String deviceCode,
                                                @Param("ringNumList") List<Integer> ringNumList,
                                                @Param("tableName") String tableName);

    /**
     * 是否存在表
     *
     * @param tableName
     * @return
     */
    int existTable(@Param("tableName") String tableName);

    /**
     * 删除表
     *
     * @param tableName
     * @return
     */
    int dropTable(@Param("tableName") String tableName);

    /**
     * 创建表
     *
     * @param tableName
     * @return
     */
    int createTable(@Param("tableName") String tableName);
}
