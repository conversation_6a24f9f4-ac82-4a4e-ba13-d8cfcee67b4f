package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/8/7 15:24
 * @description
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tbm_functional_management")
@ApiModel("tbm功能管理维护")
public class TbmFunctionalManagement extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long parentId;

    private String title;

    private String code;

    private String path;

    private String icon;

    private String remark;

    private Integer sort;

    private Integer hidden;

    private String component;

    private String pathId;

    private Integer tenantId;

    private Long portalId;

    private Integer enable;

    private String ext1;

    private String ext2;

    private String ext3;

    private String ext4;

    private String ext5;
}
