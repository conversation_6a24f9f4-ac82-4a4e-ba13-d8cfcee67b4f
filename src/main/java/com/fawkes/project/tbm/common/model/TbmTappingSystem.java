package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * TBM出渣系统参数(此表为原始表，数据按月分表)
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tbm_tapping_system")
@ApiModel("TBM出渣系统参数(此表为原始表，数据按月分表)")
public class TbmTappingSystem implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 螺机转速/Double/kw
     */
    @ApiModelProperty("螺机转速/Double/kw")
    private BigDecimal snailRotationalSpeed;

    /**
     * 螺机扭矩/Double/hz
     */
    @ApiModelProperty("螺机扭矩/Double/hz")
    private BigDecimal snailTorchque;

    /**
     * 皮带机转速/Double
     */
    @ApiModelProperty("皮带机转速/Double")
    private BigDecimal beltRotationalSpeed;

    /**
     * 环数
     */
    @ApiModelProperty("环数")
    private Integer ringNum;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Timestamp updateTime;

}