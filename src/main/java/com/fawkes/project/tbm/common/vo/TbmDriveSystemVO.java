package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * TBM驱动系统VO
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel("TBM驱动系统VO")
public class TbmDriveSystemVO implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 电机总功率/Double/kw
     */
    @ApiModelProperty("电机总功率/Double/kw")
    private String power;

    /**
     * 振动频率/Double/hz
     */
    @ApiModelProperty("振动频率/Double/hz")
    private String frequency;

    /**
     * 点击温度/Double/hz
     */
    @ApiModelProperty("振动频率/Double/hz")
    private String temperature;

    /**
     * 电机电流/Double
     */
    @ApiModelProperty("电机电流/Double")
    private String electricCurrent;

    /**
     * 电机总功率列表
     */
    @ApiModelProperty("电机总功率列表")
    private List<TbmXYVO> powerList;

    /**
     * 振动频率列表
     */
    @ApiModelProperty("振动频率列表")
    private List<TbmXYVO> frequencyList;

    /**
     * 电机温度列表
     */
    @ApiModelProperty("电机温度列表")
    private List<TbmXYVO> temperatureList;

    /**
     * 电机电流列表
     */
    @ApiModelProperty("电机电流列表")
    private List<TbmXYVO> electricCurrentList;

    /**
     * 告警状态，0-正常，1-异常
     */
    @ApiModelProperty("告警状态，0-正常，1-异常")
    private Integer alarmStatus;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("告警id")
    private Long alarmId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd")
    private Timestamp createTime;

    /**
     * 环数
     */
    @ApiModelProperty("环数")
    private Integer ringNum;
}
