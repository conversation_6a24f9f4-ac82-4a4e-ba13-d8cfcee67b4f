package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 刀具磨损统计vo
 */
@Data
@ApiModel("刀具磨损统计vo")
public class ToolWearRuleVO implements Serializable {

    /**
     * 位置
     */
    @ApiModelProperty("位置")
    private String toolLocation;

    /**
     * 累计磨损量
     */
    @ApiModelProperty("累计磨损量")
    private BigDecimal cumulativeWearValue;

    /**
     * 每公里磨损量
     */
    @ApiModelProperty("每公里磨损量")
    private BigDecimal everyKilometerWearValue;

    /**
     * 刀具位置id
     */
    @ApiModelProperty("刀具位置id")
    private Long toolPositionManageId;

    /**
     * 最后一次磨损值
     */
    @JsonIgnore
    private BigDecimal LastWearValue;

    /**
     * 最后一次结束里程
     */
    @JsonIgnore
    private BigDecimal LastEndMileage;

    /**
     * 最后一次是否换刀(true是/false否)
     */
    @JsonIgnore
    private Boolean LastHasToolChanged;
}
