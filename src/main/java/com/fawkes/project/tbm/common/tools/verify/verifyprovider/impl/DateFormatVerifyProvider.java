package com.fawkes.project.tbm.common.tools.verify.verifyprovider.impl;

import com.fawkes.project.tbm.common.tools.verify.enums.VerifyType;
import com.fawkes.project.tbm.common.tools.verify.verifyprovider.VerifyProvider;
import com.fawkes.project.tbm.common.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @version DateFormatVerifyProvider 2023/7/24 9:50
 */
@Component
public class DateFormatVerifyProvider implements VerifyProvider {
    /**
     * 支持的验证类型
     * @return 支持的验证类型
     */
    @Override
    public VerifyType supportedVerifyType() {
        return VerifyType.DateFormat;
    }

    /**
     * 验证
     * @param target 验证目标
     * @return 验证结果 true:验证通过  false:验证失败
     * @throws Exception 验证异常
     */
    @Override
    public boolean verify(Object target) throws Exception {
        if(target == null) {
            return true;
        }

        if(target instanceof String == false) {
            return false;
        }
        if(StringUtils.isBlank(target.toString())){
            return true;
        }
        Date date = DateUtils.parseDate(target);
        return date != null;
    }
}
