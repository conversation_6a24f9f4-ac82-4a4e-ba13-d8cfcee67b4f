package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.sql.Date;

/**
 * 安全运行配置表
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_runing")
@ApiModel("安全运行配置表")
public class SafeRuning extends BaseModel {

    private static final long serialVersionUID = 1L;

    /**
     * 安全运行时间
     */
    @ApiModelProperty("安全运行时间")
    private Date runingStart;

}