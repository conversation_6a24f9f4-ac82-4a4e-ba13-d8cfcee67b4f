package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 有害气体设备管理表
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("harmful_gas_device")
@ApiModel("有害气体设备管理表")
public class HarmfulGasDevice extends BaseModel {

    private static final long serialVersionUID = 1L;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String name;

    /**
     * TBM设备code
     */
    @ApiModelProperty("TBM设备code")
    private String deviceCode;

    /**
     * TBM设备名称
     */
    @ApiModelProperty("TBM设备名称")
    private String deviceName;

    /**
     * 甲烷
     */
    @ApiModelProperty("甲烷")
    private BigDecimal ch4Level;

    /**
     * 二氧化碳
     */
    @ApiModelProperty("二氧化碳")
    private BigDecimal h2sLevel;

    /**
     * 氧气
     */
    @ApiModelProperty("氧气")
    private BigDecimal o2Level;

    /**
     * 一氧化碳
     */
    @ApiModelProperty("一氧化碳")
    private BigDecimal coLevel;

    /**
     * 更新人姓名
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新人姓名")
    private String updateByFullname;

}