package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.sql.Timestamp;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tbm_support_system_alarm_details")
@ApiModel("TBM支撑系统-告警表-详情(数据按月分表)")
public class TbmSupportSystemAlarmDetails {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("告警id")
    private Long alarmId;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * 报警内容
     */
    @ApiModelProperty("报警内容")
    private String alarmContent;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Timestamp updateTime;
}
