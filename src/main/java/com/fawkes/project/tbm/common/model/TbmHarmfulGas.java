package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * TBM有害气体参数表(此表为原始表，数据按月分表)
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tbm_harmful_gas")
@ApiModel("TBM有害气体参数表(此表为原始表，数据按月分表)")
public class TbmHarmfulGas implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String code;

    /**
     * CH4/Double/%LEL
     */
    @ApiModelProperty("CH4/Double/%LEL")
    private BigDecimal ch4Level;

    /**
     * CO2/Double/%VOL
     */
    @ApiModelProperty("h2s/Double/%VOL")
    private BigDecimal h2sLevel;

    /**
     * O2/Double/%VOL
     */
    @ApiModelProperty("O2/Double/%VOL")
    private BigDecimal o2Level;

    /**
     * CO/Double/PPM
     */
    @ApiModelProperty("CO/Double/PPM")
    private BigDecimal coLevel;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Timestamp createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Timestamp updateTime;

}