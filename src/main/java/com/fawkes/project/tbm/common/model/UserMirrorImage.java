package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_mirror_image")
@ApiModel("用户镜像表")
public class UserMirrorImage extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private String userName;

    private String password;

    private String userFullname;

    private String sex;

    private String phone;

    private String email;

    private String ext1;

    private String ext2;

    private String ext3;

    private String ext4;

    private String ext5;

    private String phoneMirror;

    @ApiModelProperty("账号周期 1长期 2临时")
    private String accountPeriod;

    @ApiModelProperty("账号状态：1、激活；2、休眠；3、注销")
    private Integer accountStatus;

    private String functionalIds;

    private String subProjectIds;
}
