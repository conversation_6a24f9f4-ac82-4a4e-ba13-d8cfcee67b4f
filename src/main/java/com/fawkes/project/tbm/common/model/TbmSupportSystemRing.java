package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * TBM支撑系统参数环数表
 * <AUTHOR>
 * @version TbmSupportSystemRing 2025/6/10 18:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("TBM支撑系统参数环数表")
@TableName("tbm_support_system_ring")
public class TbmSupportSystemRing implements Serializable {
    /** 主键id */
    private Long id;
    /** 设备编码 */
    private String code;
    /** 环数 */
    private Integer ringNum;
    /** 最新更新时间 */
    private LocalDateTime maxTime;
}

