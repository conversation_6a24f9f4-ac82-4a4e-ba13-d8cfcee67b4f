package com.fawkes.project.tbm.common.utils;
 
import java.util.HashMap;
import java.util.Map;

/**
 * 定义多线程的共享工具类——可继承父线程数据的线程变量
 */
public class ThreadLocalUtil {
 
    /**
     * 定义线程变量
     */
    private static final InheritableThreadLocal<Map<String,String>> headerMap =
            new InheritableThreadLocal<Map<String,String>>(){
                @Override
                protected Map<String, String> initialValue() {
                    return new HashMap<>();
                }
            };
 
    /**
     * 获取所有共享变量
     * @return 共享Map
     */
    public static Map<String,String> get(){
        return headerMap.get();
    }
 
    /**
     * 获取共享变量指定key的value值
     * @param key key
     * @return
     */
    public static String get(String key){
        return headerMap.get().get(key);
    }
 
    /**
     * 设置共享变量值
     * @param key
     * @param value
     */
    public static void set(String key,String value){
        headerMap.get().put(key,value);
    }

    /**
     * 结束清空
     */
    public static void clear() {
        headerMap.remove();
    }
 
}