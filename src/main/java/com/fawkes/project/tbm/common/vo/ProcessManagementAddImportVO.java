package com.fawkes.project.tbm.common.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fawkes.project.tbm.common.tools.verify.config.VerifyConfig;
import com.fawkes.project.tbm.common.tools.verify.domain.IErrorMsg;
import com.fawkes.project.tbm.common.tools.verify.domain.ISerialNumber;
import com.fawkes.project.tbm.common.tools.verify.enums.VerifyType;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 工序管理导入数据VO
 * @Date 2025/6/9 10:25
 */
@Data
@ExcelIgnoreUnannotated
public class ProcessManagementAddImportVO implements ISerialNumber, IErrorMsg {

    /**
     * 序号
     */
    private Integer serialNumber;

    @ExcelProperty(value = "*区段", index = 0)
    private String section;

    @ExcelProperty(value = "*环号", index = 1)
    private String ringNum;

    @ExcelProperty(value = "*拼装点位", index = 2)
    private String assemblyPoint;

    @ExcelProperty(value = {"掘进工序填报", "（掘进）开始时间"}, index = 3)
    private String tunnelingStartTime;

    @ExcelProperty(value = {"掘进工序填报", "（掘进）结束时间"}, index = 4)
    private String tunnelingEndTime;

    @ExcelProperty(value = {"掘进工序填报", "（换步）开始时间"}, index = 5)
    private String changeStepsStartTime;

    @ExcelProperty(value = {"掘进工序填报", "（换步）结束时间"}, index = 6)
    private String changeStepsEndTime;

    @ExcelProperty(value = {"掘进工序填报", "（管片拼装）开始时间"}, index = 7)
    private String segmentAssemblyStartTime;

    @ExcelProperty(value = {"掘进工序填报", "（管片拼装）结束时间"}, index = 8)
    private String segmentAssemblyEndTime;

    @ExcelProperty(value = {"掘进工序填报", "（其它工作）开始时间"}, index = 9)
    private String othersJobStartTime;

    @ExcelProperty(value = {"掘进工序填报", "（其它工作）结束时间"}, index = 10)
    private String othersJobEndTime;

    @ExcelProperty(value = {"掘进工序填报", "（维修保养-常规保养）开始时间"}, index = 11)
    private String commonMaintenanceAndRepairStartTime;

    @ExcelProperty(value = {"掘进工序填报", "（维修保养-常规保养）结束时间"}, index = 12)
    private String commonMaintenanceAndRepairEndTime;

    @ExcelProperty(value = {"掘进工序填报", "（维修保养-故障停机）开始时间"}, index = 13)
    private String shutdownMaintenanceAndRepairStartTime;

    @ExcelProperty(value = {"掘进工序填报", "（维修保养-故障停机）结束时间"}, index = 14)
    private String shutdownMaintenanceAndRepairEndTime;

    @ExcelProperty(value = {"掘进工序填报", "（开仓换刀）开始时间"}, index = 15)
    private String openWarehouseChangeToolStartTime;

    @ExcelProperty(value = {"掘进工序填报", "（开仓换刀）结束时间"}, index = 16)
    private String openWarehouseChangeToolEndTime;

    @ExcelProperty(value = {"掘进工序填报", "第几仓"}, index = 17)
    private String openWarehouseChangeToolNum;

    @ExcelProperty(value = "异常情况说明", index = 18)
    private String descriptionOfAbnormalSituations;

    @ExcelProperty(value = "渣土性状", index = 19)
    private String constructionWasteSoilProperties;

    @ExcelProperty(value = "出渣量（m³）", index = 20)
    private String residueDischargeVolume;

    @ExcelProperty(value = "管片坡度", index = 21)
    private String slopeOfTheSegment;

    @ExcelProperty(value = "泡沫剂使用（KG）", index = 22)
    private String useOfFoamingAgent;

    @ExcelProperty(value = "油脂使用（KG）", index = 23)
    private String greaseUsage;

    @ExcelProperty(value = "膨润土", index = 24)
    private String bentonite;

    @ExcelProperty(value = "其他", index = 25)
    private String others;

    @ExcelProperty(value = "备注", index = 26)
    private String remark;

    /**
     * 错误消息
     */
    private String errorMsg;

    /**
     * 获取校验配置
     *
     * @return 校验配置
     */
    public static VerifyConfig<ProcessManagementAddImportVO> getVerifyConfig() {
        return new VerifyConfig<ProcessManagementAddImportVO>();
//                .addConfig(VerifyType.NotBlank, ProcessManagementAddImportVO::getSection, "区段不能为空")
//                .addConfig(VerifyType.NotBlank, ProcessManagementAddImportVO::getRingNum, "环号不能为空")
//                .addConfig(VerifyType.NotBlank, ProcessManagementAddImportVO::getAssemblyPoint, "拼装点位不能为空");
    }
}
