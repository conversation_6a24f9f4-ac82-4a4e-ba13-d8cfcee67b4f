package com.fawkes.project.tbm.common.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version TbmDriveSystemXYVO 2025/6/11 17:36
 */
@Data
public class TbmDriveSystemXYVO implements Serializable {
    /**
     * x轴-环数
     */
    private String ring;
    /**
     * y轴-最大
     */
    private BigDecimal powerMax;
    /**
     * y轴-最大
     */
    private BigDecimal electricCurrentMax;
    /**
     * y轴-最大
     */
    private BigDecimal temperatureMax;
    /**
     * y轴-平均
     */
    private BigDecimal powerAverage;
    /**
     * y轴-平均
     */
    private BigDecimal electricCurrentAverage;
    /**
     * y轴-平均
     */
    private BigDecimal temperatureAverage;
}
