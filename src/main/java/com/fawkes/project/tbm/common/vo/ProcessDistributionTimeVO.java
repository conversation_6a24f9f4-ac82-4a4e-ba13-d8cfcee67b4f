package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/6/4 14:06
 */
@Data
@ApiModel("工序时间分布")
public class ProcessDistributionTimeVO {

    @ApiModelProperty("工序开始时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp startTime;

    @ApiModelProperty("工序结束时间")
    @JsonFormat(timezone = "GMT+8", shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Timestamp endTime;
}
