package com.fawkes.project.tbm.common.model;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 风险统计配置表
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("risk_statistics")
@ApiModel("风险统计配置表")
public class RiskStatistics extends BaseModel {

    private static final long serialVersionUID = 1L;

    /**
     * TBM设备code
     */
    @ApiModelProperty("TBM设备code")
    private String deviceCode;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 区段
     */
    @ApiModelProperty("区段")
    private String section;

        /**
         * 风险起始点距离
         */
    @ApiModelProperty("风险起始点距离")
    private Integer riskStart;

        /**
         * 风险终点距离
         */
    @ApiModelProperty("风险终点距离")
    private Integer riskEnd;

}