package com.fawkes.project.tbm.common.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 风险源预警
 * <AUTHOR>
 * @date 2025-03-25
 */
@Data
@ApiModel("风险源预警参数")
public class RiskWarningParam  implements Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 设备code
     */
    @ApiModelProperty("设备code")
    private String deviceCode;

    /**
     * 设备名称
     */
    @NotBlank(message = "设备名称不能为空")
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 风险名称
     */
    @NotBlank(message = "风险名称不能为空")
    @Size(max = 50, message = "风险名称长度不能超过50个字符")
    @ApiModelProperty("风险名称")
    private String riskName;

    /**
     * 风险距离
     */
    @NotBlank(message = "里程范围不能为空")
    @ApiModelProperty("风险起始点距离")
    private String riskStart;

    /**
     * 风险距离
     */
    @NotBlank(message = "里程范围不能为空")
    @ApiModelProperty("风险终点距离")
    private String riskEnd;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @Size(max = 200, message = "备注长度不能超过200个字符")
    private String remark;

    /**
     * 附件
     */
    @ApiModelProperty("附件")
    private String attachment;

}