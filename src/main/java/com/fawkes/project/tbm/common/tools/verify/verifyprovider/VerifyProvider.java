package com.fawkes.project.tbm.common.tools.verify.verifyprovider;


import com.fawkes.project.tbm.common.tools.verify.enums.VerifyType;

/**
 * 验证提供者接口
 * <AUTHOR>
 */
public interface VerifyProvider {
	/**
	 * 支持的验证类型
	 * @return 支持的验证类型
	 */
	VerifyType supportedVerifyType();

	/**
	 * 验证
	 * @param target 验证目标
	 * @return 验证结果 true:验证通过  false:验证失败
	 * @throws Exception 验证异常
	 */
	boolean verify(Object target) throws Exception;
}
