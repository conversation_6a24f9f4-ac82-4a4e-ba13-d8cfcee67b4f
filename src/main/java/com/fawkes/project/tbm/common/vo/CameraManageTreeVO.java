package com.fawkes.project.tbm.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 摄像头配置表
 * <AUTHOR>
 * @date 2025-03-26
 */
@Data
@ApiModel("摄像头配置TreeVO")
public class CameraManageTreeVO implements Serializable {

    /**
     * 设备code
     */
    @ApiModelProperty("设备code")
    private String deviceCode;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 摄像头列表
     */
    @ApiModelProperty("摄像头列表")
    private List<CameraManageVO> cameraList;


}