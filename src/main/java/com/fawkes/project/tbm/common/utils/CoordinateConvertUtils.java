package com.fawkes.project.tbm.common.utils;

import org.locationtech.proj4j.*;

/**
 * 坐标转换工具类
 *
 * <AUTHOR>
 */
public class CoordinateConvertUtils {

    private static final double PI = Math.PI;
    private static final double A = 6378245.0; // WGS84椭球长半轴
    private static final double EE = 0.00669342162296594323; // WGS84椭球偏心率平方

    /**
     * 转换成经纬度 4549
     *
     * @param x 东方向坐标   502383.80
     * @param y 北方向坐标   3353649.00
     * @return
     */
    public static double[] convertLongitudeAndLatitude(Double x, Double y) {
        if (x == null || y == null) {
            return null;
        }
        // 使用Proj4字符串直接定义坐标系
        CRSFactory crsFactory = new CRSFactory();

        // CGCS2000 3°带投影坐标系 (EPSG:4549)
        String proj4549 = "+proj=tmerc +lat_0=0 +lon_0=120 +k=1 +x_0=500000 +y_0=0 "
                + "+ellps=GRS80 +units=m +no_defs";

        // CGCS2000 地理坐标系 (EPSG:4490)
        String proj4490 = "+proj=longlat +ellps=GRS80 +no_defs";

        CoordinateReferenceSystem sourceCRS = crsFactory.createFromParameters("EPSG:4549", proj4549);
        CoordinateReferenceSystem targetCRS = crsFactory.createFromParameters("EPSG:4490", proj4490);

        // 创建转换器
        CoordinateTransform transform = new CoordinateTransformFactory().createTransform(sourceCRS, targetCRS);

        // 执行转换
        ProjCoordinate sourceCoord = new ProjCoordinate(x, y);
        ProjCoordinate targetCoord = new ProjCoordinate();
        transform.transform(sourceCoord, targetCoord);

        double[] result = new double[2];
        result[0] = targetCoord.x;
        result[1] = targetCoord.y;
        return result;
    }

    public static void main(String[] args) {
        double[] doubles = convertLongitudeAndLatitude(502383.80, 3353649.00);
        // 输出结果 (经度, 纬度)
        System.out.printf("经度: " + doubles[0]);
        System.out.printf("纬度: " + doubles[1]);

        // WGS84(国际通用)
        double cgcs2000Lng = 120.0248169431508;
        double cgcs2000Lat = 30.302461254588152;

        double[] gcj02 = transform(cgcs2000Lng, cgcs2000Lat);
        System.out.println("GCJ02坐标（高德）: 经度=" + gcj02[0] + ", 纬度=" + gcj02[1]);
    }

    /**
     * 判断坐标是否在中国境外
     *
     * @param lng 经度
     * @param lat 纬度
     * @return true表示境外（无需转换）
     */
    public static boolean outOfChina(double lng, double lat) {
        return !(lng >= 72.004 && lng <= 137.8347 && lat >= 0.8293 && lat <= 55.8271);
    }

    /**
     * CGCS2000转GCJ02（高德坐标系）
     *
     * @param lng 大地2000经度
     * @param lat 大地2000纬度
     * @return 转换后的GCJ02坐标 [经度, 纬度]
     */
    public static double[] transform(double lng, double lat) {
        if (outOfChina(lng, lat)) {
            return new double[]{lng, lat}; // 境外坐标不转换
        }
        double dLat = calcLatOffset(lng - 105.0, lat - 35.0);
        double dLng = calcLngOffset(lng - 105.0, lat - 35.0);
        double radLat = lat / 180.0 * PI;
        double magic = Math.sin(radLat);
        magic = 1 - EE * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / ((A * (1 - EE)) / (magic * sqrtMagic) * PI);
        dLng = (dLng * 180.0) / (A / sqrtMagic * Math.cos(radLat) * PI);
        return new double[]{lng + dLng, lat + dLat};
    }

    // 计算纬度偏移量
    private static double calcLatOffset(double x, double y) {
        double offset = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y;
        offset += 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
        offset += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
        offset += (20.0 * Math.sin(y * PI) + 40.0 * Math.sin(y / 3.0 * PI)) * 2.0 / 3.0;
        offset += (160.0 * Math.sin(y / 12.0 * PI) + 320.0 * Math.sin(y * PI / 30.0)) * 2.0 / 3.0;
        return offset;
    }

    // 计算经度偏移量
    private static double calcLngOffset(double x, double y) {
        double offset = 300.0 + x + 2.0 * y + 0.1 * x * x;
        offset += 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
        offset += (20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0 / 3.0;
        offset += (20.0 * Math.sin(x * PI) + 40.0 * Math.sin(x / 3.0 * PI)) * 2.0 / 3.0;
        offset += (150.0 * Math.sin(x / 12.0 * PI) + 300.0 * Math.sin(x / 30.0 * PI)) * 2.0 / 3.0;
        return offset;
    }
}