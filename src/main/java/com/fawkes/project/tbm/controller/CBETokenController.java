package com.fawkes.project.tbm.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import okhttp3.*;
import org.apache.logging.log4j.util.Base64Util;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 获取CBEToken
 *
 * <AUTHOR>
 */
@Api(tags = "获取CBEToken")
@RestController
public class CBETokenController {

    @Value("${CBEInfo.CBEUrl}")
    private String CBEUrl;

    @Value("${CBEInfo.userName}")
    private String userName;

    @Value("${CBEInfo.password}")
    private String password;

    @PostMapping(path = "/CBE/token", produces = {"application/json;charset=UTF-8"})
    public Object getCBEToken() {
        Map<String, Object> res = new HashMap<>();
        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            MediaType mediaType = MediaType.parse("application/json;charset=UTF-8");
            String base64Password = Base64Util.encode(password);
            RequestBody body = RequestBody.create(mediaType, "{\"userName\":\"" + userName + "\",\"password\":\"" + base64Password + "\"}");
            Request request = new Request.Builder()
                    .url(CBEUrl + "/HDEC/newLogin/out")
                    .method("POST", body)
                    .addHeader("Accept", "application/json, text/plain, */*")
                    .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                    .addHeader("Connection", "keep-alive")
                    .addHeader("Content-Type", "application/json")
                    .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.0.0 Safari/537.36")
                    .build();
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                res.put("status", false);
                res.put("data", null);
                res.put("message", "CBE服务器地址输入错误");
                res.put("code", -8000000);
                return res;
            }
            JSONObject loginInfo = JSONObject.parseObject(response.body().string());
            if (loginInfo.get("code").toString().equals("8000000")) {
                JSONObject loginData = (JSONObject) loginInfo.get("data");
                String token = loginData.get("access_token").toString();
                res.put("status", true);
                res.put("data", token);
                res.put("message", "成功");
                res.put("code", 8000000);
                return res;
            } else {
                res.put("status", false);
                res.put("data", null);
                res.put("message", loginInfo.get("message").toString());
                res.put("code", -8000000);
                return res;
            }
        } catch (IOException e) {
            res.put("status", false);
            res.put("data", null);
            res.put("message", e.getMessage());
            res.put("code", -8000000);
            return res;
        }
    }
}