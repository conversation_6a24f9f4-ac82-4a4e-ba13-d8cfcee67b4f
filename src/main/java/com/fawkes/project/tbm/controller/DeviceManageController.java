package com.fawkes.project.tbm.controller;


import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.param.DeviceManageParam;
import com.fawkes.project.tbm.common.vo.DeviceManageVO;
import com.fawkes.project.tbm.service.DeviceManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 设备管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Api(tags = {"设备管理"})
@RestController
@RequestMapping("/device")
public class DeviceManageController {

    @Resource
    private DeviceManageService deviceManageService;

    @ApiOperation("新增设备")
    @PostMapping("/add")
    public ApiResponseBody add(@RequestBody @Valid DeviceManageParam param) {
        deviceManageService.add(param);
        return ApiResponseBody.defaultSuccess();
    }

    @ApiOperation("修改设备")
    @PostMapping("/update")
    public ApiResponseBody update(@RequestBody @Valid DeviceManageParam param) {
        deviceManageService.update(param);
        return ApiResponseBody.defaultSuccess();
    }

    @ApiOperation("设备详情")
    @ApiImplicitParam(name = "id", value = "ID")
    @GetMapping("/get")
    public ApiResponseBody<DeviceManageVO> get(@RequestParam String id) {
        return ApiResponseBody.defaultSuccess(deviceManageService.get(id));
    }

    @ApiOperation("删除设备")
    @ApiImplicitParam(name = "id", value = "ID")
    @DeleteMapping("/delete")
    public ApiResponseBody delete(@RequestParam String id) {
        return ApiResponseBody.defaultSuccess(deviceManageService.delete(id));
    }

    @ApiOperation("设备列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编号"),
            @ApiImplicitParam(name = "name", value = "名称")
    })
    @GetMapping("/list")
    public ApiResponseBody<List<DeviceManageVO>> list(@RequestParam String code,
                                                      @RequestParam String name) {
        return ApiResponseBody.defaultSuccess(deviceManageService.list(code, name));
    }
}

