package com.fawkes.project.tbm.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.model.TbmOperationLog;
import com.fawkes.project.tbm.service.IOperationLogService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 操作记录表Controller
 * <AUTHOR>
 * @date 2025-01-27
 */
@Api(tags = {"操作记录管理"})
@RestController
@RequestMapping("/operationLog")
public class OperationLogController {

    @Resource
    private IOperationLogService operationLogService;

//    /**
//     * 分页查询操作记录
//     *
//     * @param moduleType 模块类型（TOOL_MANAGEMENT-刀具管理，PROCESS_MANAGEMENT-工序管理）
//     * @param recordId 记录ID
//     * @param operationType 操作类型（ADD-新增，UPDATE-修改，DELETE-删除）
//     * @param operator 操作人
//     * @param startTime 开始时间
//     * @param endTime 结束时间
//     * @param pageNo 页码
//     * @param pageSize 页大小
//     * @return 操作记录分页结果
//     */
//    @ApiOperation(value = "分页查询操作记录", notes = "分页查询操作记录")
//    @GetMapping(value = "/page")
//    public ApiResponseBody<PageInfo<TbmOperationLog>> page(
//            @ApiParam("模块类型（TOOL_MANAGEMENT-刀具管理，PROCESS_MANAGEMENT-工序管理）") @RequestParam(name = "moduleType", required = false) String moduleType,
//            @ApiParam("记录ID") @RequestParam(name = "recordId", required = false) Long recordId,
//            @ApiParam("操作类型（ADD-新增，UPDATE-修改，DELETE-删除）") @RequestParam(name = "operationType", required = false) String operationType,
//            @ApiParam("操作人") @RequestParam(name = "operator", required = false) String operator,
//            @ApiParam("开始时间") @RequestParam(name = "startTime", required = false) String startTime,
//            @ApiParam("结束时间") @RequestParam(name = "endTime", required = false) String endTime,
//            @ApiParam("页码") @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
//            @ApiParam("页大小") @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
//        return ApiResponseBody.defaultSuccess(operationLogService.pageOperationLogs(moduleType, recordId, operationType, operator, startTime, endTime, pageNo, pageSize));
//    }

    /**
     * 根据记录ID查询操作记录
     *
     * @param moduleType 模块类型（TOOL_MANAGEMENT-刀具管理，PROCESS_MANAGEMENT-工序管理）
     * @param recordId 记录ID
     * @return 操作记录列表
     */
    @ApiOperation(value = "根据记录ID查询操作记录", notes = "根据记录ID查询操作记录")
    @GetMapping(value = "/getByRecordId")
    public ApiResponseBody<List<TbmOperationLog>> getByRecordId(
            @ApiParam("模块类型（TOOL_MANAGEMENT-刀具管理，PROCESS_MANAGEMENT-工序管理）") @RequestParam String moduleType,
            @ApiParam("记录ID") @RequestParam Long recordId) {
        return ApiResponseBody.defaultSuccess(operationLogService.getOperationLogsByRecordId(moduleType, recordId));
    }
} 