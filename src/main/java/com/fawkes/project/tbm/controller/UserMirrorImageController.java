package com.fawkes.project.tbm.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.param.UserMirrorImageParam;
import com.fawkes.project.tbm.service.IUserMirrorImageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Api(tags = {"白名单管理"})
@RestController
@RequestMapping("/whitelist")
public class UserMirrorImageController {

    @Resource
    private IUserMirrorImageService userMirrorImageService;

    @ApiOperation(value = "用户注册")
    @PostMapping("/user/register")
    public ApiResponseBody userRegister(@RequestBody @Valid UserMirrorImageParam userMirrorImageParam) {
        return userMirrorImageService.userRegister(userMirrorImageParam);
    }

    @ApiOperation(value = "获取填报权限目录树")
    @GetMapping("/functionalTree")
    public ApiResponseBody functionalTree() {
        return userMirrorImageService.functionalTree();
    }

    @ApiOperation(value = "白名单详情查询")
    @GetMapping("/details")
    public ApiResponseBody details(@RequestParam String id) {
        return userMirrorImageService.details(id);
    }

    @ApiOperation(value = "白名单分页列表查询")
    @GetMapping("/pageList")
    public ApiResponseBody pageList(@RequestParam Integer pageNo,
                                    @RequestParam Integer pageSize,
                                    @RequestParam(required = false) String name) {
        return userMirrorImageService.pageList(pageNo, pageSize, name);
    }

    @ApiOperation(value = "用户修改")
    @PostMapping("/user/update")
    public ApiResponseBody userUpdate(@RequestBody @Valid UserMirrorImageParam userMirrorImageParam) {
        return userMirrorImageService.userUpdate(userMirrorImageParam);
    }
}