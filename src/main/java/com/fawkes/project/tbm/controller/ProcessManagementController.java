package com.fawkes.project.tbm.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.enums.DataSourceEnums;
import com.fawkes.project.tbm.common.param.ProcessManagementImportResult;
import com.fawkes.project.tbm.common.param.ProcessManagementParam;
import com.fawkes.project.tbm.common.query.ProcessManagementExportQuery;
import com.fawkes.project.tbm.common.vo.*;
import com.fawkes.project.tbm.service.IProcessManagementService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/6/3 18:04
 */
@Api(tags = {"工序管理"})
@RestController
@RequestMapping("/processManagement")
public class ProcessManagementController {

    @Resource
    private IProcessManagementService iProcessManagementService;

    /**
     * 分页列表查询
     *
     * @param processName
     * @param sectionList
     * @param minRingNum
     * @param createName
     * @param createStartDate
     * @param createEndDate
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "分页列表查询", notes = "分页列表查询")
    @GetMapping(value = "/page")
    public ApiResponseBody<PageInfo<ProcessManagementVO>> page(@RequestParam(name = "processName", required = false) String processName,
                                                               @RequestParam(name = "sectionList", required = false) List<String> sectionList,
                                                               @RequestParam(name = "minRingNum", required = false) String minRingNum,
                                                               @RequestParam(name = "maxRingNum", required = false) String maxRingNum,
                                                               @RequestParam(name = "createName", required = false) String createName,
                                                               @RequestParam(name = "createStartDate", required = false) String createStartDate,
                                                               @RequestParam(name = "createEndDate", required = false) String createEndDate,
                                                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        return ApiResponseBody.defaultSuccess(iProcessManagementService.page(processName, sectionList, minRingNum, maxRingNum, createName, createStartDate, createEndDate, pageNo, pageSize));
    }

    /**
     * 新增数据
     *
     * @param param
     * @return
     */
    @ApiOperation("新增")
    @PostMapping("/add")
    public ApiResponseBody add(@RequestBody @Valid ProcessManagementParam param) throws IllegalAccessException {
        param.setDataSource(DataSourceEnums.PC.getCode());
        return ApiResponseBody.defaultSuccess(iProcessManagementService.add(param));
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @ApiOperation("详情")
    @GetMapping("/get")
    public ApiResponseBody<ProcessManagementVO> get(@RequestParam String id) {
        return ApiResponseBody.defaultSuccess(iProcessManagementService.get(id));
    }

    /**
     * ocr 识别
     *
     * @param file
     * @return
     * @throws IllegalAccessException
     */
    @ApiOperation("ocr")
    @PostMapping("/ocr")
    public ApiResponseBody ocr(@RequestParam("file") MultipartFile file) throws IllegalAccessException {
        return ApiResponseBody.defaultSuccess(iProcessManagementService.ocrByFile(file));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @ApiOperation("删除")
    @DeleteMapping(path = "/delete", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody delete(@RequestParam String id) throws IllegalAccessException {
        return ApiResponseBody.defaultSuccess(iProcessManagementService.delete(id, DataSourceEnums.PC.getCode()));
    }

    /**
     * 导入
     *
     * @param file
     * @param response
     * @return
     * @throws IOException
     */
    @ApiOperation("导入")
    @PostMapping("/import")
    public ApiResponseBody<ProcessManagementImportResult> importExcel(@RequestParam MultipartFile file, HttpServletResponse response) throws IOException, IllegalAccessException {
        return ApiResponseBody.defaultSuccess(iProcessManagementService.importExcel(file, response));
    }

    /**
     * 导出
     *
     * @param query
     * @param response
     * @throws IOException
     */
    @ApiOperation("导出")
    @PostMapping("/export")
    public void export(@RequestBody ProcessManagementExportQuery query, HttpServletResponse response) throws IOException {
        iProcessManagementService.export(query, response);
    }

    /**
     * 获取所有工序的环数
     *
     * @param section
     * @return
     */
    @ApiOperation("获取所有工序的环数")
    @GetMapping("/listProcessManagementRingNum")
    public ApiResponseBody<List<ProcessManagementRingNumVO>> listProcessManagementRingNum(@RequestParam String section) {
        return ApiResponseBody.defaultSuccess(iProcessManagementService.listProcessManagementRingNum(section));
    }

    /**
     * 统计工序分布
     *
     * @param section
     * @param ringNum
     * @return
     */
    @ApiOperation("统计工序分布")
    @GetMapping("/statisticsProcessDistribution")
    public ApiResponseBody<ProcessTotalDistributionVO> statisticsProcessDistribution(@RequestParam String section, @RequestParam String ringNum) {
        return ApiResponseBody.defaultSuccess(iProcessManagementService.statisticsProcessDistribution(section, ringNum));
    }

    /**
     * 统计工序占比
     *
     * @param section
     * @param ringNum
     * @return
     */
    @ApiOperation("统计工序占比")
    @GetMapping("/statisticsProcessRatio")
    public ApiResponseBody<List<ProcessRatioVO>> statisticsProcessRatio(@RequestParam String section, @RequestParam(required = false) String ringNum) {
        return ApiResponseBody.defaultSuccess(iProcessManagementService.statisticsProcessRatio(section, ringNum));
    }

    /**
     * 获取施工情况描述
     *
     * @param section
     * @return
     */
    @ApiOperation("获取施工情况描述")
    @GetMapping("/getConstructionSituationDescription")
    public ApiResponseBody<ProcessConstructionSituationDescriptionVO> getConstructionSituationDescription(@RequestParam String section) {
        return ApiResponseBody.defaultSuccess(iProcessManagementService.getConstructionSituationDescription(section));
    }

    /**
     * 列表获取所有环号的异常情况说明
     *
     * @param section
     * @return
     */
    @ApiOperation("列表获取所有环号的异常情况说明")
    @GetMapping("/listAllRingAbnormalSituationsDescription")
    public ApiResponseBody<List<RingAbnormalSituationsDescriptionVO>> listAllRingAbnormalSituationsDescription(@RequestParam String section) {
        return ApiResponseBody.defaultSuccess(iProcessManagementService.listAllRingAbnormalSituationsDescription(section));
    }

    /**
     * 统计设备利用率
     *
     * @param section
     * @return
     */
    @ApiOperation("统计设备利用率")
    @GetMapping("/statisticsEquipmentUseRate")
    public ApiResponseBody<List<EquipmentUseRateVO>> statisticsEquipmentUseRate(@RequestParam String section) {
        return ApiResponseBody.defaultSuccess(iProcessManagementService.statisticsEquipmentUseRate(section));
    }
}