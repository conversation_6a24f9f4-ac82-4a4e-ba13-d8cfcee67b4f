package com.fawkes.project.tbm.controller;


import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.param.SafeRuningParam;
import com.fawkes.project.tbm.common.vo.SafeRuningVO;
import com.fawkes.project.tbm.service.SafeRuningService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 安全运行配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Api(tags = {"安全运行配置"})
@RestController
@RequestMapping("/safeRuning")
public class SafeRuningController {
    @Resource
    private SafeRuningService safeRuningService;

    @ApiOperation("设置")
    @PostMapping("/set")
    public ApiResponseBody set(@RequestBody SafeRuningParam param) {
        safeRuningService.set(param);
        return ApiResponseBody.defaultSuccess();
    }

    @ApiOperation("详情")
    @GetMapping("/get")
    public ApiResponseBody<SafeRuningVO> get() {
        return ApiResponseBody.defaultSuccess(safeRuningService.get());
    }
}

