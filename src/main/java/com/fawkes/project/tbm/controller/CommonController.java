package com.fawkes.project.tbm.controller;


import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.param.FormCommitParam;
import com.fawkes.project.tbm.common.param.FormQueryParam;
import com.fawkes.project.tbm.service.ICommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 表单通用接口
 *
 * <AUTHOR>
 * @date 2019-12-02
 */
@RestController
@Api("表单通用接口")
public class CommonController {

    @Resource
    private ICommonService iCommonService;

    @ApiOperation("表单提交通用接口")
    @PostMapping(path = "/form/commit", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody saveForm(@RequestBody FormCommitParam formCommitParam) throws Exception {
        return iCommonService.saveForm(formCommitParam);
    }

    @ApiOperation("表单查询通用接口--仅限ID为Long类型")
    @PostMapping(path = "/form/query", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody queryForm(@RequestBody FormQueryParam formQueryParam) throws Exception {
        return iCommonService.queryForm(formQueryParam);
    }


}
