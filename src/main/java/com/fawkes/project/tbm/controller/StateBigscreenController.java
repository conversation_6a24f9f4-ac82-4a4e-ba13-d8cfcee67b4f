package com.fawkes.project.tbm.controller;


import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.param.TestDataImportParam;
import com.fawkes.project.tbm.common.utils.excel.ExcelUtil;
import com.fawkes.project.tbm.common.vo.*;
import com.fawkes.project.tbm.service.StateBigscreenService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 状态检测大屏 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Api(tags = {"状态检测大屏"})
@RestController
@RequestMapping("/bigscreen/state")
public class StateBigscreenController {

    @Resource
    private StateBigscreenService stateBigscreenService;

    @ApiOperation("查询设备基础信息")
    @ApiImplicitParam(name = "deviceCode", value = "设备code")
    @GetMapping("/getDeviceBaseInfo")
    public ApiResponseBody<StateDeviceInfoVO> getDeviceBaseInfo(@RequestParam String deviceCode) {
        return ApiResponseBody.defaultSuccess(stateBigscreenService.getDeviceBaseInfo(deviceCode));
    }

    @ApiOperation("查询摄像头列表")
    @ApiImplicitParam(name = "deviceCode", value = "设备code")
    @GetMapping("/listCamera")
    public ApiResponseBody<List<CameraManageVO>> listCamera(@RequestParam String deviceCode) {
        return ApiResponseBody.defaultSuccess(stateBigscreenService.listCamera(deviceCode));
    }

    @ApiOperation("查询有害气体信息列表")
    @ApiImplicitParam(name = "deviceCode", value = "设备code")
    @GetMapping("/listHarmfulGas")
    public ApiResponseBody<List<StateHarmfulGasDeviceVO>> listHarmfulGas(@RequestParam String deviceCode) {
        return ApiResponseBody.defaultSuccess(stateBigscreenService.listHarmfulGas(deviceCode));
    }

    @ApiOperation("查询刀盘系统数据列表")
    @ApiImplicitParam(name = "deviceCode", value = "设备code")
    @GetMapping("/getCutDisk")
    public ApiResponseBody<TbmCutDiskVO> getCutDisk(@RequestParam String deviceCode) {

        return ApiResponseBody.defaultSuccess(stateBigscreenService.getCutDisk(deviceCode));
    }

    @ApiOperation("查询推进系统数据列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "deviceCode", value = "设备code"),
            @ApiImplicitParam(name = "cylinderPressureStr", value = "油箱压力",defaultValue = "cylinder_pressure_one")})
    @GetMapping("/getPropulsionSystem")
    public ApiResponseBody<TbmPropulsionSystemVO> getPropulsionSystem(@RequestParam(value = "deviceCode") String deviceCode,
                                                                      @RequestParam(value = "cylinderPressureStr",defaultValue = "cylinder_pressure_one") String cylinderPressureStr) {
        return ApiResponseBody.defaultSuccess(stateBigscreenService.getPropulsionSystem(deviceCode,cylinderPressureStr));
    }

    @ApiOperation("查询驱动系统数据列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "deviceCode", value = "设备code"),
            @ApiImplicitParam(name = "electricCurrentCode", value = "电机code", defaultValue = "electric_current_one"),
            @ApiImplicitParam(name = "motorTemperature", value = "电机温度", defaultValue = "temperature_one")})
    @GetMapping("/getDriveSystem")
    public ApiResponseBody<TbmDriveSystemVO> getDriveSystem(@RequestParam String deviceCode,
                                                            @RequestParam(defaultValue = "electric_current_one") String electricCurrentCode ,
                                                            @RequestParam(defaultValue = "temperature_one") String motorTemperature) {
        return ApiResponseBody.defaultSuccess(stateBigscreenService.getDriveSystem(deviceCode, electricCurrentCode,motorTemperature));
    }

    @ApiOperation("查询出渣系统数据列表")
    @ApiImplicitParam(name = "deviceCode", value = "设备code")
    @GetMapping("/getTappingSystem")
    public ApiResponseBody<TbmTappingSystemVO> getTappingSystem(@RequestParam String deviceCode) {
        return ApiResponseBody.defaultSuccess(stateBigscreenService.getTappingSystem(deviceCode));
    }

    @ApiOperation("查询姿态检测数据列表")
    @ApiImplicitParam(name = "deviceCode", value = "设备code")
    @GetMapping("/getAttitude")
    public ApiResponseBody<TbmAttitudeVO> getAttitude(@RequestParam String deviceCode) {
        return ApiResponseBody.defaultSuccess(stateBigscreenService.getAttitude(deviceCode));
    }

    @ApiOperation("查询土仓压力数据列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "deviceCode", value = "设备code"),
            @ApiImplicitParam(name = "position", value = "位置")})
    @GetMapping("/listSupportSystem")
    public ApiResponseBody<TbmEarthSupportTotalVO> listSupportSystem(@RequestParam String deviceCode,
                                                                      @RequestParam(required = false) String position) {
        return ApiResponseBody.defaultSuccess(stateBigscreenService.listSupportSystem(deviceCode,position));
    }

    @ApiOperation("导入测试数据")
    @PostMapping("/importTestData")
    public ApiResponseBody importTestData(@RequestParam("deviceCode") String deviceCode,
                                          @RequestPart("file") MultipartFile file) {
        try {
            ExcelUtil<TestDataImportParam> util = new ExcelUtil<>(TestDataImportParam.class);
            List<TestDataImportParam> testDataImportParams = util.importExcel(file.getInputStream(), 0);
            stateBigscreenService.importTestData(testDataImportParams,deviceCode);
        } catch(Exception e) {
            e.printStackTrace();
        }
        return ApiResponseBody.defaultSuccess();
    }

    /**
     * 分页查看告警信息详情列表
     *
     * @param alarmId
     * @param alarmType
     * @return
     */
    @ApiOperation("查询最新告警信息")
    @GetMapping("/pageTbmSystemAlarmDetails")
    @ApiImplicitParams({
                        @ApiImplicitParam(name = "alarmId", value = "告警id"),
                        @ApiImplicitParam(name = "alarmType", value = "告警系统类型，tbm_cut_disk_alarm：刀盘系统；tbm_propulsion_system_alarm：推进系统；tbm_drive_system_alarm：驱动系统；tbm_earth_support_alarm：土仓压力；tbm_tapping_system_alarm：出渣系统；tbm_attitude_alarm：姿态系统；tbm_support_system_alarm：支撑系统"),
                        @ApiImplicitParam(name = "pageNo", value = "页码"),
                        @ApiImplicitParam(name = "pageSize", value = "每页数量")
    })
    public ApiResponseBody<PageInfo<TbmSystemAlarmVO>> pageTbmSystemAlarmDetails(@RequestParam("alarmId") Long alarmId,
                                                                                 @RequestParam("alarmType") String alarmType,
                                                                                 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        return ApiResponseBody.defaultSuccess(stateBigscreenService.page(alarmId, alarmType, pageNo, pageSize));
    }

    /**
     * 查询掘进进度
     *
     * @param deviceCode
     * @return
     */
    @ApiOperation("查询掘进进度")
    @ApiImplicitParam(name = "deviceCode", value = "设备code")
    @GetMapping("/getTunnelingProgress")
    public ApiResponseBody<TunnelingProgressVO> getTunnelingProgress(@RequestParam String deviceCode) {
        return ApiResponseBody.defaultSuccess(stateBigscreenService.getTunnelingProgress(deviceCode));
    }
}