package com.fawkes.project.tbm.controller;


import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.param.HarmfulGasDeviceParam;
import com.fawkes.project.tbm.common.param.HarmfulGasSetParam;
import com.fawkes.project.tbm.common.param.RiskWarningParam;
import com.fawkes.project.tbm.common.query.HarmfulGasDeviceQuery;
import com.fawkes.project.tbm.common.query.RiskWarningQuery;
import com.fawkes.project.tbm.common.vo.HarmfulGasDeviceVO;
import com.fawkes.project.tbm.common.vo.RiskWarningVO;
import com.fawkes.project.tbm.service.CameraManageService;
import com.fawkes.project.tbm.service.HarmfulGasDeviceService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 有害气体设备管理表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Api(tags = {"有害气体设备管理"})
@RestController
@RequestMapping("/gas")
public class HarmfulGasDeviceController {
    @Resource
    private HarmfulGasDeviceService harmfulGasDeviceService;

    @ApiOperation("新增有害气体设备管理")
    @PostMapping("/add")
    public ApiResponseBody add(@RequestBody  @Valid HarmfulGasDeviceParam param) {
        harmfulGasDeviceService.add(param);
        return ApiResponseBody.defaultSuccess();
    }

    @ApiOperation("修改有害气体设备管理")
    @PostMapping("/update")
    public ApiResponseBody update(@RequestBody  @Valid HarmfulGasDeviceParam param) {
        harmfulGasDeviceService.update(param);
        return ApiResponseBody.defaultSuccess();
    }

    @ApiOperation("设置有害气体阈值")
    @PostMapping("/set")
    public ApiResponseBody set(@RequestBody  @Valid HarmfulGasSetParam param) {
        harmfulGasDeviceService.setThreshold(param);
        return ApiResponseBody.defaultSuccess();
    }

    @ApiOperation("有害气体设备管理详情")
    @ApiImplicitParam(name = "id", value = "ID")
    @GetMapping("/get")
    public ApiResponseBody<HarmfulGasDeviceVO> get(@RequestParam String id) {

        return ApiResponseBody.defaultSuccess(harmfulGasDeviceService.get(id));
    }

    @ApiOperation("删除有害气体设备管理")
    @ApiImplicitParam(name = "id", value = "ID")
    @DeleteMapping("/delete")
    public ApiResponseBody delete(@RequestParam String id) {

        return ApiResponseBody.defaultSuccess( harmfulGasDeviceService.delete(id) );
    }

    @ApiOperation("有害气体设备管理列表")
    @PostMapping("/list")
    public ApiResponseBody<PageInfo<HarmfulGasDeviceVO>> list(@RequestBody HarmfulGasDeviceQuery query) {
        return ApiResponseBody.defaultSuccess(harmfulGasDeviceService.list(query));
    }
}

