package com.fawkes.project.tbm.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 第一个接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-12-18 15:01
 */
@Api(tags = {"第一个接口"})
@RestController
public class FirstController {

    @ApiOperation("你好世界")
    @ApiImplicitParam(name = "name", value = "姓名")
    @GetMapping(path = "/helloWorld", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<String> helloWorld(@RequestParam String name) {
        return ApiResponseBody.defaultSuccess("Hello, My Name Is: " + name);
    }
}
