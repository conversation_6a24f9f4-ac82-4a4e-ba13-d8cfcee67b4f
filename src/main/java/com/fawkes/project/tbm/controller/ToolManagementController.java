package com.fawkes.project.tbm.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.enums.DataSourceEnums;
import com.fawkes.project.tbm.common.param.ToolManagementImportResult;
import com.fawkes.project.tbm.common.param.ToolManagementParam;
import com.fawkes.project.tbm.common.query.ToolManagementExportQuery;
import com.fawkes.project.tbm.common.vo.*;
import com.fawkes.project.tbm.service.IToolManagementService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/6/3 18:04
 */
@Api(tags = {"刀具管理"})
@RestController
@RequestMapping("/toolManagement")
public class ToolManagementController {

    @Resource
    private IToolManagementService iToolManagementService;

    /**
     * 分页列表查询
     *
     * @param toolName
     * @param sectionList
     * @param toolLocation
     * @param createName
     * @param createStartDate
     * @param createEndDate
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "分页列表查询", notes = "分页列表查询")
    @GetMapping(value = "/page")
    public ApiResponseBody<PageInfo<ToolManagementVO>> page(@RequestParam(name = "toolName", required = false) String toolName,
                                                            @RequestParam(name = "sectionList", required = false) List<String> sectionList,
                                                            @RequestParam(name = "toolLocation", required = false) String toolLocation,
                                                            @RequestParam(name = "wearValue", required = false) String wearValue,
                                                            @RequestParam(name = "createName", required = false) String createName,
                                                            @RequestParam(name = "createStartDate", required = false) String createStartDate,
                                                            @RequestParam(name = "createEndDate", required = false) String createEndDate,
                                                            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        return ApiResponseBody.defaultSuccess(iToolManagementService.page(sectionList, toolName, toolLocation, wearValue, createName, createStartDate, createEndDate, pageNo, pageSize));
    }

    /**
     * 新增数据
     */
    @ApiOperation("新增")
    @PostMapping("/add")
    public ApiResponseBody<Boolean> add(@RequestBody @Valid ToolManagementParam param) throws IllegalAccessException {
        param.setDataSource(DataSourceEnums.PC.getCode());
        return ApiResponseBody.defaultSuccess(iToolManagementService.add(param));
    }


    /**
     * 详情
     */
    @ApiOperation("详情")
    @GetMapping("/get")
    public ApiResponseBody<ToolManagementVO> get(@RequestParam(name = "id") String id) {
        return ApiResponseBody.defaultSuccess(iToolManagementService.getById(id));
    }


    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping(path = "/delete", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<Boolean> delete(@RequestParam String id) throws IllegalAccessException {
        return ApiResponseBody.defaultSuccess(iToolManagementService.delete(id, DataSourceEnums.PC.getCode()));
    }

    /**
     * 导入
     */
    @ApiOperation("导入")
    @PostMapping("/import")
    public ApiResponseBody<ToolManagementImportResult> importExcel(@RequestParam MultipartFile file, HttpServletResponse response) throws IOException, IllegalAccessException {
        return ApiResponseBody.defaultSuccess(iToolManagementService.importExcel(file, response));
    }


    /**
     * 导出
     *
     * @param query
     * @param response
     * @throws IOException
     */
    @ApiOperation("导出")
    @PostMapping("/export")
    public void export(@RequestBody @Valid ToolManagementExportQuery query, HttpServletResponse response) throws IOException {
        iToolManagementService.export(query, response);
    }


    /**
     * 统计刀具磨损
     */
    @ApiImplicitParams({
            @ApiImplicitParam(name = "section", value = "区域code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "id", value = "刀具位置id", required = true, dataType = "long", paramType = "query")
    })
    @ApiOperation("统计刀具磨损")
    @GetMapping("/statisticsToolWear")
    public ApiResponseBody<List<ToolWearStatisticsVO>> statisticsToolWear(@RequestParam(value = "section") String section,
                                                                          @RequestParam(value = "id" ,required = false) Long toolPositionManageId){
        return ApiResponseBody.defaultSuccess(iToolManagementService.statisticsToolWear(section, toolPositionManageId));
    }

    /**
     * 刀具全局展示
     */
    @ApiImplicitParams({
            @ApiImplicitParam(name = "section", value = "区域code")
    })
    @ApiOperation("刀具全局展示")
    @GetMapping("/toolDisplay")
    public ApiResponseBody<ToolDisplayVO> toolDisplay(@RequestParam(value = "section") String section){
        return ApiResponseBody.defaultSuccess(iToolManagementService.toolDisplay(section));
    }


    /**
     * 刀具磨损比较
     */
    @ApiImplicitParams({
            @ApiImplicitParam(name = "section", value = "区域code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "groupId", value = "刀具位置组id", required = true, dataType = "long", paramType = "query")
    })
    @ApiOperation("刀具磨损比较")
    @GetMapping("/toolWearCompare")
    public ApiResponseBody<List<ToolWearCompareVO>> toolWearCompare(@RequestParam(value = "section") String section,
                                                                    @RequestParam(value = "groupId") Long groupId){

        return ApiResponseBody.defaultSuccess(iToolManagementService.toolWearCompare(section, groupId));
    }

    /**
     * 刀具磨损预警
     */
    @ApiImplicitParams({
            @ApiImplicitParam(name = "section", value = "区域code"),
            @ApiImplicitParam(name = "pageNo", value = "页码"),
            @ApiImplicitParam(name = "pageSize", value = "页大小")
    })
    @ApiOperation("刀具磨损预警")
    @GetMapping("/toolWearEarlyWarning")
    public ApiResponseBody<PageInfo<ToolWearEarlyWarningVO>> toolWearEarlyWarning(@RequestParam(value = "section") String section,
                                                                              @RequestParam(value = "pageNo",required = false) Integer pageNo,
                                                                              @RequestParam(value = "pageSize",required = false) Integer pageSize){
        return ApiResponseBody.defaultSuccess(iToolManagementService.toolWearEarlyWarning(section,pageNo,pageSize));
    }


    /**
     * 刀具磨损规律
     */
    @ApiImplicitParams({
            @ApiImplicitParam(name = "section", value = "区域code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "groupId", value = "刀具位置组id", required = true, dataType = "long", paramType = "query")
    })
    @ApiOperation("刀具磨损规律")
    @GetMapping("/toolWearRule")
    public ApiResponseBody<List<ToolWearRuleVO>> toolWearRule(@RequestParam(value = "section") String section,
                                                              @RequestParam(value = "groupId") Long groupId){
        return ApiResponseBody.defaultSuccess(iToolManagementService.toolWearRule(section,groupId));
    }

}
