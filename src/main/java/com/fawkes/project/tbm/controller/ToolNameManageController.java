package com.fawkes.project.tbm.controller;


import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.vo.ToolNameManageVO;
import com.fawkes.project.tbm.service.ToolNameManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = {"刀具名称管理"})
@RestController
@RequestMapping("/tool/name")
public class ToolNameManageController {


    @Resource
    private ToolNameManageService toolNameManageService;


    @ApiImplicitParams({
            @ApiImplicitParam(name = "section", value = "区域code", required = true, dataType = "string", paramType = "query"),
    })
    @ApiOperation("获取区段下的刀具名称列表")
    @GetMapping("/device")
    public ApiResponseBody<List<ToolNameManageVO>> listToolNames(@RequestParam(value = "section") String section) {
        return ApiResponseBody.defaultSuccess(toolNameManageService.listToolNames(section));
    }

}
