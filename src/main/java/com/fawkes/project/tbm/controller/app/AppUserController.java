package com.fawkes.project.tbm.controller.app;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.service.IAppUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/8/11 17:35
 * @description
 */
@Api(tags = {"app管理"})
@RestController
@RequestMapping("/app")
public class AppUserController {

    @Resource
    private IAppUserService iAppUserService;

    @ApiOperation(value = "发送短信")
    @GetMapping("/msg/send")
    public ApiResponseBody sendMsg(@RequestParam("phone") String phone) {
        return iAppUserService.sendMsg(phone);
    }

    @ApiOperation(value = "验证短信")
    @GetMapping("/msg/verify")
    public ApiResponseBody verifyMsg(@RequestParam("phone") String phone, @RequestParam("code") String code) {
        return iAppUserService.verifyMsg(phone, code);
    }
}