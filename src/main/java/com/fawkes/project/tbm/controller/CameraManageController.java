package com.fawkes.project.tbm.controller;


import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.param.CameraManageParam;
import com.fawkes.project.tbm.common.param.DeviceManageParam;
import com.fawkes.project.tbm.common.vo.CameraManageTreeVO;
import com.fawkes.project.tbm.common.vo.CameraManageVO;
import com.fawkes.project.tbm.common.vo.DeviceManageVO;
import com.fawkes.project.tbm.service.CameraManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 摄像头配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Api(tags = {"监控集成管理"})
@RestController
@RequestMapping("/monitor")
public class CameraManageController {
    @Resource
    private CameraManageService cameraManageService;


    @ApiOperation("新增摄像头")
    @PostMapping("/add")
    public ApiResponseBody add(@RequestBody  @Valid CameraManageParam param) {
        cameraManageService.add(param);
        return ApiResponseBody.defaultSuccess();
    }

    @ApiOperation("修改摄像头")
    @PostMapping("/update")
    public ApiResponseBody update(@RequestBody  @Valid CameraManageParam param) {
        cameraManageService.update(param);
        return ApiResponseBody.defaultSuccess();
    }

    @ApiOperation("摄像头详情")
    @ApiImplicitParam(name = "id", value = "ID")
    @GetMapping("/get")
    public ApiResponseBody<CameraManageVO> get(@RequestParam String id) {

        return ApiResponseBody.defaultSuccess(cameraManageService.get(id));
    }

    @ApiOperation("删除摄像头")
    @ApiImplicitParam(name = "id", value = "ID")
    @DeleteMapping ("/delete")
    public ApiResponseBody delete(@RequestParam String id) {
        ;
        return ApiResponseBody.defaultSuccess(cameraManageService.delete(id));
    }

    @ApiOperation("摄像头列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cameraName", value = "摄像头名称")
    })
    @GetMapping("/list")
    public ApiResponseBody<List<CameraManageTreeVO>> list(@RequestParam String cameraName) {
        return ApiResponseBody.defaultSuccess(cameraManageService.list(cameraName));
    }

    @ApiOperation("摄像头列表-获取工序摄像头")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cameraName", value = "摄像头名称"),
            @ApiImplicitParam(name = "section", value = "区段名称")
    })
    @GetMapping("/listProcess")
    public ApiResponseBody<List<CameraManageVO>> listProcess(@RequestParam(required = false) String cameraName, @RequestParam String section) {
        return ApiResponseBody.defaultSuccess(cameraManageService.listProcess(cameraName, section));
    }
}

