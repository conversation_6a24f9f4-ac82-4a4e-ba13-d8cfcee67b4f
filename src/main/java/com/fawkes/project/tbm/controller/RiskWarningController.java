package com.fawkes.project.tbm.controller;


import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.param.RiskWarningParam;
import com.fawkes.project.tbm.common.query.RiskWarningQuery;
import com.fawkes.project.tbm.common.vo.RiskWarningVO;
import com.fawkes.project.tbm.service.RiskWarningService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

/**
 * <p>
 * 风险源预警表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Api(tags = {"风险源预警"})
@RestController
@RequestMapping("/risk")
public class RiskWarningController {
    @Resource
    private RiskWarningService riskWarningService;

    @ApiOperation("新增风险源")
    @PostMapping("/add")
    public ApiResponseBody add(@RequestBody @Valid RiskWarningParam param) {
        riskWarningService.add(param);
        return ApiResponseBody.defaultSuccess();
    }

    @ApiOperation("修改风险源")
    @PostMapping("/update")
    public ApiResponseBody update(@RequestBody @Valid RiskWarningParam param) {
        riskWarningService.update(param);
        return ApiResponseBody.defaultSuccess();
    }

    @ApiOperation("风险源详情")
    @ApiImplicitParam(name = "id", value = "ID")
    @GetMapping("/get")
    public ApiResponseBody<RiskWarningVO> get(@RequestParam String id) {
        return ApiResponseBody.defaultSuccess(riskWarningService.get(id));
    }

    @ApiOperation("删除风险源")
    @ApiImplicitParam(name = "id", value = "ID")
    @DeleteMapping("/delete")
    public ApiResponseBody delete(@RequestParam String id) {
        return ApiResponseBody.defaultSuccess(riskWarningService.delete(id));
    }

    @ApiOperation("风险源列表")
    @PostMapping("/list")
    public ApiResponseBody<PageInfo<RiskWarningVO>> list(@RequestBody RiskWarningQuery query) {
        return ApiResponseBody.defaultSuccess(riskWarningService.list(query));
    }

    @ApiOperation("风险源导出")
    @PostMapping("/export")
    public void export(
            @RequestBody RiskWarningQuery query,
            HttpServletResponse response
    ) throws IOException {
        riskWarningService.export(query, response);
    }

    @ApiOperation("风险源导入")
    @PostMapping("import")
    public ApiResponseBody importExcel(@RequestParam MultipartFile file, HttpServletResponse response) throws IOException {
        return ApiResponseBody.defaultSuccess(riskWarningService.importExcel(file,response));
    }
}

