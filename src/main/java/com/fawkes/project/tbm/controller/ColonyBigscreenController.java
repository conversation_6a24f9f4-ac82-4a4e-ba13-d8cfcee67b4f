package com.fawkes.project.tbm.controller;


import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.vo.*;
import com.fawkes.project.tbm.service.ColonyBigscreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 集群大屏 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Api(tags = {"集群大屏"})
@RestController
@RequestMapping("/bigscreen/colony")
public class ColonyBigscreenController {
    @Resource
    private ColonyBigscreenService colonyBigscreenService;

    @ApiOperation("查询综合指标")
    @GetMapping("/getComprehensiveIndex")
    public ApiResponseBody<ComprehensiveIndexVO> getComprehensiveIndex() {
        return ApiResponseBody.defaultSuccess(colonyBigscreenService.getComprehensiveIndex());
    }

    @ApiOperation("查询设备基础信息列表")
    @GetMapping("/listDeviceBaseInfo")
    public ApiResponseBody<List<DeviceInfoVO>> listDeviceBaseInfo() {
        return ApiResponseBody.defaultSuccess(colonyBigscreenService.listDeviceBaseInfo());
    }

    @ApiOperation("查询各标段点位信息列表")
    @GetMapping("/listSectionLocation")
    public ApiResponseBody<List<SectionLocationVO>> listSectionLocation() {
        return ApiResponseBody.defaultSuccess(colonyBigscreenService.listSectionLocation());
    }

    @ApiOperation("查询设备运行参数信息列表")
    @GetMapping("/listDeviceRuningInfo")
    public ApiResponseBody<List<DeviceRuningVO>> listDeviceRuningInfo() {
        return ApiResponseBody.defaultSuccess(colonyBigscreenService.listDeviceRuningInfo());
    }

    @ApiOperation("查询掘进日进尺列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deviceCode", value = "编号"),
            @ApiImplicitParam(name = "dateStart", value = "开始日期"),
            @ApiImplicitParam(name = "dateEnd", value = "结束日期")
    })
    @GetMapping("/listPropulsionFootage")
    public ApiResponseBody<List<DailyAdvanceVO>> listPropulsionFootage(@RequestParam String deviceCode,
                                                                       @RequestParam String dateStart,
                                                                       @RequestParam String dateEnd) {
        return ApiResponseBody.defaultSuccess(colonyBigscreenService.listPropulsionFootage(deviceCode,dateStart,dateEnd));
    }

    @ApiOperation("查询风险统计")
    @ApiImplicitParam(name = "deviceCode", value = "设备code")
    @GetMapping("/getRiskStatistics")
    public ApiResponseBody<RiskStatisticsVO> getRiskStatistics(@RequestParam String deviceCode) {
        return ApiResponseBody.defaultSuccess(colonyBigscreenService.getRiskStatistics(deviceCode));
    }


    @ApiOperation("查询风险源预警")
    @ApiImplicitParam(name = "deviceCode", value = "设备code")
    @GetMapping("/listRiskWarning")
    public ApiResponseBody<List<RiskWarningVO>> listRiskWarning(@RequestParam String deviceCode) {
        return ApiResponseBody.defaultSuccess(colonyBigscreenService.listRiskWarning(deviceCode));
    }

    @ApiOperation("查询掘进月进尺列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deviceCode", value = "编号"),
            @ApiImplicitParam(name = "dateStart", value = "开始日期 yyyy-MM"),
            @ApiImplicitParam(name = "dateEnd", value = "结束日期 yyyy-MM")
    })
    @GetMapping("/listMonthPropulsionFootage")
    public ApiResponseBody<List<MonthAdvanceVO>> listMonthPropulsionFootage(@RequestParam String deviceCode,
                                                                            @RequestParam String dateStart,
                                                                            @RequestParam String dateEnd) {
        return ApiResponseBody.defaultSuccess(colonyBigscreenService.listMonthPropulsionFootage(deviceCode,dateStart,dateEnd));
    }
}

