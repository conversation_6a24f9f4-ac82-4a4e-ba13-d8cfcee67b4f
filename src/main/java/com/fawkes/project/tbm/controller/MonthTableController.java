package com.fawkes.project.tbm.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.job.ComprehensiveIndexJob;
import com.fawkes.project.tbm.job.AlarmMonthTableNameInitJob;
import com.fawkes.project.tbm.job.DeviceMonthTableNameInitJob;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = {"月份表管理"})
@RestController
@RequestMapping("/monthTable")
public class MonthTableController {

    @Resource
    private AlarmMonthTableNameInitJob alarmMonthTableNameInitJob;

    @Resource
    private DeviceMonthTableNameInitJob deviceMonthTableNameInitJob;

    @Resource
    private ComprehensiveIndexJob comprehensiveIndexJob;

    /**
     * 初始化告警月份表
     *
     * @return
     */
    @ApiOperation("初始化告警月份表")
    @PostMapping("/initAlarmMonthTable")
    public ApiResponseBody initAlarmMonthTable() {
        alarmMonthTableNameInitJob.initMonthTableAlarm();
        return ApiResponseBody.defaultSuccess();
    }

    /**
     * 初始化设备月份表
     *
     * @return
     */
    @ApiOperation("初始化设备月份表")
    @PostMapping("/initDeviceMonthTable")
    public ApiResponseBody initDeviceMonthTable() {
        deviceMonthTableNameInitJob.initMonthTableDevice();
        return ApiResponseBody.defaultSuccess();
    }

    /**
     * 集群大屏综合指标定时任务
     *
     * @return
     */
    @ApiOperation("集群大屏综合指标定时任务")
    @PostMapping("/generateComprehensiveIndex")
    public ApiResponseBody generateComprehensiveIndex() {
        comprehensiveIndexJob.generateComprehensiveIndex();
        return ApiResponseBody.defaultSuccess();
    }
}
