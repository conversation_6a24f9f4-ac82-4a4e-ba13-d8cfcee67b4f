package com.fawkes.project.tbm.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.vo.ToolPositionManageGroupVO;
import com.fawkes.project.tbm.common.vo.ToolPositionManageResult;
import com.fawkes.project.tbm.common.vo.ToolPositionManageVO;
import com.fawkes.project.tbm.service.ToolPositionManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = {"刀具位置管理"})
@RestController
@RequestMapping("/tool/position")
public class ToolPositionManageController {

    @Resource
    private ToolPositionManageService toolPositionManageService;

    @ApiImplicitParams({
            @ApiImplicitParam(name = "section", value = "区域code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "id", value = "刀具名称ID", required = true, dataType = "long", paramType = "query")
    })
    @ApiOperation("获取区域+刀具下的位置")
    @GetMapping("/name/with/section")
    public ApiResponseBody<List<ToolPositionManageVO>> listPositionsByRegionAndTool(@RequestParam(value = "section") String section,
                                                                                    @RequestParam(value = "id") Long toolNameManageId) {
        return ApiResponseBody.defaultSuccess(toolPositionManageService.listPositionsByRegionAndTool(section, toolNameManageId));
    }


    /**
     * 区域下的所有位置
     * @param section 区域code
     * @return 区域下所有位置
     */
    @ApiOperation("区域下的所有位置")
    @GetMapping("/section")
    public ApiResponseBody<ToolPositionManageResult> listPositionsBySection(@RequestParam(value = "section") String section) {
        return ApiResponseBody.defaultSuccess(toolPositionManageService.listPositionsBySection(section));
    }

    /**
     * 区域下的所有位置分组
     * @param section 区域code
     * @return 区域下所有位置
     */
    @ApiOperation("区域下的所有位置分组")
    @GetMapping("/section/group")
    public ApiResponseBody<List<ToolPositionManageGroupVO>> listGroupVO(@RequestParam(value = "section") String section) {
        return ApiResponseBody.defaultSuccess(toolPositionManageService.listGroupVO(section));
    }

}
