package com.fawkes.project.tbm;

import com.fawkes.project.tbm.common.config.MyBatisPlusConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@EnableDiscoveryClient
@SpringBootApplication
@AutoConfigureBefore(value = MyBatisPlusConfig.class)
public class ProjectTbmApplication {

    public static void main(String[] args) {
        SpringApplication.run(ProjectTbmApplication.class, args);
    }

}
