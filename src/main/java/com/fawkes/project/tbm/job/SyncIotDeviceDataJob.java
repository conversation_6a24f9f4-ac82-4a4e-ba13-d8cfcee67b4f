package com.fawkes.project.tbm.job;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fawkes.project.tbm.common.constants.IOTDeviceConstants;
import com.fawkes.project.tbm.common.model.*;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;

/**
 * 同步IOT设备数据任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SyncIotDeviceDataJob {

    @Resource
    private TbmCutDiskService tbmCutDiskService;

    @Resource
    private TbmDriveSystemService tbmDriveSystemService;

    @Resource
    private TbmPropulsionSystemService tbmPropulsionSystemService;

    @Resource
    private TbmTappingSystemService tbmTappingSystemService;

    @Resource
    private TbmAttitudeService tbmAttitudeService;

    @Resource
    private TbmEarthSupportService tbmEarthSupportService;

    @Resource
    private TbmSupportSystemService tbmSupportSystemService;

    @Resource
    private TbmHarmfulGasService tbmHarmfulGasService;

    @Resource
    private TbmCutDiskRingService tbmCutDiskRingService;

    @Resource
    private TbmAttitudeRingService tbmAttitudeRingService;

    @Resource
    private TbmDriveSystemRingService tbmDriveSystemRingService;

    @Resource
    private TbmTappingSystemRingService tbmTappingSystemRingService;

    @Resource
    private TbmEarthSupportRingService tbmEarthSupportRingService;

    @Resource
    private TbmPropulsionSystemRingService tbmPropulsionSystemRingService;

    @Resource
    private TbmSupportSystemRingService tbmSupportSystemRingService;

    @Value("${iot.domain}")
    private String domain;

    /**
     * 同步IOT设备数据, 每50秒执行一次
     */
    @Scheduled(cron = "*/50 * * * * ?")
    public void syncIotDeviceData() {
        log.info("开始同步IOT设备数据");
        String deviceId = "dgj_0";
        String deviceCode = "01";
        String response = HttpUtil.get(domain + "/api/ypiot/device/instance/" + deviceId + "/properties/latest?deviceId=" + deviceId);
        if (StringUtils.isEmpty(response)) {
            log.error("同步IOT设备数据失败");
            return;
        }

        JSONObject jsonObject = JSONObject.parseObject(response);
        Integer code = jsonObject.getInteger("code");
        if (code != 8000000) {
            log.error("同步IOT设备数据失败, code: {}", code);
            return;
        }

        List<IOTResponse> iotResponseList = JSONObject.parseArray(jsonObject.getString("data"), IOTResponse.class);
        if (CollectionUtils.isEmpty(iotResponseList)) {
            log.error("同步IOT设备数据失败, data为空");
            return;
        }

        //同步IOT设备数据
        syncIotDeviceData(iotResponseList, deviceCode);

        //同步IOT设备环号数据
        syncIotDeviceRingNumData(iotResponseList, deviceCode);
    }

    /**
     * 同步IOT设备环号数据（目前只同步北段）
     *
     * @param iotResponseList
     */
    private void syncIotDeviceRingNumData(List<IOTResponse> iotResponseList, String deviceCode) {
        String ringNum = null;
        Long createTime = null;
        Optional<String> optionalR = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.RING_NUM)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalR.isPresent()) {
            ringNum = optionalR.get();
        }

        Optional<Long> optionalS = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.RING_NUM)).map(IOTResponse::getCreateTime).findFirst();
        if (optionalS.isPresent()) {
            createTime = optionalS.get();
        }

        //同步环号
        if (StringUtils.isNotBlank(ringNum) && createTime != null) {
            // 将时间戳转为当前时间
            LocalDateTime localDateTime = Instant.ofEpochMilli(createTime).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();

            LambdaQueryWrapper<TbmCutDiskRing> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TbmCutDiskRing::getCode, deviceCode);
            queryWrapper.eq(TbmCutDiskRing::getRingNum, ringNum);
            TbmCutDiskRing tbmCutDiskRing = new TbmCutDiskRing();
            tbmCutDiskRing.setCode(deviceCode);
            tbmCutDiskRing.setRingNum(Integer.valueOf(ringNum));
            tbmCutDiskRing.setMaxTime(localDateTime);
            boolean b = tbmCutDiskRingService.saveOrUpdate(tbmCutDiskRing, queryWrapper);
            //log.info("同步IOT刀盘系统设备环号数据: {}", b);

            LambdaQueryWrapper<TbmDriveSystemRing> tbmDriveSystemRingLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tbmDriveSystemRingLambdaQueryWrapper.eq(TbmDriveSystemRing::getCode, deviceCode);
            tbmDriveSystemRingLambdaQueryWrapper.eq(TbmDriveSystemRing::getRingNum, ringNum);
            TbmDriveSystemRing tbmDriveSystemRing = new TbmDriveSystemRing();
            tbmDriveSystemRing.setCode(deviceCode);
            tbmDriveSystemRing.setRingNum(Integer.valueOf(ringNum));
            tbmDriveSystemRing.setMaxTime(localDateTime);
            boolean c = tbmDriveSystemRingService.saveOrUpdate(tbmDriveSystemRing, tbmDriveSystemRingLambdaQueryWrapper);
            //log.info("同步IOT驱动系统设备环号数据: {}", c);

            LambdaQueryWrapper<TbmEarthSupportRing> tbmEarthSupportRingLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tbmEarthSupportRingLambdaQueryWrapper.eq(TbmEarthSupportRing::getCode, deviceCode);
            tbmEarthSupportRingLambdaQueryWrapper.eq(TbmEarthSupportRing::getRingNum, ringNum);
            TbmEarthSupportRing tbmEarthSupportRing = new TbmEarthSupportRing();
            tbmEarthSupportRing.setCode(deviceCode);
            tbmEarthSupportRing.setRingNum(Integer.valueOf(ringNum));
            tbmEarthSupportRing.setMaxTime(localDateTime);
            boolean d = tbmEarthSupportRingService.saveOrUpdate(tbmEarthSupportRing, tbmEarthSupportRingLambdaQueryWrapper);
            //log.info("同步IOT土仓压力设备环号数据: {}", d);

            LambdaQueryWrapper<TbmSupportSystemRing> tbmSupportSystemRingLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tbmSupportSystemRingLambdaQueryWrapper.eq(TbmSupportSystemRing::getCode, deviceCode);
            tbmSupportSystemRingLambdaQueryWrapper.eq(TbmSupportSystemRing::getRingNum, ringNum);
            TbmSupportSystemRing tbmSupportSystemRing = new TbmSupportSystemRing();
            tbmSupportSystemRing.setCode(deviceCode);
            tbmSupportSystemRing.setRingNum(Integer.valueOf(ringNum));
            tbmSupportSystemRing.setMaxTime(localDateTime);
            boolean l = tbmSupportSystemRingService.saveOrUpdate(tbmSupportSystemRing, tbmSupportSystemRingLambdaQueryWrapper);
            //log.info("同步IOT支撑系统设备环号数据: {}", l);

            LambdaQueryWrapper<TbmPropulsionSystemRing> tbmPropulsionSystemRingLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tbmPropulsionSystemRingLambdaQueryWrapper.eq(TbmPropulsionSystemRing::getCode, deviceCode);
            tbmPropulsionSystemRingLambdaQueryWrapper.eq(TbmPropulsionSystemRing::getRingNum, ringNum);
            TbmPropulsionSystemRing tbmPropulsionSystemRing = new TbmPropulsionSystemRing();
            tbmPropulsionSystemRing.setCode(deviceCode);
            tbmPropulsionSystemRing.setRingNum(Integer.valueOf(ringNum));
            tbmPropulsionSystemRing.setMaxTime(localDateTime);
            boolean e = tbmPropulsionSystemRingService.saveOrUpdate(tbmPropulsionSystemRing, tbmPropulsionSystemRingLambdaQueryWrapper);
            //log.info("同步IOT推进系统设备环号数据: {}", e);

            LambdaQueryWrapper<TbmTappingSystemRing> tbmTappingSystemRingLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tbmTappingSystemRingLambdaQueryWrapper.eq(TbmTappingSystemRing::getCode, deviceCode);
            tbmTappingSystemRingLambdaQueryWrapper.eq(TbmTappingSystemRing::getRingNum, ringNum);
            TbmTappingSystemRing tbmTappingSystemRing = new TbmTappingSystemRing();
            tbmTappingSystemRing.setCode(deviceCode);
            tbmTappingSystemRing.setRingNum(Integer.valueOf(ringNum));
            tbmTappingSystemRing.setMaxTime(localDateTime);
            boolean f = tbmTappingSystemRingService.saveOrUpdate(tbmTappingSystemRing, tbmTappingSystemRingLambdaQueryWrapper);
            //log.info("同步IOT出渣系统设备环号数据: {}", f);

            LambdaQueryWrapper<TbmAttitudeRing> tbmAttitudeRingLambdaQueryWrapper = new LambdaQueryWrapper<>();
            tbmAttitudeRingLambdaQueryWrapper.eq(TbmAttitudeRing::getCode, deviceCode);
            tbmAttitudeRingLambdaQueryWrapper.eq(TbmAttitudeRing::getRingNum, ringNum);
            TbmAttitudeRing tbmAttitudeRing = new TbmAttitudeRing();
            tbmAttitudeRing.setCode(deviceCode);
            tbmAttitudeRing.setRingNum(Integer.valueOf(ringNum));
            tbmAttitudeRing.setMaxTime(localDateTime);
            boolean g = tbmAttitudeRingService.saveOrUpdate(tbmAttitudeRing, tbmAttitudeRingLambdaQueryWrapper);
            //log.info("同步IOT姿态设备环号数据: {}", g);
        }
    }

    /**
     * 同步IOT设备数据
     *
     * @param iotResponseList
     */
    private void syncIotDeviceData(List<IOTResponse> iotResponseList, String deviceCode) {
        //处理刀盘系统
        Boolean aBoolean = tbmCutDiskService.syncIotDeviceData(iotResponseList, deviceCode);
        //log.info("同步IOT刀盘系统设备数据结果为: {}", aBoolean);

        //处理推进 系统
        Boolean bBoolean = tbmPropulsionSystemService.syncIotDeviceData(iotResponseList, deviceCode);
        //log.info("同步IOT推进系统设备数据结果为: {}", bBoolean);

        //处理土仓压力
        Boolean cBoolean = tbmEarthSupportService.syncIotDeviceData(iotResponseList, deviceCode);
        //log.info("同步IOT土仓压力设备数据结果为: {}", cBoolean);

        //处理有害气体
        Boolean dBoolean = tbmHarmfulGasService.syncIotDeviceData(iotResponseList, deviceCode);
        //log.info("同步IOT有害气体设备数据结果为: {}", dBoolean);

        //同步螺机系统
        Boolean eBoolean = tbmTappingSystemService.syncTbmTappingSystem(iotResponseList, deviceCode);
        //log.info("同步IOT螺机系统设备数据结果为: {}", eBoolean);

        //导向系统
        Boolean fBoolean = tbmAttitudeService.syncTbmAttitude(iotResponseList, deviceCode);
        //log.info("同步IOT导向系统设备数据结果为: {}", fBoolean);

        //驱动系统
        Boolean gBoolean = tbmDriveSystemService.syncTbmDriveSystem(iotResponseList, deviceCode);
        //log.info("同步IOT驱动系统设备数据结果为: {}", gBoolean);

        //支持系统
        Boolean mBoolean = tbmSupportSystemService.syncTbmSupportSystem(iotResponseList, deviceCode);
        //log.info("同步IOT支持系统设备数据结果为: {}", mBoolean);
    }
}