package com.fawkes.project.tbm.job;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.fawkes.project.tbm.common.config.MyBatisPlusConfig;
import com.fawkes.project.tbm.common.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警月份表初始化任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AlarmMonthTableNameInitJob {

    @Resource
    private TbmCutDiskAlarmMapper tbmCutDiskAlarmMapper;

    @Resource
    private TbmPropulsionSystemAlarmMapper tbmPropulsionSystemAlarmMapper;

    @Resource
    private TbmDriveSystemAlarmMapper tbmDriveSystemAlarmMapper;

    @Resource
    private TbmEarthSupportAlarmMapper tbmEarthSupportAlarmMapper;

    @Resource
    private TbmTappingSystemAlarmMapper tbmTappingSystemAlarmMapper;

    @Resource
    private TbmAttitudeAlarmMapper tbmAttitudeAlarmMapper;

    @Resource
    private TbmSupportSystemAlarmMapper tbmSupportSystemAlarmMapper;

    @Resource
    private TbmCutDiskAlarmDetailsMapper tbmCutDiskAlarmDetailsMapper;

    @Resource
    private TbmDriveSystemAlarmDetailsMapper tbmDriveSystemAlarmDetailsMapper;

    @Resource
    private TbmPropulsionSystemAlarmDetailsMapper tbmPropulsionSystemAlarmDetailsMapper;

    @Resource
    private TbmEarthSupportAlarmDetailsMapper tbmEarthSupportAlarmDetailsMapper;

    @Resource
    private TbmAttitudeAlarmDetailsMapper tbmAttitudeAlarmDetailsMapper;

    @Resource
    private TbmSupportSystemAlarmDetailsMapper tbmSupportSystemAlarmDetailsMapper;

    @Resource
    private TbmTappingSystemAlarmDetailsMapper tbmTappingSystemAlarmDetailsMapper;

    /**
     * 初始化月份表-告警表 每月的最后一天生成下个月的表
     */
    @Scheduled(cron = "0 00 23 28-31 * ?")
    public void initMonthTableAlarm() {
        LocalDateTime now = LocalDateTime.now();
        int year = now.getYear();
        int month = now.getMonthValue();
        List<String> monthTableNameAlarmList = MyBatisPlusConfig.MONTH_TABLE_ALARM_NAME_LIST;
        initMonthTableAlarm(monthTableNameAlarmList, month, year);

        List<String> monthTableNameAlarmDetailsList = MyBatisPlusConfig.MONTH_TABLE_ALARM_DETAILS_NAME_LIST;
        initMonthTableAlarm(monthTableNameAlarmDetailsList, month, year);
    }

    /**
     * 初始化月份表-告警表
     *
     * @param monthTableNameList
     * @param month
     * @param year
     */
    private void initMonthTableAlarm(List<String> monthTableNameList, int month, int year) {
        for (String tableName : monthTableNameList) {
            // 12月 生成下一年1月份的表
            if (month == 12) {
                realInitMonthTableAlarm(tableName,tableName + "_" + (year + 1) + "01");
            } else {
                int nextMonth = month + 1;
                String nextMonthStr = String.valueOf(nextMonth);
                if(nextMonth < 10){
                    nextMonthStr = "0" + nextMonth;
                }
                realInitMonthTableAlarm(tableName, tableName + "_" + year + nextMonthStr);
            }
        }
    }

    /**
     * 初始化月份表-告警表
     *
     * @param originalTableName
     * @param tableName
     */
    private void realInitMonthTableAlarm(String originalTableName, String tableName) {
        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_CUT_DISK_ALARM)) {
            int existTable = tbmCutDiskAlarmMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmCutDiskAlarmMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_PROPULSION_SYSTEM_ALARM)) {
            int existTable = tbmPropulsionSystemAlarmMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmPropulsionSystemAlarmMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_DRIVE_SYSTEM_ALARM)) {
            int existTable = tbmDriveSystemAlarmMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmDriveSystemAlarmMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_EARTH_SUPPORT_ALARM)) {
            int existTable = tbmEarthSupportAlarmMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmEarthSupportAlarmMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_TAPPING_SYSTEM_ALARM)) {
            int existTable = tbmTappingSystemAlarmMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmTappingSystemAlarmMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_ATTITUDE_ALARM)) {
            int existTable = tbmAttitudeAlarmMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmAttitudeAlarmMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_SUPPORT_SYSTEM_ALARM)) {
            int existTable = tbmSupportSystemAlarmMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmSupportSystemAlarmMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_CUT_DISK_ALARM_DETAILS)) {
            int existTable = tbmCutDiskAlarmDetailsMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmCutDiskAlarmDetailsMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_PROPULSION_SYSTEM_ALARM_DETAILS)) {
            int existTable = tbmPropulsionSystemAlarmDetailsMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmPropulsionSystemAlarmDetailsMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_DRIVE_SYSTEM_ALARM_DETAILS)) {
            int existTable = tbmDriveSystemAlarmDetailsMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmDriveSystemAlarmDetailsMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_EARTH_SUPPORT_ALARM_DETAILS)) {
            int existTable = tbmEarthSupportAlarmDetailsMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmEarthSupportAlarmDetailsMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_TAPPING_SYSTEM_ALARM_DETAILS)) {
            int existTable = tbmTappingSystemAlarmDetailsMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmTappingSystemAlarmDetailsMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_ATTITUDE_ALARM_DETAILS)) {
            int existTable = tbmAttitudeAlarmDetailsMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmAttitudeAlarmDetailsMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_SUPPORT_SYSTEM_ALARM_DETAILS)) {
            int existTable = tbmSupportSystemAlarmDetailsMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmSupportSystemAlarmDetailsMapper.createTable(tableName);
            }
        }
    }
}