package com.fawkes.project.tbm.job;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.fawkes.project.tbm.common.config.MyBatisPlusConfig;
import com.fawkes.project.tbm.common.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备月份表名初始化任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeviceMonthTableNameInitJob {

    @Resource
    private TbmCutDiskMapper tbmCutDiskMapper;

    @Resource
    private TbmPropulsionSystemMapper tbmPropulsionSystemMapper;

    @Resource
    private TbmDriveSystemMapper tbmDriveSystemMapper;

    @Resource
    private TbmEarthSupportMapper tbmEarthSupportMapper;

    @Resource
    private TbmTappingSystemMapper tbmTappingSystemMapper;

    @Resource
    private TbmAttitudeMapper tbmAttitudeMapper;

    @Resource
    private TbmSupportSystemMapper tbmSupportSystemMapper;

    @Resource
    private TbmHarmfulGasMapper tbmHarmfulGasMapper;

    /**
     * 初始化月份表-告警表 每月的最后一天生成下个月的表
     */
    @Scheduled(cron = "0 10 23 28-31 * ?")
    public void initMonthTableDevice() {
        LocalDateTime now = LocalDateTime.now();
        int year = now.getYear();
        int month = now.getMonthValue();
        List<String> monthTableNameList = MyBatisPlusConfig.MONTH_TABLE_NAME_LIST;
        initMonthTableDevice(monthTableNameList, month, year);
    }

    /**
     * 初始化月份表-告警表
     *
     * @param monthTableNameList
     * @param month
     * @param year
     */
    private void initMonthTableDevice(List<String> monthTableNameList, int month, int year) {
        for (String tableName : monthTableNameList) {
            // 12月 生成下一年1月份的表
            if (month == 12) {
                realInitMonthTableDevice(tableName,tableName + "_" + (year + 1) + "01");
            } else {
                int nextMonth = month + 1;
                String nextMonthStr = String.valueOf(nextMonth);
                if(nextMonth < 10){
                    nextMonthStr = "0" + nextMonth;
                }
                realInitMonthTableDevice(tableName, tableName + "_" + year + nextMonthStr);
            }
        }
    }

    /**
     * 初始化月份表-告警表
     *
     * @param originalTableName
     * @param tableName
     */
    private void realInitMonthTableDevice(String originalTableName, String tableName) {
        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_CUT_DISK)) {
            int existTable = tbmCutDiskMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmCutDiskMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_PROPULSION_SYSTEM)) {
            int existTable = tbmPropulsionSystemMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmPropulsionSystemMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_DRIVE_SYSTEM)) {
            int existTable = tbmDriveSystemMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmDriveSystemMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_EARTH_SUPPORT)) {
            int existTable = tbmEarthSupportMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmEarthSupportMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_TAPPING_SYSTEM)) {
            int existTable = tbmTappingSystemMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmTappingSystemMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_ATTITUDE)) {
            int existTable = tbmAttitudeMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmAttitudeMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_SUPPORT_SYSTEM)) {
            int existTable = tbmSupportSystemMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmSupportSystemMapper.createTable(tableName);
            }
        }

        if (StringUtils.equals(originalTableName, MyBatisPlusConfig.TBM_HARMFUL_GAS)) {
            int existTable = tbmHarmfulGasMapper.existTable(tableName);
            //不存在表，创建
            if(existTable == 0){
                log.info("创建表：{}", tableName);
                tbmHarmfulGasMapper.createTable(tableName);
            }
        }
    }
}
