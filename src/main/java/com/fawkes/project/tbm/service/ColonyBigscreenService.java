package com.fawkes.project.tbm.service;

import com.fawkes.project.tbm.common.vo.*;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * 集群大屏 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
public interface ColonyBigscreenService {

    List<DeviceInfoVO> listDeviceBaseInfo();

    List<RiskWarningVO> listRiskWarning(String deviceCode);

    ComprehensiveIndexVO getComprehensiveIndex();

    RiskStatisticsVO getRiskStatistics(String deviceCode);

    List<DailyAdvanceVO> listPropulsionFootage(String deviceCode, String dateStart, String dateEnd);

    List<SectionLocationVO> listSectionLocation();

    List<DeviceRuningVO> listDeviceRuningInfo();

    List<MonthAdvanceVO> listMonthPropulsionFootage(String deviceCode, String dateStart, String dateEnd);
}
