package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.constants.IOTDeviceConstants;
import com.fawkes.project.tbm.common.handler.MonthTableNameHandler;
import com.fawkes.project.tbm.common.mapper.TbmEarthSupportMapper;
import com.fawkes.project.tbm.common.model.TbmEarthSupport;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.common.vo.TbmXYVO;
import com.fawkes.project.tbm.service.TbmEarthSupportService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * TBM土仓压力参数表(此表为原始表，数据按月分表) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Service
public class TbmEarthSupportServiceImpl extends ServiceImpl<TbmEarthSupportMapper, TbmEarthSupport> implements TbmEarthSupportService {


    @Resource
    private TbmEarthSupportMapper tbmEarthSupportMapper;

    @Override
    public TbmEarthSupport getLastTbmEarthSupport(String deviceCode) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        TbmEarthSupport tbmEarthSupport = this.lambdaQuery().eq(TbmEarthSupport::getCode, deviceCode)
                .orderByDesc(TbmEarthSupport::getCreateTime).last("LIMIT  1").one();
        MonthTableNameHandler.removeMonthData();
        return tbmEarthSupport;
    }

    @Override
    public List<TbmEarthSupport> getLastTenRingNumTbmEarthSupport(String deviceCode, Integer ringNum) {
        Integer minRingNum = 0;
        if (ringNum - 9 > 0) {
            minRingNum = ringNum - 9;
        }
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        List<TbmEarthSupport> tbmEarthSupports = this.lambdaQuery().eq(TbmEarthSupport::getCode, deviceCode)
                .between(TbmEarthSupport::getRingNum, minRingNum, ringNum).list();
        MonthTableNameHandler.removeMonthData();
        return tbmEarthSupports;
    }

    /**
     * 获取最后十个环号对应的数据
     *
     * @param deviceCode  设备编码
     * @param ringNumList 环号
     * @param position    位置
     */
    @Override
    public List<TbmXYVO> getRingNumTbmEarthSupport(String deviceCode, List<Integer> ringNumList, String position) {
        String tableName = "tbm_earth_support_" + DateUtils.getYyyyMM();
        return tbmEarthSupportMapper.getRingNumTbmEarthSupport(deviceCode, ringNumList, position, tableName);
    }

    @Override
    public Boolean syncIotDeviceData(List<IOTResponse> iotResponseList, String deviceCode) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        String bracePressure = null;
        String ringNum = null;
        Optional<String> optionalS = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.BRACE_PRESSURE)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalS.isPresent()) {
            bracePressure = optionalS.get();
        }

        Optional<String> optionalR = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.RING_NUM)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalR.isPresent()) {
            ringNum = optionalR.get();
        }

        String bracePressureOne = null;
        Optional<String> optionalK = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.POSITION_ONE)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalK.isPresent()) {
            bracePressureOne = optionalK.get();
        }

        String bracePressureTwo = null;
        Optional<String> optionalL = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.POSITION_TWO)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalL.isPresent()) {
            bracePressureTwo = optionalL.get();
        }

        String bracePressureThree = null;
        Optional<String> optionalM = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.POSITION_THREE)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalM.isPresent()) {
            bracePressureThree = optionalM.get();
        }

        String bracePressureFour = null;
        Optional<String> optionalN = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.POSITION_FOUE)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalN.isPresent()) {
            bracePressureFour = optionalN.get();
        }

        String bracePressureFive = null;
        Optional<String> optionalO = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.POSITION_FIVE)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalO.isPresent()) {
            bracePressureFive = optionalO.get();
        }

        String bracePressureSix = null;
        Optional<String> optionalP = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.POSITION_SIX)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalP.isPresent()) {
            bracePressureSix = optionalP.get();
        }

        String bracePressureSeven = null;
        Optional<String> optionalQ = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.POSITION_SEVEN)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalQ.isPresent()) {
            bracePressureSeven = optionalQ.get();
        }

        String bracePressureEight = null;
        Optional<String> optionalZ = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.POSITION_EIGHT)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalZ.isPresent()) {
            bracePressureEight = optionalZ.get();
        }

        TbmEarthSupport tbmEarthSupport = new TbmEarthSupport();
        tbmEarthSupport.setCode(deviceCode);
        if (bracePressure != null) {
            tbmEarthSupport.setBracePressure(new BigDecimal(bracePressure));
        }
        if (ringNum != null) {
            tbmEarthSupport.setRingNum(Integer.valueOf(ringNum));
        }

        tbmEarthSupport.setCreateTime(new Timestamp(System.currentTimeMillis()));
        tbmEarthSupport.setUpdateTime(new Timestamp(System.currentTimeMillis()));

        if (bracePressureOne != null) {
            tbmEarthSupport.setPositionOne(new BigDecimal(bracePressureOne));
        }

        if (bracePressureTwo != null) {
            tbmEarthSupport.setPositionTwo(new BigDecimal(bracePressureTwo));
        }

        if (bracePressureThree != null) {
            tbmEarthSupport.setPositionThree(new BigDecimal(bracePressureThree));
        }

        if (bracePressureFour != null) {
            tbmEarthSupport.setPositionFour(new BigDecimal(bracePressureFour));
        }

        if (bracePressureFive != null) {
            tbmEarthSupport.setPositionFive(new BigDecimal(bracePressureFive));
        }

        if (bracePressureSix != null) {
            tbmEarthSupport.setPositionSix(new BigDecimal(bracePressureSix));
        }

        if (bracePressureSeven != null) {
            tbmEarthSupport.setPositionSeven(new BigDecimal(bracePressureSeven));
        }

        if (bracePressureEight != null) {
            tbmEarthSupport.setPositionEight(new BigDecimal(bracePressureEight));
        }
        int insert = tbmEarthSupportMapper.insert(tbmEarthSupport);
        MonthTableNameHandler.removeMonthData();
        return insert > 0;
    }
}
