package com.fawkes.project.tbm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.project.tbm.common.mapper.ToolManagementMapper;
import com.fawkes.project.tbm.common.mapper.ToolPositionManageMapper;
import com.fawkes.project.tbm.common.model.ToolManagement;
import com.fawkes.project.tbm.common.model.ToolPositionManage;
import com.fawkes.project.tbm.common.utils.ListUtil;
import com.fawkes.project.tbm.common.vo.ToolPositionManageGroupVO;
import com.fawkes.project.tbm.common.vo.ToolPositionManageResult;
import com.fawkes.project.tbm.common.vo.ToolPositionManageVO;
import com.fawkes.project.tbm.service.ToolPositionManageService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ToolPositionManageServiceImpl extends ServiceImpl<ToolPositionManageMapper, ToolPositionManage> implements ToolPositionManageService {


    @Resource
    private ToolManagementMapper toolManagementMapper;
    /**
     * 获获取区域+刀具下的位置
     * @param section 区域
     * @param toolNameManageId 刀具id
     */
    @Override
    public List<ToolPositionManageVO> listPositionsByRegionAndTool(String section, Long toolNameManageId) {
        return getToolPositionManageVOS(section, toolNameManageId,null);
    }


    /**
     * 根据id获取map
     *
     * @param toolPositionManageId 位置id
     * @return <id,Position>
     */
    @Override
    public Map<Long, String> getToolPositionMap(List<Long> toolPositionManageId) {
        if (ListUtil.isEmpty(toolPositionManageId)) {
            return new HashMap<>(1);
        }
        List<ToolPositionManage> list = super.list(new LambdaQueryWrapper<>(ToolPositionManage.class)
                .in(ToolPositionManage::getId, toolPositionManageId)
                .eq(ToolPositionManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .select(ToolPositionManage::getId, ToolPositionManage::getToolPosition));
        return list.stream().collect(Collectors.toMap(ToolPositionManage::getId, ToolPositionManage::getToolPosition));
    }

    /**
     * 获取区域下的所有刀具位置
     *
     * @param section 区域code
     * @return 所有刀具位置
     */
    @Override
    public ToolPositionManageResult listPositionsBySection(String section) {
        List<Long> toolPositionManageIdList = toolManagementMapper.findToolPositionManageIdList(section);
        if (ListUtil.isEmpty(toolPositionManageIdList)){
            return new ToolPositionManageResult();
        }
        List<ToolPositionManageVO> toolPositionManageVOS = this.getToolPositionManageVOS(section, null,toolPositionManageIdList);
        if (ListUtil.isEmpty(toolPositionManageVOS)) {
            return new ToolPositionManageResult();
        }
        //这个区域下的所有刀具位置
        ToolPositionManageResult toolPositionManageResult = new ToolPositionManageResult();
        toolPositionManageResult.setPositionManageVOList(toolPositionManageVOS);

        //查询最新一次更新的刀具位置
        ToolManagement toolManagement = this.toolManagementMapper.selectOne(
                new LambdaQueryWrapper<>(ToolManagement.class)
                        .eq(ToolManagement::getSection, section)
                        .eq(ToolManagement::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                        .orderByDesc(ToolManagement::getUpdateDate)
                        .orderByDesc(ToolManagement::getId)
                        .last("limit 1")
        );
        if (toolManagement != null) {
            toolPositionManageResult.setId(toolManagement.getToolPositionManageId());
        } else {
            //如果没填过给列表第一个id
            toolPositionManageResult.setId(toolPositionManageVOS.get(0).getId());
        }
        return toolPositionManageResult;
    }


    private List<ToolPositionManageVO> getToolPositionManageVOS(String section, Long toolNameManageId,List<Long> toolPositionManageIdList) {
        List<ToolPositionManage> list = super.list(new LambdaQueryWrapper<>(ToolPositionManage.class)
                .eq(StringUtils.isNotBlank(section), ToolPositionManage::getSection, section)
                .eq(toolNameManageId != null, ToolPositionManage::getToolNameManageId, toolNameManageId)
                .in(ListUtil.isNotEmpty(toolPositionManageIdList), ToolPositionManage::getId, toolPositionManageIdList)
                .eq(ToolPositionManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .orderByAsc(ToolPositionManage::getSort));
        return BeanUtil.copyToList(list, ToolPositionManageVO.class);
    }

    /**
     * 查询区域下分组
     *
     * @param section 区域code
     * @return 分组集合
     */
    @Override
    public List<ToolPositionManageGroupVO> listGroupVO(String section) {
        return baseMapper.listGroupVO(section);
    }
}
