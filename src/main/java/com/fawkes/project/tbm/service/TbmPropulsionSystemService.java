package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmPropulsionSystem;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.common.vo.TbmPropulsionSystemXYVO;
import com.fawkes.project.tbm.common.vo.TbmXYVO;

import java.util.List;

/**
 * <p>
 * TBM推进系统参数(此表为原始表，数据按月分表) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
public interface TbmPropulsionSystemService extends IService<TbmPropulsionSystem> {
    // 根据设备编码和油箱压力获取最近的推进系统数据
    TbmPropulsionSystem getLastTbmPropulsionSystem(String deviceCode);

    // 根据设备编码和环号和油箱压力获取最后十个环号对应的 TbmPropulsionSystem 对象
    List<TbmPropulsionSystem> getLastTenRingNumTbmPropulsionSystem(String deviceCode, Integer ringNum);

    /**
     * 获取最后十个环号对应的 TbmPropulsionSystem 对象
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     * @param cylinderPressureStr 油箱压力
     * @return
     */
    List<TbmPropulsionSystemXYVO> getRingNumTbmPropulsionSystem(String deviceCode, List<Integer> ringNumList, String cylinderPressureStr);

    /**
     * 同步IOT数据
     *
     * @param iotResponseList
     * @return
     */
    Boolean syncIotDeviceData(List<IOTResponse> iotResponseList, String deviceCode);
}
