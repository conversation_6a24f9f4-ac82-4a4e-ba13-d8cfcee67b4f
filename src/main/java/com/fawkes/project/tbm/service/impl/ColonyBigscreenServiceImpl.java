package com.fawkes.project.tbm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.fawkes.project.tbm.common.handler.MonthTableNameHandler;
import com.fawkes.project.tbm.common.mapper.StatisticsComprehensiveIndexMapper;
import com.fawkes.project.tbm.common.model.DeviceManage;
import com.fawkes.project.tbm.common.model.RiskStatistics;
import com.fawkes.project.tbm.common.model.RiskWarning;
import com.fawkes.project.tbm.common.model.SafeRuning;
import com.fawkes.project.tbm.common.model.SectionLocation;
import com.fawkes.project.tbm.common.model.StatisticsComprehensiveIndex;
import com.fawkes.project.tbm.common.model.TbmAttitude;
import com.fawkes.project.tbm.common.model.TbmCutDisk;
import com.fawkes.project.tbm.common.model.TbmPropulsionSystem;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.common.utils.ListUtil;
import com.fawkes.project.tbm.common.vo.*;
import com.fawkes.project.tbm.service.ColonyBigscreenService;
import com.fawkes.project.tbm.service.DeviceManageService;
import com.fawkes.project.tbm.service.RiskStatisticsService;
import com.fawkes.project.tbm.service.RiskWarningService;
import com.fawkes.project.tbm.service.SafeRuningService;
import com.fawkes.project.tbm.service.SectionLocationService;
import com.fawkes.project.tbm.service.StatisticsComprehensiveIndexService;
import com.fawkes.project.tbm.service.TbmAttitudeService;
import com.fawkes.project.tbm.service.TbmCutDiskService;
import com.fawkes.project.tbm.service.TbmPropulsionSystemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 摄像头配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Slf4j
@Service
public class ColonyBigscreenServiceImpl implements ColonyBigscreenService {
    @Resource
    private DeviceManageService deviceManageService;

    @Resource
    private RiskWarningService riskWarningService;

    @Resource
    private SafeRuningService safeRuningService;

    @Resource
    private RiskStatisticsService riskStatisticsService;

    @Resource
    private StatisticsComprehensiveIndexService statisticsComprehensiveIndexService;

    @Resource
    private SectionLocationService sectionLocationService;

    @Resource
    private TbmCutDiskService tbmCutDiskService;

    @Resource
    private TbmPropulsionSystemService tbmPropulsionSystemService;
    @Resource
    private TbmAttitudeService tbmAttitudeService;

    @Resource
    private StatisticsComprehensiveIndexMapper statisticsComprehensiveIndexMapper;

    @Override
    public List<DeviceInfoVO> listDeviceBaseInfo() {
        // 设备基础信息
        List<DeviceManage> deviceManages = deviceManageService.list();
        List<DeviceInfoVO> deviceInfoVOS = new ArrayList<>();
        for (DeviceManage deviceManage : deviceManages) {
            DeviceInfoVO deviceInfoVO = BeanUtil.copyProperties(deviceManage, DeviceInfoVO.class);
            // 统计表的数据，当前环数，设备运行状态
            StatisticsComprehensiveIndex comprehensiveIndex = statisticsComprehensiveIndexService.lambdaQuery()
                    .eq(StatisticsComprehensiveIndex::getCode, deviceManage.getCode()).orderByDesc(StatisticsComprehensiveIndex::getStatisticsDate).last("LIMIT 1").one();
            if (Objects.nonNull(comprehensiveIndex)) {
                deviceInfoVO.setCurrentRingNum(String.valueOf(comprehensiveIndex.getRingNum()));
                deviceInfoVO.setStatus(comprehensiveIndex.getRuningState());
                deviceInfoVO.setMileage(String.valueOf(comprehensiveIndex.getMileage()));
            } else {
                deviceInfoVO.setCurrentRingNum("0");
                deviceInfoVO.setStatus("已停机");
                deviceInfoVO.setMileage("0");
            }

            deviceInfoVOS.add(deviceInfoVO);
        }
        return deviceInfoVOS;
    }

    @Override
    public List<RiskWarningVO> listRiskWarning(String deviceCode) {
        // 获取风险列表
        List<RiskWarning> riskWarnings = riskWarningService.lambdaQuery()
                .eq(StringUtils.isNotBlank(deviceCode), RiskWarning::getDeviceCode, deviceCode).list();
        // 获取设备位置里程
        List<String> deviceCodes = riskWarnings.stream().map(RiskWarning::getDeviceCode).distinct().collect(Collectors.toList());
        Map<String, BigDecimal> map = getDeviceMileageMap(deviceCodes);
        // 每个设备取距离最近的3个
        List<RiskWarning> newRiskWarnings = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
            List<RiskWarning> collect = riskWarnings.stream().filter(riskWarning -> riskWarning.getDeviceCode().equals(entry.getKey())
                    && riskWarning.getRiskStart().compareTo(entry.getValue()) > 0).sorted(Comparator.comparing(RiskWarning::getRiskStart)).limit(3).collect(Collectors.toList());
            newRiskWarnings.addAll(collect);
        }

        List<RiskWarningVO> riskWarningVOS = BeanUtil.copyToList(newRiskWarnings, RiskWarningVO.class);
        for (RiskWarningVO riskWarningVO : riskWarningVOS) {
            riskWarningVO.setRiskDistance(riskWarningVO.getRiskStart().subtract(map.get(riskWarningVO.getDeviceCode())));
        }
        return riskWarningVOS;
    }

    @Override
    public ComprehensiveIndexVO getComprehensiveIndex() {
        ComprehensiveIndexVO indexVO = new ComprehensiveIndexVO();
        // 安全运行天数
        SafeRuning safeRuning = safeRuningService.lambdaQuery().orderByDesc(SafeRuning::getId).last("LIMIT 1").one();
        if (Objects.nonNull(safeRuning)) {
            Date runingStart = safeRuning.getRuningStart();
            String dateStr = DateUtil.format(runingStart, "yyyy-MM-dd");
            Date fullDate = DateUtil.parse(dateStr + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
            Date currDate = new Date();
            LocalDate currDate1 = currDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate runingStart1 = fullDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            // 计算天数差
            long daysBetween = ChronoUnit.DAYS.between(runingStart1, currDate1);
            indexVO.setRuningDay(Math.toIntExact(daysBetween));
        } else {
            indexVO.setRuningDay(0);
        }
        // 设备总数
        List<DeviceManage> deviceManages = deviceManageService.list();
        if (CollectionUtils.isEmpty(deviceManages)) {
            indexVO.setDeviceNum(0);
            indexVO.setMileage("0");
            indexVO.setTodayRingNum("0");
        } else {
            indexVO.setDeviceNum(deviceManages.size());
            // 默认都是0
            BigDecimal mileage = new BigDecimal(0);
            Integer todayRingNum = 0;
            indexVO.setMileage(String.valueOf(mileage));
            indexVO.setTodayRingNum(String.valueOf(todayRingNum));
            // 当天日期（LocalDate）
            LocalDate localDate = LocalDate.now();
            Date currentDate = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            // 查询统计的数据
            List<StatisticsComprehensiveIndex> indexList = statisticsComprehensiveIndexService.lambdaQuery()
                    .eq(StatisticsComprehensiveIndex::getStatisticsDate, currentDate).list();
            if (CollectionUtils.isEmpty(indexList)) {
                indexVO.setRuningDay(0);
                return indexVO;
            }
            Map<String, List<StatisticsComprehensiveIndex>> listMap = indexList.stream().collect(Collectors.groupingBy(StatisticsComprehensiveIndex::getCode));

            for (DeviceManage deviceManage : deviceManages) {
                // 累计里程，今日推进环数
                List<StatisticsComprehensiveIndex> indices = listMap.get(deviceManage.getCode());
                if (CollectionUtils.isEmpty(indices)) {
                    continue;
                }
                mileage = mileage.add(indices.get(0).getMileage());
                todayRingNum += indices.get(0).getTodayRingNum();
            }
            indexVO.setMileage(String.valueOf(mileage));
            indexVO.setTodayRingNum(String.valueOf(todayRingNum));
        }
        // 写死
//        indexVO.setRuningDay(0);
        return indexVO;
    }

    @Override
    public RiskStatisticsVO getRiskStatistics(String deviceCode) {
        RiskStatisticsVO riskStatisticsVO = new RiskStatisticsVO();
        Integer pass = 0;
        Integer ing = 0;
        Integer near = 0;
        Integer remote = 0;
        List<RiskWarning> riskStatistics = riskWarningService.lambdaQuery().eq(StringUtils.isNotBlank(deviceCode), RiskWarning::getDeviceCode, deviceCode).list();
        List<String> deviceCodes = riskStatistics.stream().map(RiskWarning::getDeviceCode).distinct().collect(Collectors.toList());
        // 获取设备位置里程
        Map<String, BigDecimal> map = getDeviceMileageMap(deviceCodes);
        for (RiskWarning riskStatistic : riskStatistics) {
            double riskStart = riskStatistic.getRiskStart().doubleValue();
            double riskEnd = riskStatistic.getRiskEnd().doubleValue();
            double risk = map.get(riskStatistic.getDeviceCode()).doubleValue();
            // 通过的
            if (0 < risk - riskEnd) {
                pass += 1;
            } else if ((0 < riskStart - risk) && (riskStart - risk <= 50)) {
                // 邻近风险(<50)
                near += 1;
            } else if (50 < riskStart - risk) {
                // 远距离风险(>50)
                remote += 1;
            } else {
                // 正在通过的
                ing += 1;
            }

        }
        riskStatisticsVO.setPass(pass);
        riskStatisticsVO.setIng(ing);
        riskStatisticsVO.setNear(near);
        riskStatisticsVO.setRemote(remote);
        return riskStatisticsVO;
    }

    /**
     * 获取设备当前位置里程
     * @param deviceCodes
     * @return
     */
    private Map<String, BigDecimal> getDeviceMileageMap(List<String> deviceCodes) {
        Map<String, BigDecimal> map = new HashMap();
        for (String code : deviceCodes) {
            StatisticsComprehensiveIndex comprehensiveIndex = statisticsComprehensiveIndexService.lambdaQuery()
                    .eq(StatisticsComprehensiveIndex::getCode, code).orderByDesc(StatisticsComprehensiveIndex::getStatisticsDate).last("LIMIT 1").one();
            if(Objects.nonNull(comprehensiveIndex)){
                map.put(code, comprehensiveIndex.getMileage());
            }else{
                map.put(code, BigDecimal.valueOf(0));
            }
        }
        return map;
    }

    @Override
    public List<DailyAdvanceVO> listPropulsionFootage(String deviceCode, String dateStart, String dateEnd) {
        List<DailyAdvanceVO> dailyAdvanceVOS = new ArrayList<>();
        List<String> dateList = new ArrayList<>();
        // 如果没有任何条件，默认查询全部的设备，最近7天的数据
        if (StringUtils.isBlank(dateStart) || StringUtils.isBlank(dateEnd)) {
            dateList = DateUtils.getBeforeSevenDays();
        } else {
            try {
                dateList = DateUtils.getDateList(dateStart, dateEnd, "yyyy-MM-dd");
            } catch (Exception e) {
                log.info(e.getMessage());
            }
        }
        List<StatisticsComprehensiveIndex> indexList = statisticsComprehensiveIndexService.lambdaQuery()
                .between(StatisticsComprehensiveIndex::getStatisticsDate, dateList.get(0), dateList.get(dateList.size() - 1))
                .eq(StringUtils.isNotBlank(deviceCode), StatisticsComprehensiveIndex::getCode, deviceCode).list();
        if (CollectionUtils.isEmpty(indexList)) {
            return dailyAdvanceVOS;
        }
        Map<Date, List<StatisticsComprehensiveIndex>> dateListMap = indexList.stream().collect(Collectors.groupingBy(StatisticsComprehensiveIndex::getStatisticsDate));
        // 按照日期填充数据
        DailyAdvanceVO dailyAdvanceVO;
        for (String dateStr : dateList) {
            dailyAdvanceVO = new DailyAdvanceVO();
            // 日进尺
            Date date = DateUtils.dateTime("yyyy-MM-dd", dateStr);
            List<StatisticsComprehensiveIndex> indices = dateListMap.get(date);
            if (CollectionUtils.isNotEmpty(indices)) {
                BigDecimal reduce = indices.stream().map(StatisticsComprehensiveIndex::getTodayMileage)
                        .filter(Objects::nonNull)       // 过滤空值
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                dailyAdvanceVO.setDailyAdvance(String.valueOf(reduce));
            } else {
                dailyAdvanceVO.setDailyAdvance("0.00");
            }
            // 日期
            dailyAdvanceVO.setDate(DateUtils.format(date, "MM/dd"));
            dailyAdvanceVOS.add(dailyAdvanceVO);
        }

        return dailyAdvanceVOS;
    }

    @Override
    public List<SectionLocationVO> listSectionLocation() {
        List<SectionLocationVO> sectionLocationVOS = new ArrayList<>();
        List<SectionLocation> sectionLocations = sectionLocationService.list();
        if (CollectionUtils.isEmpty(sectionLocations)) {
            return sectionLocationVOS;
        }
        SectionLocationVO sectionLocationVO = null;
        Map<String, List<SectionLocation>> listMap = sectionLocations.stream().collect(Collectors.groupingBy(SectionLocation::getSection));
        for (Map.Entry<String, List<SectionLocation>> entry : listMap.entrySet()) {
            sectionLocationVO = new SectionLocationVO();
            // 标段
            sectionLocationVO.setSection(entry.getKey());
            // 位置
            List<SectionLocation> locations = entry.getValue();
            List<LocationVO> locationVOS = BeanUtil.copyToList(locations, LocationVO.class);
            sectionLocationVO.setLocation(locationVOS);
            sectionLocationVOS.add(sectionLocationVO);
        }
        return sectionLocationVOS;
    }

    @Override
    public List<DeviceRuningVO> listDeviceRuningInfo() {
        List<DeviceRuningVO> deviceRuningVOS = new ArrayList<>();
        DeviceRuningVO deviceRuningVO = null;
        List<DeviceManage> deviceManages = deviceManageService.list();
        List<SectionLocation> sectionLocations = sectionLocationService.list();
        Map<String, List<SectionLocation>> listMap = ListUtil.toGroup(sectionLocations,SectionLocation::getSection);
        if (CollectionUtils.isNotEmpty(deviceManages)) {
            for (DeviceManage deviceManage : deviceManages) {
                deviceRuningVO = new DeviceRuningVO();
                deviceRuningVO.setName(deviceManage.getName());
                deviceRuningVO.setCode(deviceManage.getCode());
                deviceRuningVO.setDeviceCode(deviceManage.getDeviceCode());
                // 默认0
                deviceRuningVO.setActualRpm("0");
                deviceRuningVO.setCutDiskTorchque("0");
                deviceRuningVO.setPenetration("0");
                deviceRuningVO.setThrust("0");
                deviceRuningVO.setPropSpeed("0");
                MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
                // 刀盘系统：刀盘转速、刀盘扭矩、贯入度
                TbmCutDisk tbmCutDisk = tbmCutDiskService.lambdaQuery().eq(TbmCutDisk::getCode, deviceManage.getCode()).orderByDesc(TbmCutDisk::getCreateTime).last("LIMIT 1").one();
                if (Objects.nonNull(tbmCutDisk)) {
                    deviceRuningVO.setActualRpm(String.valueOf(tbmCutDisk.getActualRpm()));
                    deviceRuningVO.setCutDiskTorchque(String.valueOf(tbmCutDisk.getCutDiskTorchque()));
                    deviceRuningVO.setPenetration(String.valueOf(tbmCutDisk.getPenetration()));
                }

                // 推进系统：总推进力、推进速度
                TbmPropulsionSystem tbmPropulsionSystem = tbmPropulsionSystemService.lambdaQuery().eq(TbmPropulsionSystem::getCode, deviceManage.getCode()).orderByDesc(TbmPropulsionSystem::getCreateTime).last("LIMIT 1").one();
                if (Objects.nonNull(tbmPropulsionSystem)) {
                    deviceRuningVO.setThrust(String.valueOf(tbmPropulsionSystem.getThrust()));
                    deviceRuningVO.setPropSpeed(String.valueOf(tbmPropulsionSystem.getPropSpeed()));
                }
                // 经纬度
                TbmAttitude tbmAttitude = tbmAttitudeService.lambdaQuery().eq(TbmAttitude::getCode, deviceManage.getCode()).orderByDesc(TbmAttitude::getCreateTime).last("LIMIT 1").one();
                MonthTableNameHandler.removeMonthData();

                List<SectionLocation> sectionLocationList = listMap.get(deviceManage.getSection());
                //兼容处理，默认已停机（目前只有北段有盾构机接入）
                deviceRuningVO.setRuningState("已停机");
                if(Objects.nonNull(tbmAttitude)){
                    deviceRuningVO.setRuningState(tbmAttitude.getRuningState());
                    deviceRuningVO.setLatitude(tbmAttitude.getLatitude());
                    deviceRuningVO.setLatitudeGc(tbmAttitude.getLatitudeGc());
                    if(StringUtils.isEmpty(tbmAttitude.getLatitude())){
                        if(ListUtil.isNotEmpty(sectionLocationList)){
                            SectionLocation sectionLocation = sectionLocationList.get(0);
                            deviceRuningVO.setLatitude(sectionLocation.getLatitude());
                            deviceRuningVO.setLatitudeGc(sectionLocation.getLatitude());
                        }
                    }
                    deviceRuningVO.setLongitude(tbmAttitude.getLongitude());
                    deviceRuningVO.setLongitudeGc(tbmAttitude.getLongitudeGc());
                    if(StringUtils.isEmpty(tbmAttitude.getLongitude())){
                        if(ListUtil.isNotEmpty(sectionLocationList)){
                            SectionLocation sectionLocation = sectionLocationList.get(0);
                            deviceRuningVO.setLongitude(sectionLocation.getLongitude());
                            deviceRuningVO.setLongitudeGc(sectionLocation.getLongitude());
                        }
                    }
                } else {
                    if(ListUtil.isNotEmpty(sectionLocationList)){
                        //这里是静态的，除了北段的，目前其他段都是静态的
                        SectionLocation sectionLocation = sectionLocationList.get(0);
                        deviceRuningVO.setLatitude(sectionLocation.getLatitudeWgs());
                        //高德
                        deviceRuningVO.setLatitudeGc(sectionLocation.getLatitude());
                        deviceRuningVO.setLongitude(sectionLocation.getLongitudeWgs());
                        //高德
                        deviceRuningVO.setLongitudeGc(sectionLocation.getLongitude());
                    }
                }
                deviceRuningVOS.add(deviceRuningVO);
            }
        }
        return deviceRuningVOS;
    }

    @Override
    public List<MonthAdvanceVO> listMonthPropulsionFootage(String deviceCode, String dateStart, String dateEnd) {
        List<String> monthList = new ArrayList<>();
        // 如果没有任何条件，默认查询全部的设备，默认展示当月份+前6个月的数据
        if (StringUtils.isBlank(dateStart) || StringUtils.isBlank(dateEnd)) {
            monthList = DateUtils.getBeforeSixMonth("2025-07-01");
        } else {
            try {
                monthList = DateUtils.getMonthDateList(dateStart, dateEnd, "yyyy-MM");
            } catch (Exception e) {
                log.info(e.getMessage());
            }
        }

        List<MonthAdvanceVO> monthAdvanceVOList = statisticsComprehensiveIndexMapper.listMonthPropulsionFootage(deviceCode, monthList);
        if(CollectionUtils.isNotEmpty(monthAdvanceVOList)){
            for (MonthAdvanceVO monthAdvanceVO : monthAdvanceVOList) {
                monthAdvanceVO.setMonth(DateUtils.getYyMm(monthAdvanceVO.getMonth()));
            }
        }
        return monthAdvanceVOList;
    }
}