package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmAttitude;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.common.vo.TbmAttitudeXYVO;

import java.util.List;

/**
 * <p>
 * TBM姿态参数表(此表为原始表，数据按月分表) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
public interface TbmAttitudeService extends IService<TbmAttitude> {
    // 根据设备编码获取最近的数据
    TbmAttitude getLastTbmAttitude(String deviceCode);

    // 根据设备编码和环号获取最后十个环号对应的数据
    List<TbmAttitude> getLastTenRingNumTbmAttitude(String deviceCode, Integer ringNum);

    /**
     * 获取最后十个环号对应的数据
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     * @return
     */
    List<TbmAttitudeXYVO> getRingNumTbmAttitude(String deviceCode, List<Integer> ringNumList);



    /**
     * 同步导向系统
     * @param iotResponseList
     * @return
     */
    Boolean syncTbmAttitude(List<IOTResponse> iotResponseList, String deviceCode);
}
