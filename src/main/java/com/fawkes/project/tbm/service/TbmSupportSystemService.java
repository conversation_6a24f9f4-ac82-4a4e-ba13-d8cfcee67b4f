package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmSupportSystem;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.common.vo.TbmXYVO;

import java.util.List;

/**
 * <p>
 * TBM支撑系统参数表(此表为原始表，数据按月分表) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
public interface TbmSupportSystemService extends IService<TbmSupportSystem> {

    /**
     * 根据设备编码获取最近的数据
     *
     * @param deviceCode
     * @return
     */
    TbmSupportSystem getLastTbmEarthSupport(String deviceCode);

    /**
     * 获取最后十个环号对应的数据
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     * @param position 位置
     * @return
     */
    List<TbmXYVO> getRingNumTbmSupportSystem(String deviceCode, List<Integer> ringNumList, String position);

    /**
     * 同步螺机系统
     *
     * @param iotResponseList
     */
    Boolean syncTbmSupportSystem(List<IOTResponse> iotResponseList, String deviceCode);
}
