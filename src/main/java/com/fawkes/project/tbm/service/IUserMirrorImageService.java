package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.model.UserMirrorImage;
import com.fawkes.project.tbm.common.param.UserMirrorImageParam;

/**
 * <AUTHOR>
 */
public interface IUserMirrorImageService extends IService<UserMirrorImage> {

    /**
     * 用户注册
     *
     * @param userMirrorImageParam
     * @return
     */
    ApiResponseBody userRegister(UserMirrorImageParam userMirrorImageParam);

    /**
     * 获取填报权限目录树
     *
     * @return
     */
    ApiResponseBody functionalTree();

    /**
     * 白名单详情查询
     *
     * @return
     */
    ApiResponseBody details(String id);

    /**
     * 白名单分页列表查询
     *
     * @param page
     * @param size
     * @param name
     * @return
     */
    ApiResponseBody pageList(Integer page, Integer size, String name);

    /**
     * 用户修改
     *
     * @param userMirrorImageParam
     * @return
     */
    ApiResponseBody userUpdate(UserMirrorImageParam userMirrorImageParam);
}