package com.fawkes.project.tbm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.mapper.SafeRuningMapper;
import com.fawkes.project.tbm.common.model.RiskWarning;
import com.fawkes.project.tbm.common.model.SafeRuning;
import com.fawkes.project.tbm.common.param.SafeRuningParam;
import com.fawkes.project.tbm.common.vo.RiskWarningVO;
import com.fawkes.project.tbm.common.vo.SafeRuningVO;
import com.fawkes.project.tbm.service.SafeRuningService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 安全运行配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Service
public class SafeRuningServiceImpl extends ServiceImpl<SafeRuningMapper, SafeRuning> implements SafeRuningService {

    @Override
    public void set(SafeRuningParam param) {
        SafeRuning safeRuning = BeanUtil.copyProperties(param, SafeRuning.class);
        this.saveOrUpdate(safeRuning);
    }

    @Override
    public SafeRuningVO get() {
        SafeRuning safeRuning = this.lambdaQuery().orderByDesc(SafeRuning::getId).last("LIMIT 1").one();
        if(Objects.isNull(safeRuning)){
            return null;
        }
        return BeanUtil.copyProperties(safeRuning, SafeRuningVO.class);

    }
}
