package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.mapper.TbmAttitudeRingMapper;
import com.fawkes.project.tbm.common.model.TbmAttitudeRing;
import com.fawkes.project.tbm.service.TbmAttitudeRingService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version TbmAttitudeRingServiceImpl 2025/6/12 9:58
 */
@Service
public class TbmAttitudeRingServiceImpl
        extends ServiceImpl<TbmAttitudeRingMapper, TbmAttitudeRing>
        implements TbmAttitudeRingService {
    /**
     * 多条件查询
     */
    @Override
    public List<TbmAttitudeRing> queryList(LambdaQueryWrapper<TbmAttitudeRing> wrapper) {
        return this.list(wrapper);
    }

    /**
     * 批量新增
     */
    @Override
    public Boolean addBatch(List<TbmAttitudeRing> list) {
        return this.saveBatch(list);
    }

    /**
     * 条件修改
     */
    @Override
    public Boolean edit(LambdaUpdateWrapper<TbmAttitudeRing> wrapper) {
        return this.update(wrapper);
    }
}
