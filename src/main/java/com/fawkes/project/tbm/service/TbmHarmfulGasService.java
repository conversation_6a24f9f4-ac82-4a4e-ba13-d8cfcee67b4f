package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmHarmfulGas;
import com.fawkes.project.tbm.common.vo.IOTResponse;

import java.util.List;

/**
 * <p>
 * TBM有害气体参数表(此表为原始表，数据按月分表) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
public interface TbmHarmfulGasService extends IService<TbmHarmfulGas> {


    List<TbmHarmfulGas>  selectByCodeInLastOne(List<String> codes);

    /**
     * 同步IOT数据
     *
     * @param iotResponseList
     * @return
     */
    Boolean syncIotDeviceData(List<IOTResponse> iotResponseList, String deviceCode);
}
