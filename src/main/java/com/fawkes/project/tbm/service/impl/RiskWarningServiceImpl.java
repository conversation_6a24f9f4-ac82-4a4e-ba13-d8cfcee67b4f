package com.fawkes.project.tbm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.core.exception.BusinessException;
import com.fawkes.project.tbm.common.mapper.RiskWarningMapper;
import com.fawkes.project.tbm.common.model.DeviceManage;
import com.fawkes.project.tbm.common.model.RiskWarning;
import com.fawkes.project.tbm.common.param.RiskWarningImportResult;
import com.fawkes.project.tbm.common.param.RiskWarningParam;
import com.fawkes.project.tbm.common.query.RiskWarningQuery;
import com.fawkes.project.tbm.common.tools.verify.ObjectValidator;
import com.fawkes.project.tbm.common.tools.verify.config.VerifyConfig;
import com.fawkes.project.tbm.common.tools.verify.domain.RowVerifyError;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.common.utils.ListUtil;
import com.fawkes.project.tbm.common.utils.MyEntityTool;
import com.fawkes.project.tbm.common.vo.*;
import com.fawkes.project.tbm.service.DeviceManageService;
import com.fawkes.project.tbm.service.RiskWarningService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <p>
 * 风险源预警表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Service
@Slf4j
public class RiskWarningServiceImpl extends ServiceImpl<RiskWarningMapper, RiskWarning> implements RiskWarningService {

    @Resource
    private DeviceManageService deviceManageService;

    private static final String REGEX = "^[1-9]\\d{0,7}(\\.\\d{1,3})?$";

    /**
     * 对象验证器
     */
    @Resource
    private ObjectValidator objectValidator;

    @Override
    public PageInfo<RiskWarningVO> list(RiskWarningQuery query) {
        PageHelper.startPage(query.getPageNo(), query.getPageSize());
        List<RiskWarning> riskWarnings = this.lambdaQuery().like(StringUtils.isNotBlank(query.getDeviceName()), RiskWarning::getDeviceName, query.getDeviceName())
                .like(StringUtils.isNotBlank(query.getRiskName()), RiskWarning::getRiskName, query.getRiskName())
                .ge(StringUtils.isNotBlank(query.getRiskDistanceStart()), RiskWarning::getRiskStart, query.getRiskDistanceStart())
                .le(StringUtils.isNotBlank(query.getRiskDistanceEnd()), RiskWarning::getRiskEnd, query.getRiskDistanceEnd())
                .orderByDesc(RiskWarning::getUpdateDate).list();
        if (CollectionUtils.isEmpty(riskWarnings)) {
            return new PageInfo<>();
        }
        List<RiskWarningVO> riskWarningVOS = BeanUtil.copyToList(riskWarnings, RiskWarningVO.class);
        return ListUtil.pageConvert(riskWarnings, riskWarningVOS);
    }

    @Override
    public void add(RiskWarningParam param) {
        String regex = "^[1-9]\\d{0,7}(\\.\\d{1,3})?$";
        if (!param.getRiskStart().matches(regex) || !param.getRiskEnd().matches(regex)) {
            throw new BusinessException("里程范围输入正数，整数最大8位，小数最大2位");
        }
        RiskWarning riskWarning = BeanUtil.copyProperties(param, RiskWarning.class);
        riskWarning.setRiskStart(new BigDecimal(param.getRiskStart()));
        riskWarning.setRiskEnd(new BigDecimal(param.getRiskEnd()));
        this.save(riskWarning);
    }

    @Override
    public void update(RiskWarningParam param) {
        String regex = "^[1-9]\\d{0,7}(\\.\\d{1,3})?$";
        if (!param.getRiskStart().matches(regex) || !param.getRiskEnd().matches(regex)) {
            throw new BusinessException("里程范围输入正数，整数最大8位，小数最大2位");
        }
        RiskWarning riskWarning = BeanUtil.copyProperties(param, RiskWarning.class);
        riskWarning.setRiskStart(new BigDecimal(param.getRiskStart()));
        riskWarning.setRiskEnd(new BigDecimal(param.getRiskEnd()));
        this.updateById(riskWarning);
    }

    @Override
    public boolean delete(String id) {
        return removeById(id);
    }

    @Override
    public RiskWarningVO get(String id) {
        RiskWarning riskWarning = this.getById(id);
        return BeanUtil.copyProperties(riskWarning, RiskWarningVO.class);
    }

    @Override
    public void export(RiskWarningQuery query, HttpServletResponse response) throws IOException {
        List<Long> idList = query.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            throw new BusinessException("请选择导出数据");
        }
        List<RiskWarning> riskWarnings = this.lambdaQuery().in(RiskWarning::getId, idList).orderByDesc(RiskWarning::getUpdateDate).list();
        List<RiskWarningExportVO> riskWarningExportVOS = BeanUtil.copyToList(riskWarnings, RiskWarningExportVO.class);
        exportData(response, riskWarningExportVOS, "风险源预警");
    }

    /**
     * 导出数据
     *
     * @param response
     * @param riskWarningExportVOS
     * @throws IOException
     */
    private void exportData(HttpServletResponse response, List<RiskWarningExportVO> riskWarningExportVOS, String fileName) throws IOException {
        ServletOutputStream outputStream = response.getOutputStream();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(outputStream)
                .head(RiskWarningExportVO.class)
                .excelType(ExcelTypeEnum.XLSX)
                .sheet("sheet")
                .doWrite(riskWarningExportVOS);

        outputStream.flush();
        outputStream.close();
    }

    @Override
    public RiskWarningImportResult importExcel(MultipartFile file, HttpServletResponse response) throws IOException {
        InputStream inputStream = file.getInputStream();
        List<RiskWarningExportErrorVO> excelList = EasyExcel.read(inputStream).head(RiskWarningExportErrorVO.class).sheet(0).doReadSync();
        if (CollectionUtils.isEmpty(excelList)) {
            throw new BusinessException("excel文件读取错误！请检查文件");
        }

        ListUtil.forI(excelList, (index, vo) -> vo.setSerialNumber(index + 1));

        VerifyConfig<RiskWarningExportErrorVO> verifyConfig = RiskWarningExportErrorVO.getVerifyConfig();
        List<RowVerifyError<RiskWarningExportErrorVO>> verifyErrorList = this.objectValidator.verifyImportList(excelList, verifyConfig);
        excelList = RowVerifyError.filterErrorData(excelList, verifyErrorList);
        if (ListUtil.isEmpty(excelList)) {
            return new RiskWarningImportResult(verifyErrorList);
        }

        // 设备信息
        List<DeviceManage> deviceManages = deviceManageService.list();
        if (CollectionUtils.isEmpty(deviceManages)) {
            throw new BusinessException("设备信息不存在，请先维护设备信息");
        }
        Map<String, String> stringMap = deviceManages.stream().collect(Collectors.toMap(DeviceManage::getName, DeviceManage::getCode));

        for (RiskWarningExportErrorVO riskWarningExportErrorVO : excelList) {
            List<String> errorMsgList = getErrorMsgList(riskWarningExportErrorVO, stringMap);
            // 设备code
            riskWarningExportErrorVO.setDeviceCode(stringMap.get(riskWarningExportErrorVO.getDeviceName()));

            if(CollectionUtils.isNotEmpty(errorMsgList)){
                RowVerifyError<RiskWarningExportErrorVO> verifyError = new RowVerifyError<>();
                verifyError.setRowNo(riskWarningExportErrorVO.getSerialNumber());
                verifyError.setErrorObject(riskWarningExportErrorVO);
                verifyError.setErrorMsgList(errorMsgList);
                verifyErrorList.add(verifyError);
            }
        }

        excelList = RowVerifyError.filterErrorData(excelList, verifyErrorList);
        if (ListUtil.isEmpty(excelList)) {
            return new RiskWarningImportResult(verifyErrorList);
        }

        RiskWarningImportResult result = new RiskWarningImportResult();
        result.setErrorList(verifyErrorList);
        List<RiskWarningVO> riskWarningVOList = BeanUtil.copyToList(excelList, RiskWarningVO.class);
        result.setRight(riskWarningVOList);

        if (CollectionUtils.isNotEmpty(result.getError())) {
            return result;
        }

        //数据入库
        List<RiskWarning> riskWarningList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(excelList)){
            for (RiskWarningExportErrorVO riskWarningExportErrorVO : excelList){
                RiskWarning riskWarning = BeanUtil.toBean(riskWarningExportErrorVO, RiskWarning.class);
                MyEntityTool.insertEntityWithNameSilent(riskWarning);
                riskWarningList.add(riskWarning);
            }
        }
        boolean saveBatch = this.saveBatch(riskWarningList);
        log.info("function=insertEntityWithNameSilent, saveBatch:{}", saveBatch);
        return result;
    }

    /**
     * 校验导入参数
     *
     * @param riskWarningExportErrorVO
     * @return
     */
    private List<String> getErrorMsgList(RiskWarningExportErrorVO riskWarningExportErrorVO, Map<String, String> stringMap) {
        List<String> errorMsgList = new ArrayList<>();

        // 校验错误数据
        if (StringUtils.isBlank(riskWarningExportErrorVO.getDeviceName())) {
            errorMsgList.add("设备名称不能为空");
        }else {
            if(riskWarningExportErrorVO.getDeviceName().length() > 50){
                errorMsgList.add("设备名称长度不能超过50");
            }
        }

        if (StringUtils.isBlank(riskWarningExportErrorVO.getRiskName())) {
            errorMsgList.add("风险名称不能为空");
        }else {
            if(riskWarningExportErrorVO.getRiskName().length() > 50){
                errorMsgList.add("风险名称长度不能超过50");
            }
        }
        if (Objects.isNull(riskWarningExportErrorVO.getRiskStart())) {
            errorMsgList.add("里程范围起始点距离不能为空");
        }else {
            if (Objects.isNull(riskWarningExportErrorVO.getRiskEnd())){
                errorMsgList.add("里程范围终点距离不能为空");
            }
        }
        if (!riskWarningExportErrorVO.getRiskStart().matches(REGEX)) {
            errorMsgList.add("里程范围起始点距离格式错误");
        }else {
            if(!riskWarningExportErrorVO.getRiskEnd().matches(REGEX)){
                errorMsgList.add("里程范围终点距离格式错误");
            }
        }
        if (StringUtils.isNotBlank(riskWarningExportErrorVO.getRemark()) && riskWarningExportErrorVO.getRemark().length() > 200) {
            errorMsgList.add("备注不能超过200个字");
        }
        if (!stringMap.containsKey(riskWarningExportErrorVO.getDeviceName())) {
            errorMsgList.add("设备名称不存在");
        }
        return errorMsgList;
    }

    /**
     * 导出错误数据
     *
     * @param response
     * @param riskWarningExportVOS
     * @throws IOException
     */
    private void exportErrorData(HttpServletResponse response, List<RiskWarningExportErrorVO> riskWarningExportVOS, String fileName) throws IOException {
        ServletOutputStream outputStream = response.getOutputStream();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(outputStream)
                .head(RiskWarningExportErrorVO.class)
                .excelType(ExcelTypeEnum.XLSX)
                .sheet("sheet")
                .doWrite(riskWarningExportVOS);

        outputStream.flush();
        outputStream.close();
    }
}
