package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.ToolPositionManage;
import com.fawkes.project.tbm.common.vo.ToolPositionManageGroupVO;
import com.fawkes.project.tbm.common.vo.ToolPositionManageResult;
import com.fawkes.project.tbm.common.vo.ToolPositionManageVO;

import java.util.List;
import java.util.Map;

public interface ToolPositionManageService extends IService<ToolPositionManage> {

    /**
     * 获取区域+刀具下的位置
     * @param section 区域
     * @param toolNameManageId 刀具id
     */
    List<ToolPositionManageVO> listPositionsByRegionAndTool(String section, Long toolNameManageId);

    /**
     * 根据id获取map
     *
     * @param toolPositionManageId 位置id
     * @return <id,Position>
     */
    Map<Long, String> getToolPositionMap(List<Long> toolPositionManageId);

    /**
     * 获取区域下的所有刀具位置
     * @param section 区域code
     * @return 所有刀具位置
     */
    ToolPositionManageResult listPositionsBySection(String section);

    /**
     * 查询区域下分组
     * @param section 区域code
     * @return 分组集合
     */
    List<ToolPositionManageGroupVO> listGroupVO(String section);
}
