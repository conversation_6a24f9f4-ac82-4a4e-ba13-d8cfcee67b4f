package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmCutDisk;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.common.vo.TbmCutDiskXYVO;

import java.util.List;

/**
 * <p>
 * TBM刀盘系统参数表(此表为原始表，数据按月分表) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
public interface TbmCutDiskService extends IService<TbmCutDisk> {

// 根据设备编码获取最近的刀盘数据
    TbmCutDisk getLastTbmCutDisk(String deviceCode);

// 根据设备编码和环号获取最后十个环号对应的TbmCutDisk对象
    List<TbmCutDisk> getLastTenRingNumTbmCutDisk(String deviceCode,Integer ringNum);

    /**
     * 十个环号对应的TbmCutDisk对象
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     * @return
     */
    List<TbmCutDiskXYVO> getRingNumTbmCutDisk(String deviceCode, List<Integer> ringNumList);

    /**
     * 同步IOT数据
     *
     * @param iotResponseList
     * @return
     */
    Boolean syncIotDeviceData(List<IOTResponse> iotResponseList, String deviceCode);
}
