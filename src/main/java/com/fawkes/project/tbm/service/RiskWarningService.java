package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.model.RiskWarning;
import com.fawkes.project.tbm.common.param.RiskWarningImportResult;
import com.fawkes.project.tbm.common.param.RiskWarningParam;
import com.fawkes.project.tbm.common.query.RiskWarningQuery;
import com.fawkes.project.tbm.common.vo.DeviceManageVO;
import com.fawkes.project.tbm.common.vo.RiskWarningVO;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 风险源预警表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
public interface RiskWarningService extends IService<RiskWarning> {

    PageInfo<RiskWarningVO> list(RiskWarningQuery query);

    void add(RiskWarningParam param);

    void update(RiskWarningParam param);

    boolean delete(String id);

    RiskWarningVO get(String id);

    void export(RiskWarningQuery query, HttpServletResponse response) throws IOException;

    RiskWarningImportResult importExcel(MultipartFile file, HttpServletResponse response) throws IOException;
}
