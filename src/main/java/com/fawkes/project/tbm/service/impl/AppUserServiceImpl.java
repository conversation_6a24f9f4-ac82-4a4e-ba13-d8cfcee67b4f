package com.fawkes.project.tbm.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fawkes.cache.redis.utils.RedisTool;
import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.exception.BusinessException;
import com.fawkes.core.utils.StringTool;
import com.fawkes.project.tbm.common.mapper.UserMirrorImageMapper;
import com.fawkes.project.tbm.common.model.UserMirrorImage;
import com.fawkes.project.tbm.service.IAppUserService;
import com.fawkes.stream.msg.send.sms.SmsMsg;
import com.fawkes.stream.msg.send.sms.SmsTool;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.SignatureException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/8/11 17:38
 * @description
 */
@Service
@Slf4j
public class AppUserServiceImpl implements IAppUserService {

    @Resource
    private RedisTool redisTool;

    @Resource
    private AmqpTemplate amqpTemplate;

    @Autowired
    private UserMirrorImageMapper userMirrorImageMapper;

    @Value("${tbm.loginUrl}")
    private String loginUrl;

    @Value("${tbm.signUrl}")
    private String signUrl;

    @Value("${tbm.smsTemplateId}")
    private String smsTemplateId;

    @Override
    public ApiResponseBody sendMsg(String phone) {
        // 校验手机号码是否存在
        LambdaQueryWrapper<UserMirrorImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserMirrorImage::getPhone, phone);
        wrapper.last("limit 1");
        UserMirrorImage one = userMirrorImageMapper.selectOne(wrapper);
        if (one == null) {
            throw new BusinessException("手机号码不存在,请联系管理员!");
        }

        // 判断账号状态，如果为休眠和注销不允许登录
        if (2 == one.getAccountStatus()) {
            throw new BusinessException("用户已休眠，请联系管理员!");
        }
        if (3 == one.getAccountStatus()) {
            throw new BusinessException("用户已注销，请联系管理员!");
        }

        // 存储到Redis (5分钟过期)
        String key = "tbm_sms_code:" + phone;
        Object storedCode = redisTool.get(key);
        // 验证码还未过期
        if (storedCode != null) {
            long expire = redisTool.getExpire(key);
            throw new BusinessException("请" + expire + "秒后重新发送验证码!");
        }

        // 生成6位随机验证码
        //String code = generateRandomCode(6);
        String code = "123456";
        log.info("生成验证码：{}", code);
        redisTool.set(key, code, 300);

        // 发送短信
        Map<String, String> map = new HashMap<>();
        map.put("p1", code);
        map.put("p2", code);
        SmsMsg smsMsg = new SmsMsg();
        smsMsg.setMobile(phone);
        smsMsg.setParameter(map);
        smsMsg.setTemplate(smsTemplateId);
        smsMsg.setTemplateType("登录短信验证码");

        log.info("开始发送验证码：{}", smsMsg);
        ApiResponseBody apiResponseBody = SmsTool.ceneralSend(ListUtil.of(smsMsg), amqpTemplate);
        log.info("结束发送验证码：{}", apiResponseBody);
        return ApiResponseBody.defaultSuccess(true);
    }

    @Override
    public ApiResponseBody verifyMsg(String phone, String code) {
        String key = "tbm_sms_code:" + phone;
        Object storedCode = redisTool.get(key);
        log.info("手机{}验证码：{}", phone, storedCode);
        // 验证码不存在或已过期
        if (storedCode == null) {
            throw new BusinessException("验证码不存在或已过期!");
        }

        // 验证码匹配（忽略大小写）
        if (storedCode.toString().equalsIgnoreCase(code)) {
            // 验证成功后删除缓存
            redisTool.del(key);

            // 根据手机号码映射用户表手机，获取用户的用户名和密码
            LambdaQueryWrapper<UserMirrorImage> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserMirrorImage::getPhone, phone);
            wrapper.last("limit 1");
            UserMirrorImage one = userMirrorImageMapper.selectOne(wrapper);
            String userName = one.getUserName();
            String password = one.getPassword();
            log.info("用户信息：{}", one);

            // 调用登录接口，获取登录数据返回给前端
            JSONObject jsonObject = loginUser(userName, password, phone);
            if (!Objects.isNull(jsonObject)) {
                jsonObject.put("phone", phone);
            }
            return ApiResponseBody.defaultSuccess(jsonObject);
        }

        return ApiResponseBody.error(500, "登录失败");
    }

    /**
     * 生成随机数字验证码
     *
     * @param length 验证码长度
     * @return 随机验证码
     */
    private String generateRandomCode(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10)); // 0-9的随机数
        }
        return sb.toString();
    }

    private JSONObject loginUser(String username, String password, String phone) {
        JSONObject jsonObject = null;
        try {
            String curTime = getTl();
            String ttl = "300";
            String uid = "fawkes";
            String secretKey = "fawkes_secret";
            HashMap map = new HashMap();
            map.put("ts", ListUtil.of(curTime));
            map.put("ttl", ListUtil.of(ttl));
            map.put("uid", ListUtil.of(uid));
            String s = buildSortParam(map);

            // 创建表单请求体
            FormBody formBody = new FormBody.Builder()
                    .add("username", username)
                    .add("password", password)
                    .add("grant_type", "password")
                    .add("scope", "all")
                    .build();
            log.info("登录请求体参数:{}", formBody);
            String sign = calculateRFC2104HMAC(s, secretKey);
            String url = loginUrl + URLEncoder.encode(sign, "UTF-8") + "&" + s;
            log.info("登录接口url:{}", url);
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            Request request = new Request.Builder()
                    .url(url)
                    .post(formBody)
                    .build();
            Response response = client.newCall(request).execute();
            log.info("登录返回:{}", response);
            ResponseBody responseBody = response.body();
            jsonObject = JSONObject.parseObject(responseBody.string());

            LambdaQueryWrapper<UserMirrorImage> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserMirrorImage::getPhone, phone);
            wrapper.last("limit 1");
            UserMirrorImage userMirrorImage = userMirrorImageMapper.selectOne(wrapper);
            //jsonObject 中增加用户填报权限、子项目权限
            jsonObject.put("functionalIds", userMirrorImage.getFunctionalIds());
            jsonObject.put("subProjectIds", userMirrorImage.getSubProjectIds());
        } catch (Exception e) {
            log.info("用户登录异常：{}", e.getMessage());
        }
        return jsonObject;

    }


    public synchronized String calculateRFC2104HMAC(String data, String secretKey) throws SignatureException {
        String result;
        try {
            SecretKeySpec signingKey = new SecretKeySpec(secretKey.getBytes(),
                    "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(data.getBytes());
            result = new String(java.util.Base64.getEncoder().encode(rawHmac), StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new SignatureException("Failed to generate HMAC : "
                    + e.getMessage());
        }
        return result;
    }

    //获取凤翎平台时间戳
    public String getTl() throws Exception {
        String result = "";
        String url = signUrl;
        log.info("获取时间戳url:{}", url);
        // 创建HttpClientBuilder
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        // HttpClient
        CloseableHttpClient closeableHttpClient = httpClientBuilder.build();
        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
        httpGet.setHeader("Accept", "application/json;charset=utf-8");
        HttpResponse httpResponse;
        httpResponse = closeableHttpClient.execute(httpGet);
        HttpEntity entity = (HttpEntity) httpResponse.getEntity();
        log.info("获取时间戳返回结果: {}", JSONObject.toJSONString(entity));
        if (entity != null) {
            result = EntityUtils.toString(entity, "UTF-8");
        }

        log.info("获取时间戳返回结果,result: {}", result);
        return result;
    }

    /**
     * 对参数进行排序
     *
     * @param singleValueMap
     * @return
     */
    public String buildSortParam(Map<String, Collection<String>> singleValueMap) {
        //拼装url  a=b&c=d
        List<String> paramList = new ArrayList<>(singleValueMap.size());
        singleValueMap.forEach((k, v) -> {
            if (!Objects.equals("sign", k)) {
                String param = null;
                try {
                    //request参数里，中文会被urlencode，所以加密的时候需要先decode
                    param = (k + "=" + URLDecoder.decode(StringTool.join(v, ","), "UTF-8"));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                paramList.add(param);
            }
        });
        //对参数进行排序
        Collections.sort(paramList);
        String pathParam = StringTool.join(paramList, "&");
        return pathParam;
    }
}
