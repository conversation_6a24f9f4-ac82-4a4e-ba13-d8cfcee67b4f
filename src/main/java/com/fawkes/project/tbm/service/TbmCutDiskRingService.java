package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmCutDiskRing;

import java.util.List;

/**
 * <AUTHOR>
 * @version TbmCutDiskRingServiceImpl 2025/6/11 9:32
 */
public interface TbmCutDiskRingService extends IService<TbmCutDiskRing> {

    /**
     * 多条件查询
     * @param wrapper
     * @return
     */
    List<TbmCutDiskRing> queryList(LambdaQueryWrapper<TbmCutDiskRing> wrapper);

    /**
     * 批量新增
     * @param list
     * @return
     */
    Boolean addBatch(List<TbmCutDiskRing> list);

    /**
     * 条件修改
     * @param wrapper
     * @return
     */
    Boolean edit(LambdaUpdateWrapper<TbmCutDiskRing> wrapper);

}
