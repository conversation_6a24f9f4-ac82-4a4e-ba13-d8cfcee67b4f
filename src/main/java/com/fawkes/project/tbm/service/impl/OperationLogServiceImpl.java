package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.project.tbm.common.mapper.OperationLogMapper;
import com.fawkes.project.tbm.common.mapper.ToolNameManageMapper;
import com.fawkes.project.tbm.common.mapper.ToolPositionManageMapper;
import com.fawkes.project.tbm.common.model.*;
import com.fawkes.project.tbm.common.utils.BigDecimalUtils;
import com.fawkes.project.tbm.common.utils.MyEntityTool;
import com.fawkes.project.tbm.service.IOperationLogService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 操作记录表Service实现类
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
@Slf4j
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, TbmOperationLog> implements IOperationLogService {

    @Resource
    private ToolNameManageMapper toolNameManageMapper;

    @Resource
    private ToolPositionManageMapper toolPositionManageMapper;

    @Override
    public void recordToolManagementAddOperation(ToolManagement toolManagement, Integer dataSource) {
        TbmOperationLog tbmOperationLog = new TbmOperationLog();
        tbmOperationLog.setModuleType("TOOL_MANAGEMENT");
        tbmOperationLog.setBizId(toolManagement.getId());
        tbmOperationLog.setOperationType("ADD");
        tbmOperationLog.setOperationContent("新增该条数据");
        tbmOperationLog.setDataSource(dataSource);
        MyEntityTool.insertEntityWithNameSilent(tbmOperationLog);
        this.save(tbmOperationLog);
    }

    @Override
    public void recordToolManagementUpdateOperation(ToolManagement oldToolManagement, ToolManagement newToolManagement, Integer dataSource) {
        TbmOperationLog tbmOperationLog = new TbmOperationLog();
        tbmOperationLog.setModuleType("TOOL_MANAGEMENT");
        tbmOperationLog.setBizId(newToolManagement.getId());
        tbmOperationLog.setOperationType("UPDATE");
        tbmOperationLog.setOperationContent(generateToolManagementUpdateContent(oldToolManagement, newToolManagement));
        tbmOperationLog.setDataSource(dataSource);
        MyEntityTool.insertEntityWithNameSilent(tbmOperationLog);
        this.save(tbmOperationLog);
    }

    @Override
    public void recordToolManagementDeleteOperation(ToolManagement toolManagement, Integer dataSource) {
        TbmOperationLog tbmOperationLog = new TbmOperationLog();
        tbmOperationLog.setModuleType("TOOL_MANAGEMENT");
        tbmOperationLog.setBizId(toolManagement.getId());
        tbmOperationLog.setOperationType("DELETE");
        tbmOperationLog.setOperationContent("删除该条填报内容");
        tbmOperationLog.setDataSource(dataSource);
        MyEntityTool.insertEntityWithNameSilent(tbmOperationLog);
        this.save(tbmOperationLog);
    }

    @Override
    public void recordProcessManagementAddOperation(ProcessManagement processManagement, Integer dataSource) {
        TbmOperationLog tbmOperationLog = new TbmOperationLog();
        tbmOperationLog.setModuleType("PROCESS_MANAGEMENT");
        tbmOperationLog.setBizId(processManagement.getId());
        tbmOperationLog.setOperationType("ADD");
        tbmOperationLog.setOperationContent("新增该条数据");
        tbmOperationLog.setDataSource(dataSource);
        MyEntityTool.insertEntityWithNameSilent(tbmOperationLog);
        this.save(tbmOperationLog);
    }

    @Override
    public void recordProcessManagementUpdateOperation(ProcessManagement oldProcessManagement, ProcessManagement newProcessManagement, Integer dataSource) {
        TbmOperationLog tbmOperationLog = new TbmOperationLog();
        tbmOperationLog.setModuleType("PROCESS_MANAGEMENT");
        tbmOperationLog.setBizId(newProcessManagement.getId());
        tbmOperationLog.setOperationType("UPDATE");
        tbmOperationLog.setOperationContent(generateProcessManagementUpdateContent(oldProcessManagement, newProcessManagement));
        tbmOperationLog.setDataSource(dataSource);
        MyEntityTool.insertEntityWithNameSilent(tbmOperationLog);
        this.save(tbmOperationLog);
    }

    @Override
    public void recordProcessManagementDeleteOperation(ProcessManagement processManagement, Integer dataSource) {
        TbmOperationLog tbmOperationLog = new TbmOperationLog();
        tbmOperationLog.setModuleType("PROCESS_MANAGEMENT");
        tbmOperationLog.setBizId(processManagement.getId());
        tbmOperationLog.setOperationType("DELETE");
        tbmOperationLog.setOperationContent("删除该条填报内容");
        tbmOperationLog.setDataSource(dataSource);
        MyEntityTool.insertEntityWithNameSilent(tbmOperationLog);
        this.save(tbmOperationLog);
    }

    @Override
    public PageInfo<TbmOperationLog> pageOperationLogs(String moduleType, Long recordId, String operationType,
                                                       String operator, String startTime, String endTime,
                                                       Integer pageNo, Integer pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        LambdaQueryWrapper<TbmOperationLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbmOperationLog::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag());

        if (StringUtils.isNotEmpty(moduleType)) {
            queryWrapper.eq(TbmOperationLog::getModuleType, moduleType);
        }

        if (recordId != null) {
            queryWrapper.eq(TbmOperationLog::getBizId, recordId);
        }

        if (StringUtils.isNotEmpty(operationType)) {
            queryWrapper.eq(TbmOperationLog::getOperationType, operationType);
        }

        if (StringUtils.isNotEmpty(operator)) {
            queryWrapper.like(TbmOperationLog::getUpdateName, operator);
        }

        if (StringUtils.isNotEmpty(startTime)) {
            queryWrapper.ge(TbmOperationLog::getUpdateDate, startTime);
        }

        if (StringUtils.isNotEmpty(endTime)) {
            queryWrapper.le(TbmOperationLog::getUpdateDate, endTime);
        }

        queryWrapper.orderByDesc(TbmOperationLog::getUpdateDate);

        List<TbmOperationLog> tbmOperationLogList = this.list(queryWrapper);
        return new PageInfo<>(tbmOperationLogList);
    }

    @Override
    public List<TbmOperationLog> getOperationLogsByRecordId(String moduleType, Long recordId) {
        LambdaQueryWrapper<TbmOperationLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbmOperationLog::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag());
        queryWrapper.eq(TbmOperationLog::getModuleType, moduleType);
        queryWrapper.eq(TbmOperationLog::getBizId, recordId);
        queryWrapper.orderByDesc(TbmOperationLog::getUpdateDate);

        return this.list(queryWrapper);
    }

    /**
     * 生成刀具管理更新内容
     *
     * @param oldToolManagement
     * @param newToolManagement
     * @return
     */
    private String generateToolManagementUpdateContent(ToolManagement oldToolManagement, ToolManagement newToolManagement) {
        StringBuilder content = new StringBuilder();

        // 比较各个字段的变化
        if (!StringUtils.equals(oldToolManagement.getSection(), newToolManagement.getSection())) {
            content.append("区段由").append(oldToolManagement.getSection()).append("修改为").append(newToolManagement.getSection()).append(";");
        }

        if (!oldToolManagement.getRingNum().equals(newToolManagement.getRingNum())) {
            content.append("环号由").append(oldToolManagement.getRingNum()).append("修改为").append(newToolManagement.getRingNum()).append(";");
        }

        if (!oldToolManagement.getToolNameManageId().equals(newToolManagement.getToolNameManageId())) {
            ToolNameManage oldToolNameManage = toolNameManageMapper.selectById(oldToolManagement.getToolNameManageId());
            ToolNameManage newToolNameManage = toolNameManageMapper.selectById(newToolManagement.getToolNameManageId());
            content.append("刀具名称由").append(oldToolNameManage.getTooName()).append("修改为").append(newToolNameManage.getTooName()).append(";");
        }

        if (!oldToolManagement.getToolPositionManageId().equals(newToolManagement.getToolPositionManageId())) {
            ToolPositionManage oldToolPositionManage = toolPositionManageMapper.selectById(oldToolManagement.getToolPositionManageId());
            ToolPositionManage newToolPositionManage = toolPositionManageMapper.selectById(newToolManagement.getToolPositionManageId());
            content.append("刀具位置由").append(oldToolPositionManage.getToolPosition()).append("修改为").append(newToolPositionManage.getToolPosition()).append(";");
        }

        if (!BigDecimalUtils.equals(oldToolManagement.getWearValue(), newToolManagement.getWearValue())) {
            content.append("磨损值由").append(oldToolManagement.getWearValue().stripTrailingZeros().toPlainString()).append("修改为").append(newToolManagement.getWearValue().stripTrailingZeros().toPlainString()).append(";");
        }

        if (!StringUtils.equals(oldToolManagement.getStartOrientation(), newToolManagement.getStartOrientation())) {
            content.append("起始方位由").append(oldToolManagement.getStartOrientation()).append("修改为").append(newToolManagement.getStartOrientation()).append(";");
        }

        if (!BigDecimalUtils.equals(oldToolManagement.getStartMileage(), newToolManagement.getStartMileage())) {
            content.append("起始里程由").append(oldToolManagement.getStartMileage().stripTrailingZeros().toPlainString()).append("修改为").append(newToolManagement.getStartMileage().stripTrailingZeros().toPlainString()).append(";");
        }

        if (!StringUtils.equals(oldToolManagement.getEndOrientation(), newToolManagement.getEndOrientation())) {
            content.append("结束方位由").append(oldToolManagement.getEndOrientation()).append("修改为").append(newToolManagement.getEndOrientation()).append(";");
        }

        if (!BigDecimalUtils.equals(oldToolManagement.getEndMileage(), newToolManagement.getEndMileage())) {
            content.append("结束里程由").append(oldToolManagement.getEndMileage().stripTrailingZeros().toPlainString()).append("修改为").append(newToolManagement.getEndMileage().stripTrailingZeros().toPlainString()).append(";");
        }

        if (!oldToolManagement.getOpenWarehouseStartTime().equals(newToolManagement.getOpenWarehouseStartTime())) {
            content.append("开仓开始时间由").append(oldToolManagement.getOpenWarehouseStartTime()).append("修改为").append(newToolManagement.getOpenWarehouseStartTime()).append(";");
        }

        if (!oldToolManagement.getOpenWarehouseEndTime().equals(newToolManagement.getOpenWarehouseEndTime())) {
            content.append("开仓结束时间由").append(oldToolManagement.getOpenWarehouseEndTime()).append("修改为").append(newToolManagement.getOpenWarehouseEndTime()).append(";");
        }

        if (!StringUtils.equals(oldToolManagement.getNewlyInstalledCuttingToolsStatus(), newToolManagement.getNewlyInstalledCuttingToolsStatus())) {
            content.append("新装刀具状态由").append(oldToolManagement.getNewlyInstalledCuttingToolsStatus()).append("修改为").append(newToolManagement.getNewlyInstalledCuttingToolsStatus()).append(";");
        }

        if (!BigDecimalUtils.equals(oldToolManagement.getChangeThreshold(), newToolManagement.getChangeThreshold())) {
            content.append("更换阈值由").append(oldToolManagement.getChangeThreshold().stripTrailingZeros().toPlainString()).append("修改为").append(newToolManagement.getChangeThreshold().stripTrailingZeros().toPlainString()).append(";");
        }

        if (!StringUtils.equals(oldToolManagement.getChangeToolReason(), newToolManagement.getChangeToolReason())) {
            content.append("换刀原因由").append(oldToolManagement.getChangeToolReason()).append("修改为").append(newToolManagement.getChangeToolReason()).append(";");
        }

        if (!Objects.equals(oldToolManagement.isHasToolChanged(), newToolManagement.isHasToolChanged())) {
            content.append("是否换刀由").append(oldToolManagement.isHasToolChanged()).append("修改为").append(newToolManagement.isHasToolChanged()).append(";");
        }

        if (!StringUtils.equals(oldToolManagement.getAttachment(), newToolManagement.getAttachment())) {
            content.append("附件由").append(oldToolManagement.getAttachment()).append("修改为").append(newToolManagement.getAttachment()).append(";");
        }

        if (!StringUtils.equals(oldToolManagement.getRemark(), newToolManagement.getRemark())) {
            content.append("备注由").append(oldToolManagement.getRemark()).append("修改为").append(newToolManagement.getRemark()).append(";");
        }

        if (!StringUtils.equals(oldToolManagement.getChangeToolPicture(), newToolManagement.getChangeToolPicture())) {
            content.append("换刀图片由").append(oldToolManagement.getChangeToolPicture()).append("修改为").append(newToolManagement.getChangeToolPicture()).append(";");
        }

        return content.length() > 0 ? content.toString() : "无字段变化";
    }

    /**
     * 生成工序管理更新内容
     *
     * @param oldProcessManagement
     * @param newProcessManagement
     * @return
     */
    private String generateProcessManagementUpdateContent(ProcessManagement oldProcessManagement, ProcessManagement newProcessManagement) {
        StringBuilder content = new StringBuilder();

        // 比较各个字段的变化
        if (!StringUtils.equals(oldProcessManagement.getSection(), newProcessManagement.getSection())) {
            content.append("区段由").append(oldProcessManagement.getSection()).append("修改为").append(newProcessManagement.getSection()).append(";");
        }

        if (!oldProcessManagement.getRingNum().equals(newProcessManagement.getRingNum())) {
            content.append("环号由").append(oldProcessManagement.getRingNum()).append("修改为").append(newProcessManagement.getRingNum()).append(";");
        }

        if (!BigDecimalUtils.equals(oldProcessManagement.getAssemblyPoint(), newProcessManagement.getAssemblyPoint())) {
            content.append("拼装点位由").append(oldProcessManagement.getAssemblyPoint().stripTrailingZeros().toPlainString()).append("修改为").append(newProcessManagement.getAssemblyPoint().stripTrailingZeros().toPlainString()).append(";");
        }

        if (!Objects.equals(oldProcessManagement.getTunnelingStartTime(), newProcessManagement.getTunnelingStartTime())) {
            content.append("掘进开始时间由").append(oldProcessManagement.getTunnelingStartTime()).append("修改为").append(newProcessManagement.getTunnelingStartTime()).append(";");
        }

        if (!Objects.equals(oldProcessManagement.getTunnelingEndTime(), newProcessManagement.getTunnelingEndTime())) {
            content.append("掘进结束时间由").append(oldProcessManagement.getTunnelingEndTime()).append("修改为").append(newProcessManagement.getTunnelingEndTime()).append(";");
        }

        if (!Objects.equals(oldProcessManagement.getChangeStepsStartTime(), newProcessManagement.getChangeStepsStartTime())) {
            content.append("换步开始时间由").append(oldProcessManagement.getChangeStepsStartTime()).append("修改为").append(newProcessManagement.getChangeStepsStartTime()).append(";");
        }

        if (!Objects.equals(oldProcessManagement.getChangeStepsEndTime(), newProcessManagement.getChangeStepsEndTime())) {
            content.append("换步结束时间由").append(oldProcessManagement.getChangeStepsEndTime()).append("修改为").append(newProcessManagement.getChangeStepsEndTime()).append(";");
        }

        if (!Objects.equals(oldProcessManagement.getSegmentAssemblyStartTime(), newProcessManagement.getSegmentAssemblyStartTime())) {
            content.append("管片拼装开始时间由").append(oldProcessManagement.getSegmentAssemblyStartTime()).append("修改为").append(newProcessManagement.getSegmentAssemblyStartTime()).append(";");
        }

        if (!Objects.equals(oldProcessManagement.getSegmentAssemblyEndTime(), newProcessManagement.getSegmentAssemblyEndTime())) {
            content.append("管片拼装结束时间由").append(oldProcessManagement.getSegmentAssemblyEndTime()).append("修改为").append(newProcessManagement.getSegmentAssemblyEndTime()).append(";");
        }

        if (!Objects.equals(oldProcessManagement.getOthersJobStartTime(), newProcessManagement.getOthersJobStartTime())) {
            content.append("其他工作开始时间由").append(oldProcessManagement.getOthersJobStartTime()).append("修改为").append(newProcessManagement.getOthersJobStartTime()).append(";");
        }

        if (!Objects.equals(oldProcessManagement.getOthersJobEndTime(), newProcessManagement.getOthersJobEndTime())) {
            content.append("其他工作结束时间由").append(oldProcessManagement.getOthersJobEndTime()).append("修改为").append(newProcessManagement.getOthersJobEndTime()).append(";");
        }

        if (!Objects.equals(oldProcessManagement.getCommonMaintenanceAndRepairStartTime(), newProcessManagement.getCommonMaintenanceAndRepairStartTime())) {
            content.append("常规保养开始时间由").append(oldProcessManagement.getCommonMaintenanceAndRepairStartTime()).append("修改为").append(newProcessManagement.getCommonMaintenanceAndRepairStartTime()).append(";");
        }

        if (!Objects.equals(oldProcessManagement.getCommonMaintenanceAndRepairEndTime(), newProcessManagement.getCommonMaintenanceAndRepairEndTime())) {
            content.append("常规保养结束时间由").append(oldProcessManagement.getCommonMaintenanceAndRepairEndTime()).append("修改为").append(newProcessManagement.getCommonMaintenanceAndRepairEndTime()).append(";");
        }

        if (!Objects.equals(oldProcessManagement.getShutdownMaintenanceAndRepairStartTime(), newProcessManagement.getShutdownMaintenanceAndRepairStartTime())) {
            content.append("故障停机开始时间由").append(oldProcessManagement.getShutdownMaintenanceAndRepairStartTime()).append("修改为").append(newProcessManagement.getShutdownMaintenanceAndRepairStartTime()).append(";");
        }

        if (!Objects.equals(oldProcessManagement.getShutdownMaintenanceAndRepairEndTime(), newProcessManagement.getShutdownMaintenanceAndRepairEndTime())) {
            content.append("故障停机结束时间由").append(oldProcessManagement.getShutdownMaintenanceAndRepairEndTime()).append("修改为").append(newProcessManagement.getShutdownMaintenanceAndRepairEndTime()).append(";");
        }

        if (!Objects.equals(oldProcessManagement.getOpenWarehouseChangeToolStartTime(), newProcessManagement.getOpenWarehouseChangeToolStartTime())) {
            content.append("开仓换刀开始时间由").append(oldProcessManagement.getOpenWarehouseChangeToolStartTime()).append("修改为").append(newProcessManagement.getOpenWarehouseChangeToolStartTime()).append(";");
        }

        if (!Objects.equals(oldProcessManagement.getOpenWarehouseChangeToolEndTime(), newProcessManagement.getOpenWarehouseChangeToolEndTime())) {
            content.append("开仓换刀结束时间由").append(oldProcessManagement.getOpenWarehouseChangeToolEndTime()).append("修改为").append(newProcessManagement.getOpenWarehouseChangeToolEndTime()).append(";");
        }

        if (!Objects.equals(oldProcessManagement.getOpenWarehouseChangeToolNum(), newProcessManagement.getOpenWarehouseChangeToolNum())) {
            content.append("开仓换刀仓数由").append(oldProcessManagement.getOpenWarehouseChangeToolNum()).append("修改为").append(newProcessManagement.getOpenWarehouseChangeToolNum()).append(";");
        }

        if (!StringUtils.equals(oldProcessManagement.getDescriptionOfAbnormalSituations(), newProcessManagement.getDescriptionOfAbnormalSituations())) {
            content.append("异常情况说明由").append(oldProcessManagement.getDescriptionOfAbnormalSituations()).append("修改为").append(newProcessManagement.getDescriptionOfAbnormalSituations()).append(";");
        }

        if (!StringUtils.equals(oldProcessManagement.getAttachment(), newProcessManagement.getAttachment())) {
            content.append("附件由").append(oldProcessManagement.getAttachment()).append("修改为").append(newProcessManagement.getAttachment()).append(";");
        }

        if (!StringUtils.equals(oldProcessManagement.getRemark(), newProcessManagement.getRemark())) {
            content.append("备注由").append(oldProcessManagement.getRemark()).append("修改为").append(newProcessManagement.getRemark()).append(";");
        }

        if (!StringUtils.equals(oldProcessManagement.getProcessName(), newProcessManagement.getProcessName())) {
            content.append("工序名称由").append(oldProcessManagement.getProcessName()).append("修改为").append(newProcessManagement.getProcessName()).append(";");
        }

        if (!StringUtils.equals(oldProcessManagement.getDescriptionOfConstructionSituation(), newProcessManagement.getDescriptionOfConstructionSituation())) {
            content.append("施工情况描述由").append(oldProcessManagement.getDescriptionOfConstructionSituation()).append("修改为").append(newProcessManagement.getDescriptionOfConstructionSituation()).append(";");
        }

        return content.length() > 0 ? content.toString() : "无字段变化";
    }
} 