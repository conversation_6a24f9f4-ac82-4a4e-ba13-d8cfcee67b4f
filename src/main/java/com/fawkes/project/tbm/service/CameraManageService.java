package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.CameraManage;
import com.fawkes.project.tbm.common.param.CameraManageParam;
import com.fawkes.project.tbm.common.param.DeviceManageParam;
import com.fawkes.project.tbm.common.vo.CameraManageTreeVO;
import com.fawkes.project.tbm.common.vo.CameraManageVO;
import com.fawkes.project.tbm.common.vo.DeviceManageVO;

import java.util.List;

/**
 * <p>
 * 摄像头配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface CameraManageService extends IService<CameraManage> {
     List<CameraManageTreeVO> list(String name);

    void add(CameraManageParam param);

    void update(CameraManageParam param);

    boolean delete(String id);

    CameraManageVO get(String id);

    List<CameraManageVO> listProcess(String name, String section);
}
