package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.ProcessManagement;
import com.fawkes.project.tbm.common.param.ProcessManagementImportResult;
import com.fawkes.project.tbm.common.param.ProcessManagementParam;
import com.fawkes.project.tbm.common.query.ProcessManagementExportQuery;
import com.fawkes.project.tbm.common.vo.*;
import com.fawkes.project.tbm.common.vo.app.ProcessManagementAppVO;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/6/3 18:00
 */
public interface IProcessManagementService extends IService<ProcessManagement> {

    /**
     * 分页查询
     *
     * @param processName
     * @param sectionList
     * @param minRingNum
     * @param createName
     * @param createStartDate
     * @param createEndDate
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageInfo page(String processName, List<String> sectionList, String minRingNum, String maxRingNum, String createName, String createStartDate, String createEndDate, Integer pageNo, Integer pageSize);

    /**
     * 新增
     *
     * @param processManagementParam
     * @return
     */
    Boolean add(ProcessManagementParam processManagementParam) throws IllegalAccessException;

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ProcessManagementVO get(String id);

    /**
     * ocr识别
     *
     * @param token
     * @return
     */
    ProcessManagementVO ocrByToken(String token);

    /**
     * ocr识别
     *
     * @param file
     * @return
     */
    OcrProcessManagementVO ocrByFile(MultipartFile file);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delete(String id, Integer dataSource) throws IllegalAccessException;

    /**
     * 导入
     *
     * @param file
     * @param response
     * @return
     * @throws IOException
     */
    ProcessManagementImportResult importExcel(MultipartFile file, HttpServletResponse response) throws IOException, IllegalAccessException;

    /**
     * 导出
     *
     * @param query
     * @param response
     * @throws IOException
     */
    void export(ProcessManagementExportQuery query, HttpServletResponse response) throws IOException;

    /**
     * 按照区段获取该区段下所有的工序环数，需要去重，按照环号大小升序排序
     *
     * @param section
     * @return
     */
    List<ProcessManagementRingNumVO> listProcessManagementRingNum(String section);

    /**
     * 按照区段获取该区段下所有的工序环数，需要去重，按照环号大小升序排序
     *
     * @param section
     * @param ringNum
     * @return
     */
    ProcessTotalDistributionVO statisticsProcessDistribution(String section, String ringNum);

    /**
     * 统计工序占比
     *
     * @param section
     * @param ringNum
     * @return
     */
    List<ProcessRatioVO> statisticsProcessRatio(String section, String ringNum);

    /**
     * 获取施工情况描述
     *
     * @param section
     * @return
     */
    ProcessConstructionSituationDescriptionVO getConstructionSituationDescription(String section);

    /**
     * 列表获取所有环号的异常情况说明
     *
     * @param section
     * @return
     */
    List<RingAbnormalSituationsDescriptionVO> listAllRingAbnormalSituationsDescription(String section);

    /**
     * 统计设备利用率
     *
     * @param section
     * @return
     */
    List<EquipmentUseRateVO> statisticsEquipmentUseRate(String section);

    /**
     * APP 分页查询
     *
     * @param section
     * @param searchValue
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageInfo<ProcessManagementAppVO> pageApp(String section, String searchValue, Integer pageNo, Integer pageSize);
}