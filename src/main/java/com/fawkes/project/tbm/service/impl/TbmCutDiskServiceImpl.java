package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.constants.IOTDeviceConstants;
import com.fawkes.project.tbm.common.handler.MonthTableNameHandler;
import com.fawkes.project.tbm.common.mapper.TbmCutDiskMapper;
import com.fawkes.project.tbm.common.model.TbmCutDisk;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.common.utils.MyEntityTool;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.common.vo.TbmCutDiskXYVO;
import com.fawkes.project.tbm.service.TbmCutDiskService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * TBM刀盘系统参数表(此表为原始表，数据按月分表) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Service
public class TbmCutDiskServiceImpl extends ServiceImpl<TbmCutDiskMapper, TbmCutDisk> implements TbmCutDiskService {

    @Resource
    private TbmCutDiskMapper tbmCutDiskMapper;

    @Override
    public TbmCutDisk getLastTbmCutDisk(String deviceCode) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        List<TbmCutDisk> cameraManages = this.lambdaQuery().eq(TbmCutDisk::getCode, deviceCode)
                .orderByDesc(TbmCutDisk::getCreateTime).last("LIMIT  1").list();
        MonthTableNameHandler.removeMonthData();
        if (CollectionUtils.isNotEmpty(cameraManages)) {
            return cameraManages.get(0);
        }
        return null;
    }

    @Override
    public List<TbmCutDisk> getLastTenRingNumTbmCutDisk(String deviceCode, Integer ringNum) {
        Integer minRingNum = 0;
        if (ringNum - 9 > 0) {
            minRingNum = ringNum - 9;
        }
        List<TbmCutDisk> cameraManages = this.lambdaQuery().eq(TbmCutDisk::getCode, deviceCode)
                .between(TbmCutDisk::getRingNum, minRingNum, ringNum).list();
        return cameraManages;
    }

    /**
     * 十个环号对应的TbmCutDisk对象
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     */
    @Override
    public List<TbmCutDiskXYVO> getRingNumTbmCutDisk(String deviceCode, List<Integer> ringNumList) {
        String tableName = "tbm_cut_disk_" + DateUtils.getYyyyMM();
        return tbmCutDiskMapper.getRingNumTbmCutDisk(deviceCode,ringNumList,tableName);
    }

    @Override
    public Boolean syncIotDeviceData(List<IOTResponse> iotResponseList, String deviceCode) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        String cutDiskTorchque = null;
        String actualRpm = null;
        String penetration = null;
        String ringNum = null;
        Optional<String> optionalS = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.CUT_DISK_TORCHQUE)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalS.isPresent()) {
            cutDiskTorchque = optionalS.get();
        }

        Optional<String> optionalA = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.ACTUAL_RPM)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalA.isPresent()) {
            actualRpm = optionalA.get();
        }

        Optional<String> optionalP = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.PENETRATION)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalP.isPresent()) {
            penetration = optionalP.get();
        }

        Optional<String> optionalR = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.RING_NUM)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalR.isPresent()) {
            ringNum = optionalR.get();
        }

        TbmCutDisk tbmCutDisk = new TbmCutDisk();
        tbmCutDisk.setCode(deviceCode);
        if (cutDiskTorchque != null) {
            tbmCutDisk.setCutDiskTorchque(new BigDecimal(cutDiskTorchque));
        }
        if (actualRpm != null) {
            tbmCutDisk.setActualRpm(new BigDecimal(actualRpm));
        }
        if (penetration != null) {
            tbmCutDisk.setPenetration(new BigDecimal(penetration));
        }
        if (ringNum != null) {
            tbmCutDisk.setRingNum(Integer.valueOf(ringNum));
        }

        tbmCutDisk.setCreateTime(new Timestamp(System.currentTimeMillis()));
        tbmCutDisk.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        int insert = tbmCutDiskMapper.insert(tbmCutDisk);
        MonthTableNameHandler.removeMonthData();
        return insert > 0;
    }
}