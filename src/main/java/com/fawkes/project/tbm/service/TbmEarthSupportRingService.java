package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmDriveSystemRing;
import com.fawkes.project.tbm.common.model.TbmEarthSupportRing;

import java.util.List;

/**
 * <AUTHOR>
 * @version TbmEarthSupportRingService 2025/6/11 9:34
 */
public interface TbmEarthSupportRingService extends IService<TbmEarthSupportRing> {
    /**
     * 多条件查询
     * @param wrapper
     * @return
     */
    List<TbmEarthSupportRing> queryList(LambdaQueryWrapper<TbmEarthSupportRing> wrapper);

    /**
     * 批量新增
     * @param list
     * @return
     */
    Boolean addBatch(List<TbmEarthSupportRing> list);

    /**
     * 条件修改
     * @param wrapper
     * @return
     */
    Boolean edit(LambdaUpdateWrapper<TbmEarthSupportRing> wrapper);

}
