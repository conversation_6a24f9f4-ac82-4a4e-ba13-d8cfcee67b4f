package com.fawkes.project.tbm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.core.exception.BusinessException;
import com.fawkes.core.utils.crypto.sm.Sm4Tool;
import com.fawkes.core.utils.id.IdTool;
import com.fawkes.project.tbm.client.ISystemClient;
import com.fawkes.project.tbm.client.IUserClient;
import com.fawkes.project.tbm.common.mapper.TbmFunctionalManagementMapper;
import com.fawkes.project.tbm.common.mapper.UserMirrorImageMapper;
import com.fawkes.project.tbm.common.model.MiddleUserPortal;
import com.fawkes.project.tbm.common.model.SysUser;
import com.fawkes.project.tbm.common.model.TbmFunctionalManagement;
import com.fawkes.project.tbm.common.model.UserMirrorImage;
import com.fawkes.project.tbm.common.param.CreateUserParam;
import com.fawkes.project.tbm.common.param.RoleMiddleUserParam;
import com.fawkes.project.tbm.common.param.UpdateUserParam;
import com.fawkes.project.tbm.common.param.UserMirrorImageParam;
import com.fawkes.project.tbm.common.utils.MyEntityTool;
import com.fawkes.project.tbm.common.utils.TbmFunctionalManagementTreeBuild;
import com.fawkes.project.tbm.common.vo.TbmFunctionalManagementVO;
import com.fawkes.project.tbm.common.vo.UserMirrorImageVO;
import com.fawkes.project.tbm.common.vo.UserRolesVO;
import com.fawkes.project.tbm.service.IUserMirrorImageService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.crypto.engines.SM4Engine;
import org.bouncycastle.crypto.paddings.PaddedBufferedBlockCipher;
import org.bouncycastle.crypto.paddings.ZeroBytePadding;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.util.encoders.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserMirrorImageServiceImpl extends ServiceImpl<UserMirrorImageMapper, UserMirrorImage> implements IUserMirrorImageService {

    @Resource
    private UserMirrorImageMapper userMirrorImageMapper;

    @Resource
    private IUserClient iUserClient;

    @Resource
    private ISystemClient iSystemClient;

    @Resource
    private TbmFunctionalManagementMapper tbmFunctionalManagementMapper;

    @Value("${tbm.SM4KEY}")
    private String SM4KEY;

    String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%*-_+=?";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody userRegister(UserMirrorImageParam userMirrorImageParam) {
        log.info("开始用户注册: {}", JSONObject.toJSONString(userMirrorImageParam));
        //  用户注册：
        //	1、将用户信息添加到维护表，校验用户名与手机号码与邮箱和系统用户表是否唯一
        verifySole(userMirrorImageParam);

        //	生成随机11位手机号码
        String phoneMirror = generateUniquePhoneNumber();
        String password = RandomUtil.randomString(characters, 12);
        log.info("真实手机号码: {}，随机手机号码: {}，随机密码: {}", userMirrorImageParam.getPhone(), phoneMirror, password);
        String encrypt = Sm4Tool.encrypt(SM4KEY, password);
        CreateUserParam param = new CreateUserParam();
        BeanUtil.copyProperties(userMirrorImageParam, param);
        param.setPassword(encrypt);
        param.setPhone(phoneMirror);
        ApiResponseBody<CreateUserParam> user = iUserClient.createUser(param);
        if (!user.getStatus()) {
            log.error("新增用户失败:{}", user.getMessage());
            throw new BusinessException("新增用户失败！");
        } else {
            // 添加角色和门户
            String userId = user.getData().getId().toString();
            if (StrUtil.isNotBlank(userMirrorImageParam.getRoleIds())) {
                settingUserRole(userId, userMirrorImageParam.getRoleIds());
            }
            if (StrUtil.isNotBlank(userMirrorImageParam.getPortalIds())) {
                settingUserPortal(userId, userMirrorImageParam.getPortalIds());
            }
        }

        // 生成前端加密方式生成的密文
        String frontEncrypt = encryptSM4(password);

        UserMirrorImage userMirrorImage = BeanUtil.copyProperties(userMirrorImageParam, UserMirrorImage.class);
        //	将随机生成的密码通过前端加密方式生成的密文，存到维护表，将真实手机号码和映射手机号码维护到维护表
        userMirrorImage.setPhoneMirror(phoneMirror);
        userMirrorImage.setPassword(frontEncrypt);
        try {
            MyEntityTool.insertEntity(userMirrorImage);
        } catch (IllegalAccessException e) {
            throw new BusinessException("新增用户失败1！");
        }
        userMirrorImageMapper.insert(userMirrorImage);
        return ApiResponseBody.defaultSuccess();
    }

    @Override
    public ApiResponseBody functionalTree() {
        LambdaQueryWrapper<TbmFunctionalManagement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbmFunctionalManagement::getDeleteFlag, 0);
        queryWrapper.eq(TbmFunctionalManagement::getEnable, 1);
        queryWrapper.eq(TbmFunctionalManagement::getHidden, 0);
        queryWrapper.orderByAsc(TbmFunctionalManagement::getSort);
        List<TbmFunctionalManagement> tbmFunctionalManagementList = tbmFunctionalManagementMapper.selectList(queryWrapper);

        List<TbmFunctionalManagementVO> tbmFunctionalManagementVOList = BeanUtil.copyToList(tbmFunctionalManagementList, TbmFunctionalManagementVO.class);
        TbmFunctionalManagementTreeBuild treeBuild = new TbmFunctionalManagementTreeBuild(tbmFunctionalManagementVOList);
        tbmFunctionalManagementVOList = treeBuild.buildTree();
        return ApiResponseBody.defaultSuccess(tbmFunctionalManagementVOList);
    }

    @Override
    public ApiResponseBody details(String id) {
        UserMirrorImage userMirrorImage = userMirrorImageMapper.selectById(id);
        UserMirrorImageVO userMirrorImageVO = BeanUtil.copyProperties(userMirrorImage, UserMirrorImageVO.class);
        String userRoleIdList = getUserRoleIdList(userMirrorImage.getUserName());
        userMirrorImageVO.setRoleIds(userRoleIdList);
        String userPortalIdList = getUserPortalIdList(userMirrorImage.getUserName());
        userMirrorImageVO.setPortalIds(userPortalIdList);
        return ApiResponseBody.defaultSuccess(userMirrorImageVO);
    }

    @Override
    public ApiResponseBody pageList(Integer page, Integer size, String name) {
        PageHelper.startPage(page, size);
        LambdaQueryWrapper<UserMirrorImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserMirrorImage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag());

        if (StringUtils.isNotBlank(name)) {
            queryWrapper.like(UserMirrorImage::getUserName, name).or().like(UserMirrorImage::getUserFullname, name);
        }

        queryWrapper.orderByDesc(UserMirrorImage::getUpdateDate);
        List<UserMirrorImage> userMirrorImageList = this.list(queryWrapper);

        if (CollectionUtils.isEmpty(userMirrorImageList)) {
            return ApiResponseBody.defaultSuccess(new PageInfo<>());
        }

        PageInfo<UserMirrorImage> info = new PageInfo<>(userMirrorImageList);
        List<UserMirrorImage> userMirrorImages = info.getList();
        List<UserMirrorImageVO> mirrorImageVOList = new ArrayList<>();

        for (UserMirrorImage userMirrorImage : userMirrorImages) {
            UserMirrorImageVO userMirrorImageVO = BeanUtil.toBean(userMirrorImage, UserMirrorImageVO.class);
            String userRoleIdList = getUserRoleIdList(userMirrorImage.getUserName());
            userMirrorImageVO.setRoleIds(userRoleIdList);
            String userPortalIdList = getUserPortalIdList(userMirrorImage.getUserName());
            userMirrorImageVO.setPortalIds(userPortalIdList);
            mirrorImageVOList.add(userMirrorImageVO);
        }

        PageInfo<UserMirrorImageVO> pageInfo = BeanUtil.toBean(info, PageInfo.class);
        pageInfo.setList(mirrorImageVOList);
        return ApiResponseBody.defaultSuccess(pageInfo);
    }

    /**
     * 获取用户门户id列表
     *
     * @param userName
     * @return
     */
    private String getUserPortalIdList(String userName) {
        ApiResponseBody<SysUser> getUserResponseBody = iUserClient.getUser("fawkes", userName);
        SysUser sysUser = getUserResponseBody.getData();
        List<Long> userPortalIdList = userMirrorImageMapper.selectPortalIdsByUserId(sysUser.getId());
        if (CollectionUtils.isEmpty(userPortalIdList)) {
            return "";
        }
        return userPortalIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
    }

    /**
     * 获取用户角色id列表
     *
     * @param userName
     * @return
     */
    private String getUserRoleIdList(String userName) {
        ApiResponseBody<SysUser> getUserResponseBody = iUserClient.getUser("fawkes", userName);
        SysUser sysUser = getUserResponseBody.getData();
        ApiResponseBody<ArrayList<UserRolesVO>> listUserRolesResponseBody = iUserClient.listUserRoles(String.valueOf(sysUser.getId()), null);
        ArrayList<UserRolesVO> userRolesVOList = listUserRolesResponseBody.getData();
        if (CollectionUtils.isEmpty(userRolesVOList)) {
            return "";
        }
        return userRolesVOList.stream().map(UserRolesVO::getId).collect(Collectors.joining(","));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody userUpdate(UserMirrorImageParam userMirrorImageParam) {
        UserMirrorImage oldUserMirrorImage = userMirrorImageMapper.selectById(userMirrorImageParam.getId());
        // 获取用户表对应用户的id
        SysUser sysUser = userMirrorImageMapper.selectSysUserId(userMirrorImageParam.getUserName(), oldUserMirrorImage.getPhoneMirror());
        // 校验手机
        if (!userMirrorImageParam.getPhone().equals(oldUserMirrorImage.getPhone())) {
            verifyUserPhone(userMirrorImageParam);
        }
        // 邮箱邮箱
        if (!userMirrorImageParam.getEmail().equals(oldUserMirrorImage.getEmail())) {
            verifyUserEmail(userMirrorImageParam);
        }

        UserMirrorImage userMirrorImage = BeanUtil.copyProperties(userMirrorImageParam, UserMirrorImage.class);
        userMirrorImageMapper.updateById(userMirrorImage);

        UpdateUserParam updateUserParam = new UpdateUserParam();
        updateUserParam.setId(sysUser.getId());
        updateUserParam.setUserName(sysUser.getUserName());
        updateUserParam.setEmail(userMirrorImage.getEmail());
        updateUserParam.setUserFullname(userMirrorImage.getUserFullname());
        updateUserParam.setAccountStatus(userMirrorImage.getAccountStatus());
        updateUserParam.setAccountPeriod(Integer.valueOf(userMirrorImage.getAccountPeriod()));
        ApiResponseBody apiResponseBody = iUserClient.updateUser(updateUserParam);
        log.info("更新凤翎用户结果：{}", JSONObject.toJSONString(apiResponseBody));

        List<Long> portalIdList = userMirrorImageMapper.selectPortalIdsByUserId(sysUser.getId());
        String oldPortalIds = StrUtil.join(",", portalIdList);
        List<Long> roleIdList = userMirrorImageMapper.selectRoleIdsByUserId(sysUser.getId());
        String oldRoleIds = StrUtil.join(",", roleIdList);

        // 门户和角色不为和之前数据不一致的话，先删除对应的用户角色对应关系，再重新添加
        if (!userMirrorImageParam.getPortalIds().equals(oldPortalIds)) {
            deleteUserPortal(sysUser.getId());
            settingUserPortal(String.valueOf(sysUser.getId()), userMirrorImageParam.getPortalIds());
        }

        if (!userMirrorImageParam.getRoleIds().equals(oldRoleIds)) {
            deleteUserRole(sysUser.getId());
            settingUserRole(String.valueOf(sysUser.getId()), userMirrorImageParam.getRoleIds());
        }

        return ApiResponseBody.defaultSuccess(true);
    }

    /**
     * 设置用户角色
     *
     * @param userId
     * @param roleIds
     */
    private void settingUserRole(String userId, String roleIds) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(roleIds)) {
            return;
        }

        List<String> roleIdList = StrUtil.split(roleIds, ",");
        for (String roleId : roleIdList) {
            RoleMiddleUserParam roleMiddleUserParam = new RoleMiddleUserParam();
            roleMiddleUserParam.setRoleId(roleId);
            roleMiddleUserParam.setUserIdList(Lists.newArrayList(userId));
            ApiResponseBody<Object> savedRoleUsers = iSystemClient.createRoleMiddleUser(roleMiddleUserParam);
            if (!savedRoleUsers.getStatus()) {
                log.error("设置用户角色失败:{}", savedRoleUsers.getMessage());
                throw new BusinessException("设置用户角色失败！");
            }
        }
    }

    /**
     * 删除用户门户
     *
     * @param userId
     */
    private void deleteUserPortal(Long userId) {
        if (userId == null) {
            return;
        }

        userMirrorImageMapper.deleteUserPortalByUserId(userId, 100000L);
    }

    /**
     * 删除用户角色
     *
     * @param userId
     */
    private void deleteUserRole(Long userId) {
        if (userId == null) {
            return;
        }

        userMirrorImageMapper.deleteUserRoleByUserId(userId, 100000L);
    }

    /**
     * 设置用户门户
     *
     * @param userId
     * @param portalIds
     */
    private void settingUserPortal(String userId, String portalIds) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(portalIds)) {
            return;
        }

        List<String> portalIdList = StrUtil.split(portalIds, ",");
        for (String portalId : portalIdList) {
            MiddleUserPortal middleUserPortal = new MiddleUserPortal();
            middleUserPortal.setPortalId(Long.valueOf(portalId));
            middleUserPortal.setUserId(Long.valueOf(userId));
            middleUserPortal.setTenantId(100000L);
            middleUserPortal.setId(IdTool.getId());
            userMirrorImageMapper.insertUserPortal(middleUserPortal);
        }
    }

    private void verifySole(UserMirrorImageParam userMirrorImageParam) {
        verifyUserPhone(userMirrorImageParam);
        if (StringUtils.isNotBlank(userMirrorImageParam.getUserName())) {
            int userCount2 = userMirrorImageMapper.selectUserByUserName(userMirrorImageParam.getUserName());
            if (userCount2 > 0) {
                throw new BusinessException("重复的用户名!");
            }
        }
        verifyUserEmail(userMirrorImageParam);
    }

    private void verifyUserEmail(UserMirrorImageParam userMirrorImageParam) {
        if (StringUtils.isNotBlank(userMirrorImageParam.getEmail())) {
            int userCount3 = userMirrorImageMapper.selectUserByEmail(userMirrorImageParam.getEmail());
            if (userCount3 > 0) {
                throw new BusinessException("邮箱不能重复使用!");
            }
        }
    }

    private void verifyUserPhone(UserMirrorImageParam userMirrorImageParam) {
        if (StringUtils.isNotBlank(userMirrorImageParam.getPhone())) {
            int userCount1_1 = userMirrorImageMapper.selectUserImageByPhone(userMirrorImageParam.getPhone());
            if (userCount1_1 > 0) {
                throw new BusinessException("手机号码不能重复使用!");
            }
        }
    }

    private static final Set<String> generatedNumbers = new HashSet<>();
    private static final Random random = new Random();

    public static String generateUniquePhoneNumber() {
        String phoneNumber;
        do {
            // 生成符合规则的手机号：第一位1，第二位3-9，后9位随机
            StringBuilder sb = new StringBuilder(11);
            sb.append("1"); // 第一位固定为1
            sb.append(3 + random.nextInt(7)); // 第二位：3-9
            for (int i = 0; i < 9; i++) {
                sb.append(random.nextInt(10)); // 后9位随机数字
            }
            phoneNumber = sb.toString();
        } while (!generatedNumbers.add(phoneNumber)); // 确保唯一性

        return phoneNumber;
    }

    public String encryptSM4(String plaintext) {
        try {
            // 转换密钥为字节数组
            byte[] keyBytes = hexStringToByteArray(SM4KEY);

            // 初始化SM4引擎（ECB模式）
            SM4Engine engine = new SM4Engine();
            PaddedBufferedBlockCipher cipher = new PaddedBufferedBlockCipher(engine, new ZeroBytePadding());
            cipher.init(true, new KeyParameter(keyBytes));

            // 处理明文
            byte[] input = plaintext.getBytes("UTF-8");
            byte[] output = new byte[cipher.getOutputSize(input.length)];

            int len = cipher.processBytes(input, 0, input.length, output, 0);
            len += cipher.doFinal(output, len);

            // 返回Base64编码结果
            return Base64.toBase64String(output, 0, len);
        } catch (Exception e) {
            throw new RuntimeException("SM4加密失败", e);
        }
    }

    private byte[] hexStringToByteArray(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }
}
