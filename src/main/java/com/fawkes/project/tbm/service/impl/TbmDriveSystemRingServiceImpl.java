package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.mapper.TbmDriveSystemRingMapper;
import com.fawkes.project.tbm.common.model.TbmDriveSystemRing;
import com.fawkes.project.tbm.service.TbmDriveSystemRingService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version TbmDriveSystemRingService 2025/6/11 9:33
 */
@Service
public class TbmDriveSystemRingServiceImpl
        extends ServiceImpl<TbmDriveSystemRingMapper, TbmDriveSystemRing>
        implements TbmDriveSystemRingService {
    /**
     * 多条件查询
     */
    @Override
    public List<TbmDriveSystemRing> queryList(LambdaQueryWrapper<TbmDriveSystemRing> wrapper) {
        return this.list(wrapper);
    }

    /**
     * 批量新增
     */
    @Override
    public Boolean addBatch(List<TbmDriveSystemRing> list) {
        return this.saveBatch(list);
    }

    /**
     * 条件修改
     */
    @Override
    public Boolean edit(LambdaUpdateWrapper<TbmDriveSystemRing> wrapper) {
        return this.update(wrapper);
    }
}
