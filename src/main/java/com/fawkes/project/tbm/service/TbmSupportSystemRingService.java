package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmEarthSupportRing;
import com.fawkes.project.tbm.common.model.TbmSupportSystemRing;

import java.util.List;

/**
 * <AUTHOR>
 * @version TbmEarthSupportRingService 2025/6/11 9:34
 */
public interface TbmSupportSystemRingService extends IService<TbmSupportSystemRing> {

    /**
     * 多条件查询
     * @param wrapper
     * @return
     */
    List<TbmSupportSystemRing> queryList(LambdaQueryWrapper<TbmSupportSystemRing> wrapper);
}
