package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmCutDiskRing;
import com.fawkes.project.tbm.common.model.TbmDriveSystemRing;

import java.util.List;

/**
 * <AUTHOR>
 * @version TbmDriveSystemRingService 2025/6/11 9:33
 */
public interface TbmDriveSystemRingService extends IService<TbmDriveSystemRing> {
    /**
     * 多条件查询
     * @param wrapper
     * @return
     */
    List<TbmDriveSystemRing> queryList(LambdaQueryWrapper<TbmDriveSystemRing> wrapper);

    /**
     * 批量新增
     * @param list
     * @return
     */
    Boolean addBatch(List<TbmDriveSystemRing> list);

    /**
     * 条件修改
     * @param wrapper
     * @return
     */
    Boolean edit(LambdaUpdateWrapper<TbmDriveSystemRing> wrapper);

}
