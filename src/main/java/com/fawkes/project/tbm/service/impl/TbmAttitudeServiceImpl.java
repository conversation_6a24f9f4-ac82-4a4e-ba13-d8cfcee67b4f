package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.constants.IOTDeviceConstants;
import com.fawkes.project.tbm.common.enums.AssemblyModeEnums;
import com.fawkes.project.tbm.common.enums.RunningStateEnums;
import com.fawkes.project.tbm.common.handler.MonthTableNameHandler;
import com.fawkes.project.tbm.common.handler.MonthTableNameHandler;
import com.fawkes.project.tbm.common.mapper.TbmAttitudeMapper;
import com.fawkes.project.tbm.common.model.TbmAttitude;
import com.fawkes.project.tbm.common.utils.CoordinateConvertUtils;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.common.vo.TbmAttitudeXYVO;
import com.fawkes.project.tbm.service.TbmAttitudeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * TBM姿态参数表(此表为原始表，数据按月分表) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Service
public class TbmAttitudeServiceImpl extends ServiceImpl<TbmAttitudeMapper, TbmAttitude> implements TbmAttitudeService {

    @Resource
    private TbmAttitudeMapper tbmAttitudeMapper;

    @Override
    public TbmAttitude getLastTbmAttitude(String deviceCode) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        TbmAttitude tbmAttitude = this.lambdaQuery().eq(TbmAttitude::getCode, deviceCode)
                .orderByDesc(TbmAttitude::getCreateTime).last("LIMIT  1").one();
        MonthTableNameHandler.removeMonthData();
        return tbmAttitude;
    }

    @Override
    public List<TbmAttitude> getLastTenRingNumTbmAttitude(String deviceCode, Integer ringNum) {
        Integer minRingNum = 0;
        if (ringNum - 9 > 0) {
            minRingNum = ringNum - 9;
        }
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        List<TbmAttitude> tbmAttitudes = this.lambdaQuery().eq(TbmAttitude::getCode, deviceCode)
                .between(TbmAttitude::getRingNum, minRingNum, ringNum).list();
        MonthTableNameHandler.removeMonthData();
        return tbmAttitudes;
    }

    /**
     * 获取最后十个环号对应的数据
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     */
    @Override
    public List<TbmAttitudeXYVO> getRingNumTbmAttitude(String deviceCode, List<Integer> ringNumList) {
        String tableName = "tbm_attitude_" + DateUtils.getYyyyMM();
        return tbmAttitudeMapper.getRingNumTbmAttitude(deviceCode,ringNumList,tableName);
    }


    /**
     * 同步导向系统
     * @param iotResponseList
     * @return
     */
    @Override
    public Boolean syncTbmAttitude(List<IOTResponse> iotResponseList, String deviceCode) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());

        String horizontalFront = getPropertyValue(iotResponseList, IOTDeviceConstants.HORIZONTAL_FRONT);
        String horizontalEnd = getPropertyValue(iotResponseList, IOTDeviceConstants.HORIZONTAL_END);

        String verticalFront = getPropertyValue(iotResponseList, IOTDeviceConstants.VERTICAL_FRONT);
        String verticalEnd = getPropertyValue(iotResponseList, IOTDeviceConstants.VERTICAL_END);

        String rollingAngle = getPropertyValue(iotResponseList, IOTDeviceConstants.ROLLING_ANGLE);
        String pitchAngle = getPropertyValue(iotResponseList, IOTDeviceConstants.PITCH_ANGLE);
        String dishCoordinateX = getPropertyValue(iotResponseList, IOTDeviceConstants.DISH_COORDINATE_X);
        String dishCoordinateY = getPropertyValue(iotResponseList, IOTDeviceConstants.DISH_COORDINATE_Y);
        String dishCoordinateZ = getPropertyValue(iotResponseList, IOTDeviceConstants.DISH_COORDINATE_Z);

        String mileage = getPropertyValue(iotResponseList, IOTDeviceConstants.MILEAGE);

        Integer ringNum = null;
        Optional<String> optionalR = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty()
                .equals(IOTDeviceConstants.RING_NUM)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalR.isPresent()) {
            ringNum = Integer.valueOf(optionalR.get());
        }

        //推进状态
        String runingState = getPropertyValue(iotResponseList, IOTDeviceConstants.RUNING_STATE);
        //拼装模式
        String assemblyMode = getPropertyValue(iotResponseList, IOTDeviceConstants.ASSEMBLY_MODE);

        TbmAttitude tbmAttitude = new TbmAttitude();
        tbmAttitude.setCode(deviceCode);
        tbmAttitude.setRingNum(ringNum);
        tbmAttitude.setHorizontalFront(buildStringToBigDecimal(horizontalFront));
        tbmAttitude.setHorizontalEnd(buildStringToBigDecimal(horizontalEnd));
        tbmAttitude.setVerticalFront(buildStringToBigDecimal(verticalFront));
        tbmAttitude.setVerticalEnd(buildStringToBigDecimal(verticalEnd));
        tbmAttitude.setRollingAngle(buildStringToBigDecimal(rollingAngle));
        tbmAttitude.setPitchAngle(buildStringToBigDecimal(pitchAngle));
        tbmAttitude.setMileage(buildStringToBigDecimal(mileage));
        tbmAttitude.setDishCoordinateX(dishCoordinateX);
        tbmAttitude.setDishCoordinateY(dishCoordinateY);
        tbmAttitude.setDishCoordinateZ(dishCoordinateZ);

        double[] longitudeAndLatitudeArr = CoordinateConvertUtils.convertLongitudeAndLatitude(Double.parseDouble(dishCoordinateY), Double.parseDouble(dishCoordinateX));
        if(longitudeAndLatitudeArr != null &&  longitudeAndLatitudeArr.length == 2){
            //经度
            tbmAttitude.setLongitude(String.valueOf(longitudeAndLatitudeArr[0]));
            //纬度
            tbmAttitude.setLatitude(String.valueOf(longitudeAndLatitudeArr[1]));

            double[] gcj02 = CoordinateConvertUtils.transform(longitudeAndLatitudeArr[0], longitudeAndLatitudeArr[1]);
            tbmAttitude.setLongitudeGc(String.valueOf(gcj02[0]));
            tbmAttitude.setLatitudeGc(String.valueOf(gcj02[1]));
        }

        String runingStateStr = null;
        //有两个状态值，推进状态/拼装状态，这两个分别为1时表示推进/拼装，两个都为0时表示停机
        if(StringUtils.equals(RunningStateEnums.SHUT_DOWN.getCode() , runingState) && StringUtils.equals(AssemblyModeEnums.ASSEMBLY_NO_IN_PROGRESS.getCode() , assemblyMode)){
            runingStateStr = RunningStateEnums.SHUT_DOWN.getName();
        }

        if(StringUtils.equals(RunningStateEnums.TUNNELING_IN_PROGRESS.getCode() , runingState)){
            runingStateStr = RunningStateEnums.TUNNELING_IN_PROGRESS.getName();
        }

        if(StringUtils.equals(AssemblyModeEnums.ASSEMBLY_IN_PROGRESS.getCode() , assemblyMode)){
            runingStateStr = AssemblyModeEnums.ASSEMBLY_IN_PROGRESS.getName();
        }

        tbmAttitude.setRuningState(runingStateStr);
        tbmAttitude.setCreateTime(new Timestamp(System.currentTimeMillis()));
        tbmAttitude.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        int insert = tbmAttitudeMapper.insert(tbmAttitude);
        MonthTableNameHandler.removeMonthData();
        return insert > 0;
    }

    private String getPropertyValue(List<IOTResponse> iotResponseList, String propertyName) {
        return iotResponseList.stream()
                .filter(iotResponse -> iotResponse.getProperty().equals(propertyName))
                .map(IOTResponse::getFormatValue)
                .findFirst()
                .orElse(null);
    }

    private BigDecimal buildStringToBigDecimal(String value) {
        if (value == null) {
            return null;
        }
        return new BigDecimal(value);
    }
}
