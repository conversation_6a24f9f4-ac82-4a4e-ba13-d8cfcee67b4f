package com.fawkes.project.tbm.service;

import com.fawkes.project.tbm.common.param.TestDataImportParam;
import com.fawkes.project.tbm.common.vo.*;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <p>
 * 集群大屏 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
public interface StateBigscreenService {

    StateDeviceInfoVO getDeviceBaseInfo(String deviceCode);

    List<CameraManageVO> listCamera(String deviceCode);

    List<StateHarmfulGasDeviceVO> listHarmfulGas(String deviceCode);

    TbmCutDiskVO getCutDisk(String deviceCode);

    TbmDriveSystemVO getDriveSystem(String deviceCode, String electricCurrentCode, String motorTemperature);

    TbmPropulsionSystemVO getPropulsionSystem(String deviceCode, String cylinderPressureStr);

    TbmTappingSystemVO getTappingSystem(String deviceCode);

    TbmAttitudeVO getAttitude(String deviceCode);

    TbmEarthSupportTotalVO listSupportSystem(String deviceCode, String position);

    void importTestData(List<TestDataImportParam> testDataImportParams, String deviceCode);

    PageInfo<TbmSystemAlarmVO> page(Long alarmId, String alarmType, Integer pageNo, Integer pageSize);

    TunnelingProgressVO getTunnelingProgress(String deviceCode);
}
