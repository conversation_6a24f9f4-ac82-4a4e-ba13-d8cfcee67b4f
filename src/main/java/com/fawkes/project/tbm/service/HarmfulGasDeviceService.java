package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.HarmfulGasDevice;
import com.fawkes.project.tbm.common.param.HarmfulGasDeviceParam;
import com.fawkes.project.tbm.common.param.HarmfulGasSetParam;
import com.fawkes.project.tbm.common.query.HarmfulGasDeviceQuery;
import com.fawkes.project.tbm.common.vo.HarmfulGasDeviceVO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <p>
 * 有害气体设备管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface HarmfulGasDeviceService extends IService<HarmfulGasDevice> {

    PageInfo<HarmfulGasDeviceVO> list(HarmfulGasDeviceQuery query);
    List<HarmfulGasDevice> listByDeviceCode(String deviceCode);
    void add(HarmfulGasDeviceParam param);

    void update(HarmfulGasDeviceParam param);

    boolean delete(String id);

    void setThreshold(HarmfulGasSetParam param);

    HarmfulGasDeviceVO get(String id);


}
