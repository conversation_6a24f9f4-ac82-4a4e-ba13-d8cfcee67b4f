package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.mapper.TbmTappingSystemRingMapper;
import com.fawkes.project.tbm.common.model.TbmTappingSystemRing;
import com.fawkes.project.tbm.service.TbmTappingSystemRingService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version TbmTappingSystemRingService 2025/6/11 9:36
 */
@Service
public class TbmTappingSystemRingServiceImpl
        extends ServiceImpl<TbmTappingSystemRingMapper, TbmTappingSystemRing>
        implements TbmTappingSystemRingService {
    /**
     * 多条件查询
     */
    @Override
    public List<TbmTappingSystemRing> queryList(LambdaQueryWrapper<TbmTappingSystemRing> wrapper) {
        return this.list(wrapper);
    }

    /**
     * 批量新增
     */
    @Override
    public Boolean addBatch(List<TbmTappingSystemRing> list) {
        return this.saveBatch(list);
    }

    /**
     * 条件修改
     */
    @Override
    public Boolean edit(LambdaUpdateWrapper<TbmTappingSystemRing> wrapper) {
        return this.update(wrapper);
    }
}
