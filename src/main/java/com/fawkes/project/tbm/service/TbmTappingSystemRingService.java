package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmPropulsionSystemRing;
import com.fawkes.project.tbm.common.model.TbmTappingSystemRing;

import java.util.List;

/**
 * <AUTHOR>
 * @version TbmTappingSystemRingService 2025/6/11 9:36
 */
public interface TbmTappingSystemRingService  extends IService<TbmTappingSystemRing> {
    /**
     * 多条件查询
     * @param wrapper
     * @return
     */
    List<TbmTappingSystemRing> queryList(LambdaQueryWrapper<TbmTappingSystemRing> wrapper);

    /**
     * 批量新增
     * @param list
     * @return
     */
    Boolean addBatch(List<TbmTappingSystemRing> list);

    /**
     * 条件修改
     * @param wrapper
     * @return
     */
    Boolean edit(LambdaUpdateWrapper<TbmTappingSystemRing> wrapper);

}
