package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmPropulsionSystem;
import com.fawkes.project.tbm.common.model.TbmTappingSystem;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.common.vo.TbmTappingSystemXYVO;

import java.util.List;

/**
 * <p>
 * TBM出渣系统参数(此表为原始表，数据按月分表) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
public interface TbmTappingSystemService extends IService<TbmTappingSystem> {

    // 根据设备编码获取最近的推进系统数据
    TbmTappingSystem getLastTbmTappingSystem(String deviceCode);

    // 根据设备编码和环号获取最后十个环号对应的 TbmTappingSystem 对象
    List<TbmTappingSystem> getLastTenRingNumTbmTappingSystem(String deviceCode, Integer ringNum);

    /**
     * 获取最后十个环号对应的 TbmTappingSystem 对象
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     * @return
     */
    List<TbmTappingSystemXYVO> getRingNumTbmTappingSystem(String deviceCode, List<Integer> ringNumList);


    /**
     * 同步螺机系统
     *
     * @param iotResponseList
     */
     Boolean syncTbmTappingSystem(List<IOTResponse> iotResponseList, String deviceCode);
}
