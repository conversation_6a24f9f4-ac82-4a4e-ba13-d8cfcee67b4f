package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.ToolManagement;
import com.fawkes.project.tbm.common.param.ToolManagementImportResult;
import com.fawkes.project.tbm.common.param.ToolManagementParam;
import com.fawkes.project.tbm.common.query.ToolManagementExportQuery;
import com.fawkes.project.tbm.common.vo.*;
import com.fawkes.project.tbm.common.vo.app.ToolManagementAppVO;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/6/3 18:01
 */
public interface IToolManagementService extends IService<ToolManagement> {

    /**
     * 分页查询
     *
     * @param sectionList
     * @param toolName
     * @param createName
     * @param createStartDate
     * @param createEndDate
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageInfo<ToolManagementVO> page(List<String> sectionList, String toolName, String toolLocation, String wearValue, String createName,
                                    String createStartDate, String createEndDate, Integer pageNo, Integer pageSize);

    /**
     * 新增
     *
     * @param param 新增刀具
     * @return true成功/false失败
     */
    Boolean add(ToolManagementParam param) throws IllegalAccessException;

    /**
     * 详情
     *
     * @param id 刀具id
     * @return 刀具详情
     */
    ToolManagementVO getById(String id);

    /**
     * 删除
     *
     * @param id 刀具id
     * @return true成功/false失败
     * @throws IllegalAccessException 填充异常
     */
    Boolean delete(String id, Integer dataSource) throws IllegalAccessException;

    /**
     * 导入
     *
     * @return 导入结果
     */
    ToolManagementImportResult importExcel(MultipartFile file, HttpServletResponse response) throws IOException;

    /**
     * 导出
     * @param query 刀具管理id
     * @param response 响应流
     */
    void export(ToolManagementExportQuery query, HttpServletResponse response) throws IOException;

    /**
     * 统计磨损值
     * @param section  区域code
     * @param toolPositionManageId 刀具位置id
     * @return 磨损统计列表
     */
    List<ToolWearStatisticsVO> statisticsToolWear(String section, Long toolPositionManageId);

    /**
     * 刀具磨损比较
     * @param section 区域code
     * @param groupId 组id
     * @return 磨损比较列表
     */
    List<ToolWearCompareVO> toolWearCompare(String section, Long groupId);

    /**
     * 刀具全局展示
     * @param section 区域code
     * @return
     */
    ToolDisplayVO toolDisplay(String section);

    /**
     * 刀具磨损预警
     */
    PageInfo<ToolWearEarlyWarningVO> toolWearEarlyWarning(String section, Integer pageNo, Integer pageSize);

    /**
     * 刀具磨损规律
     * @param section 区域code
     * @param groupId 刀具位置id
     * @return 磨损规律列表
     */
    List<ToolWearRuleVO> toolWearRule(String section, Long groupId);

    /**
     * 分页列表查询
     *
     * @param section 区段
     * @param searchValue 模糊查询（刀具名称、刀具位置、磨损值）值
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return
     */
    PageInfo<ToolManagementAppVO> pageApp(String section, String searchValue, Integer pageNo, Integer pageSize);
}
