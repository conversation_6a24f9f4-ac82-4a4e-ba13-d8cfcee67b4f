package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.constants.IOTDeviceConstants;
import com.fawkes.project.tbm.common.handler.MonthTableNameHandler;
import com.fawkes.project.tbm.common.handler.MonthTableNameHandler;
import com.fawkes.project.tbm.common.mapper.TbmTappingSystemMapper;
import com.fawkes.project.tbm.common.model.TbmTappingSystem;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.common.vo.TbmTappingSystemXYVO;
import com.fawkes.project.tbm.service.TbmTappingSystemService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * TBM出渣系统参数(此表为原始表，数据按月分表) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Service
public class TbmTappingSystemServiceImpl extends ServiceImpl<TbmTappingSystemMapper, TbmTappingSystem> implements TbmTappingSystemService {

    @Resource
    private TbmTappingSystemMapper tbmTappingSystemMapper;

    @Override
    public TbmTappingSystem getLastTbmTappingSystem(String deviceCode) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        List<TbmTappingSystem> tbmTappingSystems = this.lambdaQuery()
                .eq(TbmTappingSystem::getCode, deviceCode)
                .orderByDesc(TbmTappingSystem::getCreateTime)
                .last("LIMIT  1").list();
        MonthTableNameHandler.removeMonthData();
        if (CollectionUtils.isNotEmpty(tbmTappingSystems)) {
            return tbmTappingSystems.get(0);
        }
        return null;
    }

    @Override
    public List<TbmTappingSystem> getLastTenRingNumTbmTappingSystem(String deviceCode, Integer ringNum) {
        Integer minRingNum = 0;
        if (ringNum - 9 > 0) {
            minRingNum = ringNum - 9;
        }
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        List<TbmTappingSystem> list = this.lambdaQuery().eq(TbmTappingSystem::getCode, deviceCode)
                .between(TbmTappingSystem::getRingNum, minRingNum, ringNum).list();
        MonthTableNameHandler.removeMonthData();
        return list;
    }

    /**
     * 获取最后十个环号对应的 TbmTappingSystem 对象
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     */
    @Override
    public List<TbmTappingSystemXYVO> getRingNumTbmTappingSystem(String deviceCode, List<Integer> ringNumList) {
        String tableName = "tbm_tapping_system_" + DateUtils.getYyyyMM();
        return tbmTappingSystemMapper.getRingNumTbmTappingSystem(deviceCode, ringNumList, tableName);
    }


    /**
     * 同步螺机系统
     *
     * @param iotResponseList
     */
    @Override
    public Boolean syncTbmTappingSystem(List<IOTResponse> iotResponseList, String deviceCode) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        String snailRotationalSpeed = null;
        String snailTorchque = null;
        String beltRotationalSpeed = null;
        Optional<String> optionalS = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty()
                .equals(IOTDeviceConstants.SNAIL_ROTATIONAL_SPEED)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalS.isPresent()) {
            snailRotationalSpeed = optionalS.get();
        }

        Optional<String> optionalA = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty()
                .equals(IOTDeviceConstants.SNAIL_TORCHQUE)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalA.isPresent()) {
            snailTorchque = optionalA.get();
        }

        Optional<String> optionalP = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty()
                .equals(IOTDeviceConstants.BELT_ROTATIONAL_SPEED)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalP.isPresent()) {
            beltRotationalSpeed = optionalP.get();
        }

        Integer ringNum = null;
        Optional<String> optionalR = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty()
                .equals(IOTDeviceConstants.RING_NUM)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalR.isPresent()) {
            ringNum = Integer.valueOf(optionalR.get());
        }
        TbmTappingSystem tbmTappingSystem = new TbmTappingSystem();
        tbmTappingSystem.setCode(deviceCode);
        tbmTappingSystem.setRingNum(ringNum);
        tbmTappingSystem.setSnailRotationalSpeed(Optional.ofNullable(snailRotationalSpeed).map(BigDecimal::new).orElse(null));
        tbmTappingSystem.setSnailTorchque(Optional.ofNullable(snailTorchque).map(BigDecimal::new).orElse(null));
        tbmTappingSystem.setBeltRotationalSpeed(Optional.ofNullable(beltRotationalSpeed).map(BigDecimal::new).orElse(null));
        tbmTappingSystem.setCreateTime(new Timestamp(System.currentTimeMillis()));
        tbmTappingSystem.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        int insert = tbmTappingSystemMapper.insert(tbmTappingSystem);
        MonthTableNameHandler.removeMonthData();
        return insert > 0;
    }

}
