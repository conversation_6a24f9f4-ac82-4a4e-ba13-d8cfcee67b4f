package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.mapper.TbmSupportSystemRingMapper;
import com.fawkes.project.tbm.common.model.TbmSupportSystemRing;
import com.fawkes.project.tbm.service.TbmSupportSystemRingService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version TbmEarthSupportRingService 2025/6/11 9:34
 */
@Service
public class TbmSupportSystemRingServiceImpl
        extends ServiceImpl<TbmSupportSystemRingMapper, TbmSupportSystemRing>
        implements TbmSupportSystemRingService {

    @Override
    public List<TbmSupportSystemRing> queryList(LambdaQueryWrapper<TbmSupportSystemRing> wrapper) {
        return this.list(wrapper);
    }
}
