package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmOperationLog;
import com.fawkes.project.tbm.common.model.ProcessManagement;
import com.fawkes.project.tbm.common.model.ToolManagement;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 操作记录表Service接口
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IOperationLogService extends IService<TbmOperationLog> {

    /**
     * 记录刀具管理新增操作
     * @param toolManagement 刀具管理记录
     */
    void recordToolManagementAddOperation(ToolManagement toolManagement, Integer dataSource);

    /**
     * 记录刀具管理修改操作
     * @param oldToolManagement 修改前的刀具管理记录
     * @param newToolManagement 修改后的刀具管理记录
     */
    void recordToolManagementUpdateOperation(ToolManagement oldToolManagement, ToolManagement newToolManagement, Integer dataSource);

    /**
     * 记录刀具管理删除操作
     * @param toolManagement 被删除的刀具管理记录
     */
    void recordToolManagementDeleteOperation(ToolManagement toolManagement, Integer dataSource);

    /**
     * 记录工序管理新增操作
     * @param processManagement 工序管理记录
     */
    void recordProcessManagementAddOperation(ProcessManagement processManagement, Integer dataSource);

    /**
     * 记录工序管理修改操作
     * @param oldProcessManagement 修改前的工序管理记录
     * @param newProcessManagement 修改后的工序管理记录
     */
    void recordProcessManagementUpdateOperation(ProcessManagement oldProcessManagement, ProcessManagement newProcessManagement, Integer dataSource);

    /**
     * 记录工序管理删除操作
     * @param processManagement 被删除的工序管理记录
     */
    void recordProcessManagementDeleteOperation(ProcessManagement processManagement, Integer dataSource);

    /**
     * 分页查询操作记录
     * @param moduleType 模块类型
     * @param recordId 记录ID
     * @param operationType 操作类型
     * @param operator 操作人
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 操作记录分页结果
     */
    PageInfo<TbmOperationLog> pageOperationLogs(String moduleType, Long recordId, String operationType,
                                                String operator, String startTime, String endTime,
                                                Integer pageNo, Integer pageSize);

    /**
     * 根据记录ID查询操作记录
     * @param moduleType 模块类型
     * @param recordId 记录ID
     * @return 操作记录列表
     */
    List<TbmOperationLog> getOperationLogsByRecordId(String moduleType, Long recordId);
} 