package com.fawkes.project.tbm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.core.exception.BusinessException;
import com.fawkes.project.tbm.common.mapper.HarmfulGasDeviceMapper;
import com.fawkes.project.tbm.common.model.HarmfulGasDevice;
import com.fawkes.project.tbm.common.param.HarmfulGasDeviceParam;
import com.fawkes.project.tbm.common.param.HarmfulGasSetParam;
import com.fawkes.project.tbm.common.query.HarmfulGasDeviceQuery;
import com.fawkes.project.tbm.common.utils.ListUtil;
import com.fawkes.project.tbm.common.vo.HarmfulGasDeviceVO;
import com.fawkes.project.tbm.service.HarmfulGasDeviceService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 有害气体设备管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Service
public class HarmfulGasDeviceServiceImpl extends ServiceImpl<HarmfulGasDeviceMapper, HarmfulGasDevice> implements HarmfulGasDeviceService {

    @Override
    public PageInfo<HarmfulGasDeviceVO> list(HarmfulGasDeviceQuery query) {
        PageHelper.startPage(query.getPageNo(), query.getPageSize());
        List<HarmfulGasDevice> harmfulGasDevices = this.lambdaQuery().like(StringUtils.isNotBlank(query.getCode()), HarmfulGasDevice::getCode, query.getCode())
                .like(StringUtils.isNotBlank(query.getName()), HarmfulGasDevice::getName, query.getName())
                .like(StringUtils.isNotBlank(query.getDeviceName()), HarmfulGasDevice::getDeviceName, query.getDeviceName())
                .eq(HarmfulGasDevice::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .orderByDesc(HarmfulGasDevice::getUpdateDate).list();
        if (CollectionUtils.isEmpty(harmfulGasDevices)) {
            return new PageInfo<>();
        }
        List<HarmfulGasDeviceVO> harmfulGasDeviceVOS = BeanUtil.copyToList(harmfulGasDevices, HarmfulGasDeviceVO.class);
        return ListUtil.pageConvert(harmfulGasDevices, harmfulGasDeviceVOS);
    }

    @Override
    public List<HarmfulGasDevice> listByDeviceCode(String deviceCode) {
        List<HarmfulGasDevice> harmfulGasDeviceList = this.lambdaQuery().eq(HarmfulGasDevice::getDeviceCode, deviceCode)
                .eq(HarmfulGasDevice::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag()).list();
        return harmfulGasDeviceList;

    }

    @Override
    public void add(HarmfulGasDeviceParam param) {
        //需要判断新设备的编码code是否已经被使用
        Integer countCode = this.lambdaQuery().eq(HarmfulGasDevice::getCode, param.getCode())
                .eq(HarmfulGasDevice::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .count();
        if (countCode > 0) {
            throw new BusinessException("该设备编号已存在，请勿重复");
        }

        HarmfulGasDevice harmfulGasDevice = BeanUtil.copyProperties(param, HarmfulGasDevice.class);
        this.save(harmfulGasDevice);
    }

    @Override
    public void update(HarmfulGasDeviceParam param) {
        //需要判断新设备的编码code是否已经被使用
        Integer countCode = this.lambdaQuery().eq(HarmfulGasDevice::getCode, param.getCode())
                .eq(HarmfulGasDevice::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .notIn( HarmfulGasDevice::getId, param.getId())
                .count();
        if (countCode > 0) {
            throw new BusinessException("该设备编号已存在，请勿重复");
        }

        HarmfulGasDevice harmfulGasDevice = BeanUtil.copyProperties(param, HarmfulGasDevice.class);
        this.updateById(harmfulGasDevice);
    }

    @Override
    public boolean delete(String id) {
        boolean removed = this.removeById(id);
        return removed;
    }

    @Override
    public void setThreshold(HarmfulGasSetParam param) {
        HarmfulGasDevice harmfulGasDevice = this.getById(param.getId());
        boolean validNumber = isValidNumber(param.getCh4Level().toString());
        if (!validNumber) {
            throw new BusinessException("CH₄输入数字必须是正数，整数限制4位，小数点保留两位");
        }
        validNumber = isValidNumber(param.getH2sLevel().toString());
        if (!validNumber) {
            throw new BusinessException("CO₂输入数字必须是正数，整数限制4位，小数点保留两位");
        }
        validNumber = isValidNumber(param.getO2Level().toString());
        if (!validNumber) {
            throw new BusinessException("O₂输入数字必须是正数，整数限制4位，小数点保留两位");
        }
        validNumber = isValidNumber(param.getCoLevel().toString());
        if (!validNumber) {
            throw new BusinessException("CO输入数字必须是正数，整数限制4位，小数点保留两位");
        }
        harmfulGasDevice.setCh4Level(param.getCh4Level());
        harmfulGasDevice.setH2sLevel(param.getH2sLevel());
        harmfulGasDevice.setO2Level(param.getO2Level());
        harmfulGasDevice.setCoLevel(param.getCoLevel());
        boolean b = this.updateById(harmfulGasDevice);
    }

    @Override
    public HarmfulGasDeviceVO get(String id) {
        HarmfulGasDevice harmfulGasDevice = this.getById(id);
        HarmfulGasDeviceVO harmfulGasDeviceVO = BeanUtil.copyProperties(harmfulGasDevice, HarmfulGasDeviceVO.class);
        return harmfulGasDeviceVO;

    }

    /**
     * 验证字符串是否是正数，整数限制4位，小数点保留两位
     * @param number
     * @return
     */
    private boolean isValidNumber(String number) {
        // 首先检查格式
        boolean matches = number.matches("^\\d{1,4}(\\.\\d{1,2})?$");

        if (!matches) {
            return false; // 格式不正确，直接返回false
        }

        // 尝试将字符串转换为double类型（这里可以使用BigDecimal来获得更高的精度）
        try {
            double value = Double.parseDouble(number);

            // 检查数字是否大于0（正数）
            return value > 0;
        } catch (NumberFormatException e) {
            // 如果字符串无法解析为数字（尽管matches已经检查过了，但这里作为额外的安全措施）
            return false;
        }
    }


}
