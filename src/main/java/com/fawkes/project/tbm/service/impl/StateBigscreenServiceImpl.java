package com.fawkes.project.tbm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.project.tbm.common.config.MyBatisPlusConfig;
import com.fawkes.project.tbm.common.handler.MonthTableNameHandler;
import com.fawkes.project.tbm.common.mapper.*;
import com.fawkes.project.tbm.common.model.*;
import com.fawkes.project.tbm.common.param.TestDataImportParam;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.common.utils.ListUtil;
import com.fawkes.project.tbm.common.utils.StringUtil;
import com.fawkes.project.tbm.common.vo.*;
import com.fawkes.project.tbm.service.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 摄像头配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Slf4j
@Service
public class StateBigscreenServiceImpl implements StateBigscreenService {
    @Resource
    private DeviceManageService deviceManageService;

    @Resource
    private TbmAttitudeService tbmAttitudeService;

    @Resource
    private StatisticsComprehensiveIndexService statisticsComprehensiveIndexService;

    @Resource
    private TbmCutDiskService tbmCutDiskService;

    @Resource
    private HarmfulGasDeviceService harmfulGasDeviceService;

    @Resource
    private TbmHarmfulGasService tbmHarmfulGasService;

    @Resource
    private CameraManageService cameraManageService;

    @Resource
    private TbmDriveSystemService tbmDriveSystemService;

    @Resource
    private TbmPropulsionSystemService tbmPropulsionSystemService;

    @Resource
    private TbmTappingSystemService tbmTappingSystemService;

    @Resource
    private TbmEarthSupportService tbmEarthSupportService;

    @Resource
    private TbmSupportSystemService tbmSupportSystemService;

    @Resource
    private TbmCutDiskRingService tbmCutDiskRingService;

    @Resource
    private TbmDriveSystemRingService tbmDriveSystemRingService;

    @Resource
    private TbmEarthSupportRingService tbmEarthSupportRingService;

    @Resource
    private TbmPropulsionSystemRingService tbmPropulsionSystemRingService;

    @Resource
    private TbmTappingSystemRingService tbmTappingSystemRingService;

    @Resource
    private TbmAttitudeRingService tbmAttitudeRingService;

    @Resource
    private TbmCutDiskAlarmMapper tbmCutDiskAlarmMapper;

    @Resource
    private TbmPropulsionSystemAlarmMapper tbmPropulsionSystemAlarmMapper;

    @Resource
    private TbmDriveSystemAlarmMapper tbmDriveSystemAlarmMapper;

    @Resource
    private TbmAttitudeAlarmMapper tbmAttitudeAlarmMapper;

    @Resource
    private TbmEarthSupportAlarmMapper tbmEarthSupportAlarmMapper;

    @Resource
    private TbmTappingSystemAlarmMapper tbmTappingSystemAlarmMapper;

    @Resource
    private TbmSupportSystemAlarmMapper tbmSupportSystemAlarmMapper;

    @Resource
    private TbmCutDiskAlarmDetailsMapper tbmCutDiskAlarmDetailsMapper;

    @Resource
    private TbmPropulsionSystemAlarmDetailsMapper tbmPropulsionSystemAlarmDetailsMapper;

    @Resource
    private TbmDriveSystemAlarmDetailsMapper tbmDriveSystemAlarmDetailsMapper;

    @Resource
    private TbmAttitudeAlarmDetailsMapper tbmAttitudeAlarmDetailsMapper;

    @Resource
    private TbmEarthSupportAlarmDetailsMapper tbmEarthSupportAlarmDetailsMapper;

    @Resource
    private TbmTappingSystemAlarmDetailsMapper tbmTappingSystemAlarmDetailsMapper;

    @Resource
    private TbmSupportSystemAlarmDetailsMapper tbmSupportSystemAlarmDetailsMapper;

    @Resource
    private TbmAttitudeMapper tbmAttitudeMapper;
    @Autowired
    private TbmSupportSystemRingService tbmSupportSystemRingService;

    @Override
    public StateDeviceInfoVO getDeviceBaseInfo(String deviceCode) {
        // 设备基础信息
        DeviceManage deviceManage = deviceManageService.lambdaQuery().eq(DeviceManage::getCode, deviceCode).last("LIMIT 1").one();
        StateDeviceInfoVO deviceInfoVO = BeanUtil.copyProperties(deviceManage, StateDeviceInfoVO.class);
        if (Objects.isNull(deviceManage)) {
            return deviceInfoVO;
        }
        // 统计表的数据，当前环数，设备运行状态
        StatisticsComprehensiveIndex comprehensiveIndex = statisticsComprehensiveIndexService.lambdaQuery()
                .eq(StatisticsComprehensiveIndex::getCode, deviceManage.getCode()).orderByDesc(StatisticsComprehensiveIndex::getStatisticsDate).last("LIMIT 1").one();
        if (Objects.nonNull(comprehensiveIndex)) {
            deviceInfoVO.setCurrentRingNum(String.valueOf(comprehensiveIndex.getRingNum()));
            deviceInfoVO.setStatus(comprehensiveIndex.getRuningState());
        } else {
            deviceInfoVO.setCurrentRingNum("0");
            deviceInfoVO.setStatus("已停机");
        }
        // 物联网推过来的数据，当前环数，设备运行状态
//        TbmAttitude tbmAttitude = tbmAttitudeService.lambdaQuery().eq(TbmAttitude::getCode, deviceInfoVO.getCode()).orderByDesc(TbmAttitude::getId).last("LIMIT 1").one();
//        deviceInfoVO.setCurrentRingNum(String.valueOf(tbmAttitude.getRingNum()));
//        deviceInfoVO.setStatus(tbmAttitude.getRuningState());
        return deviceInfoVO;
    }

    @Override
    public List<CameraManageVO> listCamera(String deviceCode) {
        List<CameraManage> list = cameraManageService.lambdaQuery().eq(CameraManage::getDeviceCode, deviceCode)
                .eq(CameraManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag()).list();
        List<CameraManageVO> cameraManageVOS = BeanUtil.copyToList(list, CameraManageVO.class);
        cameraManageVOS.forEach(e -> {
            e.setCameraUrl("https://open.ys7.com/v3/openlive/FS7562710_1_1.m3u8?expire=1770600123&id=810440714545528832&t=c4c1dc7cf39b1ea44b39f0078e7e8948e0caae0dbdabd85eb31487539987fe08&ev=100");
        });
        return cameraManageVOS;
    }

    @Override
    public List<StateHarmfulGasDeviceVO> listHarmfulGas(String deviceCode) {
        List<StateHarmfulGasDeviceVO> result = new ArrayList<>();
        List<HarmfulGasDevice> harmfulGasDevices = harmfulGasDeviceService.listByDeviceCode(deviceCode);
        if (CollectionUtils.isEmpty(harmfulGasDevices)) {
            return result;
        }
        List<String> deviceCodes = harmfulGasDevices.stream().map(HarmfulGasDevice::getDeviceCode).collect(Collectors.toList());
        //根据有害气体code查询出各个code的最后一条有害气体，组成列表
        List<TbmHarmfulGas> tbmHarmfulGases = tbmHarmfulGasService.selectByCodeInLastOne(deviceCodes);


        harmfulGasDevices.forEach(e -> {
            StateHarmfulGasDeviceVO stateHarmfulGasDeviceVO = BeanUtil.copyProperties(e, StateHarmfulGasDeviceVO.class);
            Optional<TbmHarmfulGas> first = tbmHarmfulGases.stream().filter(tbmHarmfulGas -> tbmHarmfulGas.getCode().equals(e.getDeviceCode()))
                    .findFirst();
            if (first.isPresent()) {
                first.ifPresent(tbmHarmfulGas -> {
                    stateHarmfulGasDeviceVO.setCoLevel(formatToTwoDecimalPlaces(tbmHarmfulGas.getCoLevel()));
                    stateHarmfulGasDeviceVO.setH2sLevel(formatToTwoDecimalPlaces(tbmHarmfulGas.getH2sLevel()));
                    stateHarmfulGasDeviceVO.setCh4Level(formatToTwoDecimalPlaces(tbmHarmfulGas.getCh4Level()));
                    stateHarmfulGasDeviceVO.setO2Level(formatToTwoDecimalPlaces(tbmHarmfulGas.getO2Level()));
                    Boolean b = judgeHarmfulGas(e, tbmHarmfulGas);
                    stateHarmfulGasDeviceVO.setAlarm(b);

                });
            } else {
                stateHarmfulGasDeviceVO.setCoLevel("--");
                stateHarmfulGasDeviceVO.setH2sLevel("--");
                stateHarmfulGasDeviceVO.setCh4Level("--");
                stateHarmfulGasDeviceVO.setO2Level("--");
                stateHarmfulGasDeviceVO.setAlarm(false);
            }


            result.add(stateHarmfulGasDeviceVO);
        });

        return result;
    }

    @Override
    public TbmCutDiskVO getCutDisk(String deviceCode) {
        TbmCutDiskVO result = new TbmCutDiskVO();

        TbmCutDisk lastTbmCutDisk = tbmCutDiskService.getLastTbmCutDisk(deviceCode);
        if (lastTbmCutDisk == null) {
            return result;
        }
        result.setId(lastTbmCutDisk.getId());
        result.setCode(lastTbmCutDisk.getCode());
        result.setCutDiskTorchque(formatToTwoDecimalPlaces(lastTbmCutDisk.getCutDiskTorchque()));
        result.setActualRpm(formatToTwoDecimalPlaces(lastTbmCutDisk.getActualRpm()));
        result.setPenetration(formatToTwoDecimalPlaces(lastTbmCutDisk.getPenetration()));
        //todo 分表
        List<TbmCutDiskRing> tbmCutDiskRings = tbmCutDiskRingService.queryList(Wrappers.<TbmCutDiskRing>lambdaQuery()
                .eq(TbmCutDiskRing::getCode, deviceCode).orderByDesc(TbmCutDiskRing::getMaxTime)
                .last("limit 10"));
        List<Integer> list = ListUtil.distinctMap(tbmCutDiskRings, TbmCutDiskRing::getRingNum);
        List<TbmCutDiskXYVO> ringNumTbmCutDisk = tbmCutDiskService.getRingNumTbmCutDisk(deviceCode, list);
        if (CollectionUtils.isEmpty(ringNumTbmCutDisk)) {
            return result;
        }
        List<TbmXYVO> cutDiskTorchqueList = new ArrayList<>(); //刀盘扭矩列表
        List<TbmXYVO> actualRpmList = new ArrayList<>();  //刀盘转速列表
        List<TbmXYVO> penetrationList = new ArrayList<>();//贯入度列表

        ringNumTbmCutDisk.forEach(ringNum -> {
            cutDiskTorchqueList.add(getTbmXYVO(ringNum.getCutDiskTorchqueMax(), ringNum.getCutDiskTorchqueAverage(), ringNum.getRing()));
            actualRpmList.add(getTbmXYVO(ringNum.getActualRpmMax(), ringNum.getActualRpmAverage(), ringNum.getRing()));
            penetrationList.add(getTbmXYVO(ringNum.getPenetrationMax(), ringNum.getPenetrationAverage(), ringNum.getRing()));
        });

        result.setCutDiskTorchqueList(handleTbmXYVO(cutDiskTorchqueList));
        result.setActualRpmList(handleTbmXYVO(actualRpmList));
        result.setPenetrationList(handleTbmXYVO(penetrationList));
        setTbmCutDiskAlarmStatus(deviceCode, result);
        return result;
    }

    /**
     * 获取刀盘最新的告警状态
     *
     * @param deviceCode
     * @return
     */
    private void setTbmCutDiskAlarmStatus(String deviceCode, TbmCutDiskVO result){
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        LambdaQueryWrapper<TbmCutDiskAlarm> queryWrapper = Wrappers.<TbmCutDiskAlarm>lambdaQuery()
                .eq(TbmCutDiskAlarm::getCode, deviceCode)
                .orderByDesc(TbmCutDiskAlarm::getUpdateTime)
                .last("LIMIT 1");
        TbmCutDiskAlarm tbmCutDiskAlarm = tbmCutDiskAlarmMapper.selectOne(queryWrapper);
        MonthTableNameHandler.removeMonthData();
        if (Objects.nonNull(tbmCutDiskAlarm)) {
            result.setAlarmId(tbmCutDiskAlarm.getId());
            result.setAlarmStatus(tbmCutDiskAlarm.getAlarmFlag());
            result.setRingNum(tbmCutDiskAlarm.getRingNum());
            result.setCreateTime(tbmCutDiskAlarm.getCreateTime());
        }
    }

    /**
     * 获取推进最新的告警状态
     *
     * @param deviceCode
     * @return
     */
    private void setTbmPropulsionSystemAlarmStatus(String deviceCode, TbmPropulsionSystemVO result){
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        LambdaQueryWrapper<TbmPropulsionSystemAlarm> queryWrapper = Wrappers.<TbmPropulsionSystemAlarm>lambdaQuery()
                .eq(TbmPropulsionSystemAlarm::getCode, deviceCode)
                .orderByDesc(TbmPropulsionSystemAlarm::getUpdateTime)
                .last("LIMIT 1");
        TbmPropulsionSystemAlarm tbmPropulsionSystemAlarm = tbmPropulsionSystemAlarmMapper.selectOne(queryWrapper);
        MonthTableNameHandler.removeMonthData();
        if (Objects.nonNull(tbmPropulsionSystemAlarm)) {
            result.setAlarmId(tbmPropulsionSystemAlarm.getId());
            result.setAlarmStatus(tbmPropulsionSystemAlarm.getAlarmFlag());
            result.setRingNum(tbmPropulsionSystemAlarm.getRingNum());
            result.setCreateTime(tbmPropulsionSystemAlarm.getCreateTime());
        }
    }

    /**
     * 获取驱动最新的告警状态
     *
     * @param deviceCode
     * @return
     */
    private void setTbmDriveSystemAlarmStatus(String deviceCode, TbmDriveSystemVO result){
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        LambdaQueryWrapper<TbmDriveSystemAlarm> queryWrapper = Wrappers.<TbmDriveSystemAlarm>lambdaQuery()
                .eq(TbmDriveSystemAlarm::getCode, deviceCode)
                .orderByDesc(TbmDriveSystemAlarm::getUpdateTime)
                .last("LIMIT 1");
        TbmDriveSystemAlarm tbmDriveSystemAlarm = tbmDriveSystemAlarmMapper.selectOne(queryWrapper);
        MonthTableNameHandler.removeMonthData();
        if (Objects.nonNull(tbmDriveSystemAlarm)) {
            result.setAlarmId(tbmDriveSystemAlarm.getId());
            result.setAlarmStatus(tbmDriveSystemAlarm.getAlarmFlag());
            result.setRingNum(tbmDriveSystemAlarm.getRingNum());
            result.setCreateTime(tbmDriveSystemAlarm.getCreateTime());
        }
    }

    /**
     * 获取姿态参数最新的告警状态
     *
     * @param deviceCode
     * @return
     */
    private void setTbmAttitudeAlarmStatus(String deviceCode, TbmAttitudeVO result){
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        LambdaQueryWrapper<TbmAttitudeAlarm> queryWrapper = Wrappers.<TbmAttitudeAlarm>lambdaQuery()
                .eq(TbmAttitudeAlarm::getCode, deviceCode)
                .orderByDesc(TbmAttitudeAlarm::getUpdateTime)
                .last("LIMIT 1");
        TbmAttitudeAlarm tbmAttitudeAlarm = tbmAttitudeAlarmMapper.selectOne(queryWrapper);
        MonthTableNameHandler.removeMonthData();
        if (Objects.nonNull(tbmAttitudeAlarm)) {
            result.setAlarmId(tbmAttitudeAlarm.getId());
            result.setAlarmStatus(tbmAttitudeAlarm.getAlarmFlag());
            result.setRingNum(tbmAttitudeAlarm.getRingNum());
            result.setCreateTime(tbmAttitudeAlarm.getCreateTime());
        }
    }

    /**
     * 获取土仓压力最新的告警状态
     *
     * @param deviceCode
     * @return
     *
     */
    private void setTbmEarthSupportAlarmStatus(String deviceCode, TbmEarthSupportTotalVO result){
        // 设备基础信息
        DeviceManage deviceManage = deviceManageService.lambdaQuery().eq(DeviceManage::getCode, deviceCode).last("LIMIT 1").one();
        if(deviceManage != null){
            MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
            //将北段设备的”设备类型“设置为”盾构“类型，展示成土仓压力；
            //将北段设备的”设备类型“设置为”TBM“类型，展示成撑靴压力
            if(StringUtils.endsWith(deviceManage.getType(), "盾构")){
                LambdaQueryWrapper<TbmEarthSupportAlarm> queryWrapper = Wrappers.<TbmEarthSupportAlarm>lambdaQuery()
                        .eq(TbmEarthSupportAlarm::getCode, deviceCode)
                        .orderByDesc(TbmEarthSupportAlarm::getUpdateTime)
                        .last("LIMIT 1");
                TbmEarthSupportAlarm tbmEarthSupportAlarm = tbmEarthSupportAlarmMapper.selectOne(queryWrapper);
                if (Objects.nonNull(tbmEarthSupportAlarm)) {
                    result.setAlarmId(tbmEarthSupportAlarm.getId());
                    result.setAlarmStatus(tbmEarthSupportAlarm.getAlarmFlag());
                    result.setRingNum(tbmEarthSupportAlarm.getRingNum());
                    result.setCreateTime(tbmEarthSupportAlarm.getCreateTime());
                }
            }

            if(StringUtils.endsWith(deviceManage.getType(), "TBM")){
                LambdaQueryWrapper<TbmSupportSystemAlarm> queryWrapper = Wrappers.<TbmSupportSystemAlarm>lambdaQuery()
                        .eq(TbmSupportSystemAlarm::getCode, deviceCode)
                        .orderByDesc(TbmSupportSystemAlarm::getUpdateTime)
                        .last("LIMIT 1");
                TbmSupportSystemAlarm tbmSupportSystemAlarm = tbmSupportSystemAlarmMapper.selectOne(queryWrapper);
                if (Objects.nonNull(tbmSupportSystemAlarm)) {
                    result.setAlarmId(tbmSupportSystemAlarm.getId());
                    result.setAlarmStatus(tbmSupportSystemAlarm.getAlarmFlag());
                    result.setRingNum(tbmSupportSystemAlarm.getRingNum());
                    result.setCreateTime(tbmSupportSystemAlarm.getCreateTime());
                }
            }
            MonthTableNameHandler.removeMonthData();
        }
    }

    /**
     * 获取出渣系统最新的告警状态
     *
     * @param deviceCode
     * @return
     */
    private void setTbmTappingSystemAlarmStatus(String deviceCode, TbmTappingSystemVO result){
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        LambdaQueryWrapper<TbmTappingSystemAlarm> queryWrapper = Wrappers.<TbmTappingSystemAlarm>lambdaQuery()
                .eq(TbmTappingSystemAlarm::getCode, deviceCode)
                .orderByDesc(TbmTappingSystemAlarm::getUpdateTime)
                .last("LIMIT 1");
        TbmTappingSystemAlarm tbmTappingSystemAlarm = tbmTappingSystemAlarmMapper.selectOne(queryWrapper);
        MonthTableNameHandler.removeMonthData();
        if (Objects.nonNull(tbmTappingSystemAlarm)) {
            result.setAlarmId(tbmTappingSystemAlarm.getId());
            result.setAlarmStatus(tbmTappingSystemAlarm.getAlarmFlag());
            result.setRingNum(tbmTappingSystemAlarm.getRingNum());
            result.setCreateTime(tbmTappingSystemAlarm.getCreateTime());
        }
    }

    @Override
    public TbmDriveSystemVO getDriveSystem(String deviceCode, String electricCurrentCode, String motorTemperature) {
        TbmDriveSystemVO result = new TbmDriveSystemVO();
        TbmDriveSystem lastTbmDriveSystem = tbmDriveSystemService.getLastTbmDriveSystem(deviceCode);
        if (lastTbmDriveSystem == null) {
            return result;
        }
        result.setId(lastTbmDriveSystem.getId());
        result.setCode(lastTbmDriveSystem.getCode());
        result.setPower(formatToTwoDecimalPlaces(lastTbmDriveSystem.getPower()));
        result.setTemperature(formatToTwoDecimalPlaces(StringUtil.getTemperature(lastTbmDriveSystem, motorTemperature)));
        result.setElectricCurrent(formatToTwoDecimalPlaces(StringUtil.getElectricCurrent(lastTbmDriveSystem, electricCurrentCode)));
        //todo 分表
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        List<TbmDriveSystemRing> tbmDriveSystemRings = tbmDriveSystemRingService.queryList(Wrappers.<TbmDriveSystemRing>lambdaQuery()
                .eq(TbmDriveSystemRing::getCode, deviceCode).orderByDesc(TbmDriveSystemRing::getMaxTime)
                .last("limit 10"));
        MonthTableNameHandler.removeMonthData();
        List<Integer> ringNumList = ListUtil.distinctMap(tbmDriveSystemRings, TbmDriveSystemRing::getRingNum);
        List<TbmDriveSystemXYVO> systemXYList = tbmDriveSystemService.getRingNumTbmDriveSystem(deviceCode, ringNumList, motorTemperature,electricCurrentCode);
        if (CollectionUtils.isEmpty(systemXYList)) {
            return result;
        }
        List<TbmXYVO> powerList = new ArrayList<>(); // 电机总功率列表
        List<TbmXYVO> temperatureList = new ArrayList<>();// 点击温度列表
        List<TbmXYVO> electricCurrentList = new ArrayList<>();//电机电流列表
        systemXYList.forEach(ringNum -> {
            powerList.add(getTbmXYVO(ringNum.getPowerMax(), ringNum.getPowerAverage(), ringNum.getRing()));
            temperatureList.add(getTbmXYVO(ringNum.getTemperatureMax(), ringNum.getTemperatureAverage(), ringNum.getRing()));
            electricCurrentList.add(getTbmXYVO(ringNum.getElectricCurrentMax(), ringNum.getElectricCurrentAverage(), ringNum.getRing()));
        });
        result.setPowerList(handleTbmXYVO(powerList));
        result.setTemperatureList(handleTbmXYVO(temperatureList));
        result.setElectricCurrentList(handleTbmXYVO(electricCurrentList));
        setTbmDriveSystemAlarmStatus(deviceCode, result);
        return result;
    }

    @Override
    public TbmPropulsionSystemVO getPropulsionSystem(String deviceCode, String cylinderPressureStr) {
        TbmPropulsionSystemVO result = new TbmPropulsionSystemVO();
        TbmPropulsionSystem lastData = tbmPropulsionSystemService.getLastTbmPropulsionSystem(deviceCode);
        if (lastData == null) {
            return result;
        }
        result.setCode(lastData.getCode());
        result.setId(lastData.getId());
        result.setPropSpeed(formatToTwoDecimalPlaces(lastData.getPropSpeed()));
        result.setThrust(formatToTwoDecimalPlaces(lastData.getThrust()));
        result.setCylinderPressure(formatToTwoDecimalPlaces(StringUtil.getCvlinderPressure(cylinderPressureStr,lastData)));
        //todo 分表
        List<TbmPropulsionSystemRing> tbmPropulsionSystemRings = tbmPropulsionSystemRingService.queryList(Wrappers.<TbmPropulsionSystemRing>lambdaQuery()
                .eq(TbmPropulsionSystemRing::getCode, deviceCode).orderByDesc(TbmPropulsionSystemRing::getMaxTime)
                .last("limit 10"));
        List<Integer> ringNumList = ListUtil.distinctMap(tbmPropulsionSystemRings, TbmPropulsionSystemRing::getRingNum);
        List<TbmPropulsionSystemXYVO> systemXYList = tbmPropulsionSystemService.getRingNumTbmPropulsionSystem(deviceCode, ringNumList, cylinderPressureStr);

        if (CollectionUtils.isEmpty(systemXYList)) {
            return result;
        }
        List<TbmXYVO> thrustList = new ArrayList<>(); // 推力列表
        List<TbmXYVO> propSpeedList = new ArrayList<>();// 推进速度
        List<TbmXYVO> cylinderPressureList = new ArrayList<>();//油缸压力列表
        systemXYList.forEach(ringNum -> {
            thrustList.add(getTbmXYVO(ringNum.getThrustMax(), ringNum.getThrustAverage(), ringNum.getRing()));
            propSpeedList.add(getTbmXYVO(ringNum.getPropSpeedMax(), ringNum.getPropSpeedAverage(), ringNum.getRing()));
            cylinderPressureList.add(getTbmXYVO(ringNum.getCylinderPressureMax(), ringNum.getCylinderPressureAverage(), ringNum.getRing()));
        });

        result.setThrustList(handleTbmXYVO(thrustList));
        result.setPropSpeedList(handleTbmXYVO(propSpeedList));
        result.setCylinderPressureList(handleTbmXYVO(cylinderPressureList));
        setTbmPropulsionSystemAlarmStatus(deviceCode, result);
        return result;
    }

    @Override
    public TbmTappingSystemVO getTappingSystem(String deviceCode) {
        TbmTappingSystemVO result = new TbmTappingSystemVO();
        TbmTappingSystem lastData = tbmTappingSystemService.getLastTbmTappingSystem(deviceCode);
        if (lastData == null) {
            return result;
        }
        result.setCode(lastData.getCode());
        result.setId(lastData.getId());
        result.setSnailRotationalSpeed(formatToTwoDecimalPlaces(lastData.getSnailRotationalSpeed()));
        result.setSnailTorchque(formatToTwoDecimalPlaces(lastData.getSnailTorchque()));
        result.setBeltRotationalSpeed(formatToTwoDecimalPlaces(lastData.getBeltRotationalSpeed()));
        //todo 分表
        List<TbmTappingSystemRing> tbmTappingSystemRings = tbmTappingSystemRingService.queryList(Wrappers.<TbmTappingSystemRing>lambdaQuery()
                .eq(TbmTappingSystemRing::getCode, deviceCode).orderByDesc(TbmTappingSystemRing::getMaxTime)
                .last("limit 10"));
        List<Integer> ringNumList = ListUtil.distinctMap(tbmTappingSystemRings, TbmTappingSystemRing::getRingNum);
        List<TbmTappingSystemXYVO> systemXYLIst = tbmTappingSystemService.getRingNumTbmTappingSystem(deviceCode, ringNumList);
        if (CollectionUtils.isEmpty(systemXYLIst)) {
            return result;
        }
        List<TbmXYVO> snailRotationalSpeedList = new ArrayList<>(); // 螺机转速列表
        List<TbmXYVO> snailTorchqueList = new ArrayList<>();// 螺机扭矩列表
        List<TbmXYVO> beltRotationalSpeedList = new ArrayList<>();//皮带机转速列表

        systemXYLIst.forEach(ringNum -> {
            snailRotationalSpeedList.add(getTbmXYVO(ringNum.getSnailRotationalSpeedMax(), ringNum.getSnailRotationalSpeedAverage(), ringNum.getRing()));
            snailTorchqueList.add(getTbmXYVO(ringNum.getSnailTorchqueMax(), ringNum.getSnailTorchqueAverage(), ringNum.getRing()));
            beltRotationalSpeedList.add(getTbmXYVO(ringNum.getBeltRotationalSpeedMax(), ringNum.getBeltRotationalSpeedAverage(), ringNum.getRing()));
        });

        result.setSnailRotationalSpeedList(handleTbmXYVO(snailRotationalSpeedList));
        result.setSnailTorchqueList(handleTbmXYVO(snailTorchqueList));
        result.setBeltRotationalSpeedList(handleTbmXYVO(beltRotationalSpeedList));
        setTbmTappingSystemAlarmStatus(deviceCode, result);
        return result;

    }

    /**
     * 返回TbmXYVO对象  求平均值的时候保留两位小数
     *
     * @param max
     * @param avg
     * @return
     */
    private TbmXYVO getTbmXYVO(BigDecimal max, BigDecimal avg, String ringNum) {
        TbmXYVO result = new TbmXYVO();
        result.setRing(ringNum);
        result.setMax(max);
        if (max == null) {
            result.setMax(BigDecimal.ZERO);
        }
        if (avg != null) {
            BigDecimal bigDecimal1 = avg.setScale(2, RoundingMode.HALF_UP);
            result.setAverage(bigDecimal1);
        } else {
            result.setAverage(BigDecimal.ZERO);
        }
        return result;
    }

    /**
     * 返回TbmXYVO对象  求平均值的时候保留两位小数
     *
     * @param maxOptional
     * @param avgOptional
     * @return
     */
    private TbmXYVO getTbmXYVO(Optional<BigDecimal> maxOptional, OptionalDouble avgOptional, int ringNum) {
        TbmXYVO result = new TbmXYVO();
        result.setRing(String.valueOf(ringNum));
        if (maxOptional.isPresent()) {
            BigDecimal max = maxOptional.get();
            result.setMax(max);
        } else {
            result.setMax(BigDecimal.ZERO);
        }
        if (avgOptional.isPresent()) {
            double average = avgOptional.getAsDouble();
            BigDecimal bigDecimal = new BigDecimal(average);
            BigDecimal bigDecimal1 = bigDecimal.setScale(2, RoundingMode.HALF_UP);
            result.setAverage(bigDecimal1);
        } else {
            result.setAverage(BigDecimal.ZERO);
        }
        return result;
    }

    /**
     *  根据传入的TbmXYVO列表，返回一个新的TbmXYVO列表，其中每个TbmXYVO的ring属性按照自然顺序排序，不够十环自动补上
     */
    private List<TbmXYVO> handleTbmXYVO(List<TbmXYVO> tbmXYVOList) {
        List<TbmXYVO> result = new ArrayList<>();
        // 使用stream流对tbmXYVOList进行排序，按照ring属性的自然顺序
        List<TbmXYVO> collect = tbmXYVOList.stream()
                .sorted(Comparator.comparingInt(vo -> Integer.parseInt(vo.getRing())))
                .collect(Collectors.toList());
        Optional<TbmXYVO> max = tbmXYVOList.stream().max(Comparator.comparingInt(vo -> Integer.parseInt(vo.getRing())));
        int maxRing = 0;
        int size = tbmXYVOList.size();
        if (max.isPresent()) {
            maxRing = Integer.parseInt(max.get().getRing());
        }
        // 遍历排序后的列表
        for (int i = 0; i < 10; i++) {
            TbmXYVO tbmXYVO = new TbmXYVO();
            if (i < collect.size()) {
                tbmXYVO = collect.get(i);
            } else {
                tbmXYVO.setMax(null);
                tbmXYVO.setAverage(null);
                //   String formattedRing = String.format("%02d", i + 1);
                //如果查询到的数据量不够十环，就自动补录到十环
                tbmXYVO.setRing(String.valueOf(maxRing + i + 1 - size));
            }
            tbmXYVO.setRing(String.format("%02d", Integer.parseInt(tbmXYVO.getRing())));
            result.add(tbmXYVO);
        }
        return result;

    }

    /**
     * 判断有害气体是否超标.如果设备的四项指标都是0的话直接返回不超标
     *
     * @param device        设备阈值
     * @param tbmHarmfulGas 实时数据
     * @return
     */
    private Boolean judgeHarmfulGas(HarmfulGasDevice device, TbmHarmfulGas tbmHarmfulGas) {
        if (device.getCoLevel().compareTo(BigDecimal.ZERO) == 0 &&
                device.getH2sLevel().compareTo(BigDecimal.ZERO) == 0 &&
                device.getO2Level().compareTo(BigDecimal.ZERO) == 0 &&
                device.getCh4Level().compareTo(BigDecimal.ZERO) == 0) {
            return false;
        }
        return tbmHarmfulGas.getCoLevel().compareTo(device.getCoLevel()) >= 0
                || tbmHarmfulGas.getH2sLevel().compareTo(device.getH2sLevel()) >= 0
                || tbmHarmfulGas.getO2Level().compareTo(device.getO2Level()) >= 0
                || tbmHarmfulGas.getCh4Level().compareTo(device.getCh4Level()) >= 0;
    }

    @Override
    public TbmAttitudeVO getAttitude(String deviceCode) {
        TbmAttitudeVO result = new TbmAttitudeVO();
        // 最新的数据
        TbmAttitude lastData = tbmAttitudeService.getLastTbmAttitude(deviceCode);
        if (lastData == null) {
            return result;
        }
        result.setCode(lastData.getCode());
        result.setId(lastData.getId());
        result.setHorizontalFront(formatToTwoDecimalPlaces(lastData.getHorizontalFront()));
        result.setHorizontalEnd(formatToTwoDecimalPlaces(lastData.getHorizontalEnd()));
        result.setVerticalFront(formatToTwoDecimalPlaces(lastData.getVerticalFront()));
        result.setVerticalEnd(formatToTwoDecimalPlaces(lastData.getVerticalEnd()));
        result.setPitchAngle(formatToTwoDecimalPlaces(lastData.getPitchAngle()));
        result.setRollingAngle(formatToTwoDecimalPlaces(lastData.getRollingAngle()));
        result.setDishCoordinateX(lastData.getDishCoordinateX());
        result.setDishCoordinateY(lastData.getDishCoordinateY());
        result.setDishCoordinateZ(lastData.getDishCoordinateZ());
        result.setLongitude(lastData.getLongitude());
        result.setLatitude(lastData.getLatitude());
        // 最近10环 todo 分表
        List<TbmAttitudeRing> tbmAttitudeRings = tbmAttitudeRingService.queryList(Wrappers.<TbmAttitudeRing>lambdaQuery()
                .eq(TbmAttitudeRing::getCode, deviceCode).orderByDesc(TbmAttitudeRing::getMaxTime)
                .last("limit 10"));
        List<Integer> ringNumList = ListUtil.distinctMap(tbmAttitudeRings, TbmAttitudeRing::getRingNum);
        List<TbmAttitudeXYVO> tbmAttitudeXYList = tbmAttitudeService.getRingNumTbmAttitude(deviceCode, ringNumList);
        if (CollectionUtils.isEmpty(tbmAttitudeXYList)) {
            return result;
        }
        // 前点偏差X列表
        List<TbmXYVO> horizontalFrontList = new ArrayList<>();
        // 后点偏差X列表
        List<TbmXYVO> horizontalEndList = new ArrayList<>();
        // 前点偏差Y列表
        List<TbmXYVO> verticalFrontList = new ArrayList<>();
        // 后点偏差Y列表
        List<TbmXYVO> verticalEndList = new ArrayList<>();
        tbmAttitudeXYList.forEach(ringNum -> {
            horizontalFrontList.add(getTbmXYVO(ringNum.getHorizontalFrontMax(), ringNum.getHorizontalFrontAverage(), ringNum.getRing()));
            horizontalEndList.add(getTbmXYVO(ringNum.getHorizontalEndMax(), ringNum.getHorizontalEndAverage(), ringNum.getRing()));
            verticalFrontList.add(getTbmXYVO(ringNum.getVerticalFrontMax(), ringNum.getVerticalFrontAverage(), ringNum.getRing()));
            verticalEndList.add(getTbmXYVO(ringNum.getVerticalEndMax(), ringNum.getVerticalEndAverage(), ringNum.getRing()));
        });

        result.setHorizontalFrontList(handleTbmXYVO(horizontalFrontList));
        result.setHorizontalEndList(handleTbmXYVO(horizontalEndList));
        result.setVerticalFrontList(handleTbmXYVO(verticalFrontList));
        result.setVerticalEndList(handleTbmXYVO(verticalEndList));
        setTbmAttitudeAlarmStatus(deviceCode, result);
        return result;
    }

    @Override
    public TbmEarthSupportTotalVO listSupportSystem(String deviceCode, String position) {
        TbmEarthSupportTotalVO result = new TbmEarthSupportTotalVO();

        DeviceManage deviceManage = deviceManageService.lambdaQuery().eq(DeviceManage::getCode, deviceCode).last("LIMIT 1").one();
        if(deviceManage != null){
            if(StringUtils.endsWith(deviceManage.getType(), "盾构")){
                // 最新的数据 土仓压力
                TbmEarthSupport lastData = tbmEarthSupportService.getLastTbmEarthSupport(deviceCode);
                if (lastData == null) {
                    return result;
                }
                // 最近10环 todo 分表
                List<TbmEarthSupportRing> tbmEarthSupportRings = tbmEarthSupportRingService.queryList(Wrappers.<TbmEarthSupportRing>lambdaQuery()
                        .eq(TbmEarthSupportRing::getCode, deviceCode).orderByDesc(TbmEarthSupportRing::getMaxTime)
                        .last("limit 10"));
                List<Integer> ringNumList = ListUtil.distinctMap(tbmEarthSupportRings, TbmEarthSupportRing::getRingNum);
                List<TbmXYVO> numTbmEarthSupport = tbmEarthSupportService.getRingNumTbmEarthSupport(deviceCode, ringNumList, position);

                List<TbmXYVO> list = new ArrayList<>();
                numTbmEarthSupport.forEach(ringNum -> {
                    list.add(getTbmXYVO(ringNum.getMax(), ringNum.getAverage(), ringNum.getRing()));
                });
                List<TbmXYVO> tbmXYVOS = handleTbmXYVO(list);
                List<TbmEarthSupportVO> tbmEarthSupportVOS = BeanUtil.copyToList(tbmXYVOS, TbmEarthSupportVO.class);
                result.setTbmEarthSupportVOList(tbmEarthSupportVOS);
            }

            if(StringUtils.endsWith(deviceManage.getType(), "TBM")){
                // 最新的数据 支撑系统
                TbmSupportSystem lastData = tbmSupportSystemService.getLastTbmEarthSupport(deviceCode);
                if (lastData == null) {
                    return result;
                }

                List<TbmSupportSystemRing> earthSupportRings = tbmSupportSystemRingService.queryList(Wrappers.<TbmSupportSystemRing>lambdaQuery()
                        .eq(TbmSupportSystemRing::getCode, deviceCode).orderByDesc(TbmSupportSystemRing::getMaxTime)
                        .last("limit 10"));
                List<Integer> ringNumList = ListUtil.distinctMap(earthSupportRings, TbmSupportSystemRing::getRingNum);
                List<TbmXYVO> numTbmEarthSupport = tbmSupportSystemService.getRingNumTbmSupportSystem(deviceCode, ringNumList, position);

                List<TbmXYVO> list = new ArrayList<>();
                numTbmEarthSupport.forEach(ringNum -> {
                    list.add(getTbmXYVO(ringNum.getMax(), ringNum.getAverage(), ringNum.getRing()));
                });
                List<TbmXYVO> tbmXYVOS = handleTbmXYVO(list);
                List<TbmEarthSupportVO> tbmEarthSupportVOS = BeanUtil.copyToList(tbmXYVOS, TbmEarthSupportVO.class);
                result.setTbmEarthSupportVOList(tbmEarthSupportVOS);
            }
        }
        setTbmEarthSupportAlarmStatus(deviceCode, result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importTestData(List<TestDataImportParam> testDataImportParams, String deviceCode) {
        Map<Integer,TbmCutDiskRing> ringNumMap = new HashMap<>();
        int num = 1;
        for(TestDataImportParam testDataImportParam : testDataImportParams) {
            Date date = testDataImportParam.getItem2();
            Integer ringNum = Integer.valueOf(testDataImportParam.getItem3());
            TbmCutDiskRing tbmCutDiskRing = new TbmCutDiskRing();
            tbmCutDiskRing.setCode(deviceCode);
            tbmCutDiskRing.setRingNum(ringNum);
            tbmCutDiskRing.setMaxTime(DateUtils.toLocalDateTime(date));
            if(ringNumMap.containsKey(ringNum)) {
                TbmCutDiskRing tbmCutDiskRing1 = ringNumMap.get(ringNum);
                if(tbmCutDiskRing.getMaxTime().isAfter(tbmCutDiskRing1.getMaxTime())) {
                    ringNumMap.put(ringNum, tbmCutDiskRing);
                }
            } else {
                ringNumMap.put(ringNum, tbmCutDiskRing);
            }
            TbmCutDisk tbmCutDisk = new TbmCutDisk();
            tbmCutDisk.setRingNum(ringNum);
            tbmCutDisk.setCode(deviceCode);
            tbmCutDisk.setCutDiskTorchque(new BigDecimal(testDataImportParam.getItem5()));
            tbmCutDisk.setActualRpm(new BigDecimal(testDataImportParam.getItem4()));
            tbmCutDisk.setPenetration(new BigDecimal(testDataImportParam.getItem6()));
            tbmCutDisk.setCreateTime(new Timestamp(date.getTime()));
            MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
            tbmCutDiskService.save(tbmCutDisk);
            TbmPropulsionSystem tbmPropulsionSystem = new TbmPropulsionSystem();
            tbmPropulsionSystem.setCode(deviceCode);
            tbmPropulsionSystem.setThrust(new BigDecimal(testDataImportParam.getItem7()));
            tbmPropulsionSystem.setPropSpeed(new BigDecimal(testDataImportParam.getItem8()));
            tbmPropulsionSystem.setRingNum(ringNum);
            tbmPropulsionSystem.setCreateTime(new Timestamp(date.getTime()));
            tbmPropulsionSystem.setCylinderPressureOne(new BigDecimal(testDataImportParam.getItem9()));
            tbmPropulsionSystem.setCylinderPressureTwo(new BigDecimal(testDataImportParam.getItem10()));
            tbmPropulsionSystem.setCylinderPressureThree(new BigDecimal(testDataImportParam.getItem11()));
            tbmPropulsionSystem.setCylinderPressureFour(new BigDecimal(testDataImportParam.getItem12()));
            tbmPropulsionSystemService.save(tbmPropulsionSystem);

            TbmDriveSystem tbmDriveSystem = new TbmDriveSystem();
            tbmDriveSystem.setCode(deviceCode);
            tbmDriveSystem.setCreateTime(new Timestamp(date.getTime()));
            tbmDriveSystem.setRingNum(ringNum);
            tbmDriveSystem.setPower(new BigDecimal(testDataImportParam.getItem13()));
            tbmDriveSystem.setElectricCurrentOne(new BigDecimal(testDataImportParam.getItem14()));
            tbmDriveSystem.setElectricCurrentTwo(new BigDecimal(testDataImportParam.getItem15()));
            tbmDriveSystem.setTemperatureOne(new BigDecimal(testDataImportParam.getItem16()));
            tbmDriveSystem.setTemperatureTwo(new BigDecimal(testDataImportParam.getItem17()));
            tbmDriveSystemService.save(tbmDriveSystem);

            TbmTappingSystem tbmTappingSyste = new TbmTappingSystem();
            tbmTappingSyste.setCode(deviceCode);
            tbmTappingSyste.setCreateTime(new Timestamp(date.getTime()));
            tbmTappingSyste.setRingNum(ringNum);
            tbmTappingSyste.setSnailRotationalSpeed(new BigDecimal(testDataImportParam.getItem18()));
            tbmTappingSyste.setSnailTorchque(new BigDecimal(testDataImportParam.getItem19()));
            tbmTappingSyste.setBeltRotationalSpeed(new BigDecimal(testDataImportParam.getItem20()));
            tbmTappingSystemService.save(tbmTappingSyste);

            TbmAttitude tbmAttitude = new TbmAttitude();
            tbmAttitude.setCode(deviceCode);
            tbmAttitude.setCreateTime(new Timestamp(date.getTime()));
            tbmAttitude.setRingNum(ringNum);
            tbmAttitude.setRuningState("掘进中");
            tbmAttitude.setRollingAngle(new BigDecimal(testDataImportParam.getItem21()));
            tbmAttitude.setPitchAngle(new BigDecimal(testDataImportParam.getItem22()));
            tbmAttitude.setHorizontalFront(new BigDecimal(testDataImportParam.getItem23()));
            tbmAttitude.setVerticalFront(new BigDecimal(testDataImportParam.getItem24()));
            tbmAttitude.setHorizontalEnd(new BigDecimal(testDataImportParam.getItem25()));
            tbmAttitude.setVerticalEnd(new BigDecimal(testDataImportParam.getItem26()));
            tbmAttitude.setMileage(BigDecimal.ONE);
            tbmAttitudeService.save(tbmAttitude);

            TbmEarthSupport tbmEarthSupport = new TbmEarthSupport();
            tbmEarthSupport.setCode(deviceCode);
            tbmEarthSupport.setCreateTime(new Timestamp(date.getTime()));
            tbmEarthSupport.setRingNum(ringNum);
            tbmEarthSupport.setPositionOne(new BigDecimal(testDataImportParam.getItem27()));
            tbmEarthSupport.setPositionOne(new BigDecimal(testDataImportParam.getItem28()));
            tbmEarthSupport.setPositionOne(new BigDecimal(testDataImportParam.getItem29()));
            tbmEarthSupport.setPositionOne(new BigDecimal(testDataImportParam.getItem30()));
            tbmEarthSupport.setPositionOne(new BigDecimal(testDataImportParam.getItem31()));
            tbmEarthSupportService.save(tbmEarthSupport);
            MonthTableNameHandler.removeMonthData();
            log.info("新增" + num);
            num++;
        }
        log.info("新增主表结束总数:" + num);
        Collection<TbmCutDiskRing> values = ringNumMap.values();
        if(!CollectionUtils.isEmpty(values)){
            MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
            values.forEach(tbmCutDiskRing -> {
                tbmCutDiskRingService.addBatch(Collections.singletonList(tbmCutDiskRing));
                TbmDriveSystemRing tbmDriveSystemRing = new TbmDriveSystemRing();
                BeanUtil.copyProperties(tbmCutDiskRing,tbmDriveSystemRing);
                tbmDriveSystemRingService.addBatch(Collections.singletonList(tbmDriveSystemRing));
                TbmEarthSupportRing tbmEarthSupportRing = new TbmEarthSupportRing();
                BeanUtil.copyProperties(tbmCutDiskRing,tbmEarthSupportRing);
                tbmEarthSupportRingService.addBatch(Collections.singletonList(tbmEarthSupportRing));
                TbmPropulsionSystemRing tbmPropulsionSystemRing = new TbmPropulsionSystemRing();
                BeanUtil.copyProperties(tbmCutDiskRing,tbmPropulsionSystemRing);
                tbmPropulsionSystemRingService.addBatch(Collections.singletonList(tbmPropulsionSystemRing));
                TbmTappingSystemRing tbmTappingSystemRing = new TbmTappingSystemRing();
                BeanUtil.copyProperties(tbmCutDiskRing,tbmTappingSystemRing);
                tbmTappingSystemRingService.addBatch(Collections.singletonList(tbmTappingSystemRing));
                TbmAttitudeRing tbmAttitudeRing = new TbmAttitudeRing();
                BeanUtil.copyProperties(tbmCutDiskRing,tbmAttitudeRing);
                tbmAttitudeRingService.addBatch(Collections.singletonList(tbmAttitudeRing));
            });
            MonthTableNameHandler.removeMonthData();
        }
        log.info("新增全部结束");
    }

    @Override
    public PageInfo<TbmSystemAlarmVO> page(Long alarmId, String alarmType, Integer pageNo, Integer pageSize) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        PageHelper.startPage(pageNo, pageSize);
        if(StringUtils.equals(alarmType, MyBatisPlusConfig.TBM_CUT_DISK_ALARM)){
            LambdaQueryWrapper<TbmCutDiskAlarmDetails> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.orderByDesc(TbmCutDiskAlarmDetails::getCreateTime);
            queryWrapper.eq(TbmCutDiskAlarmDetails::getAlarmId, alarmId);
            List<TbmCutDiskAlarmDetails> tbmCutDiskAlarmDetailsList = this.tbmCutDiskAlarmDetailsMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(tbmCutDiskAlarmDetailsList)) {
                return new PageInfo<>();
            }

            PageInfo<TbmCutDiskAlarmDetails> info = new PageInfo<>(tbmCutDiskAlarmDetailsList);
            List<TbmCutDiskAlarmDetails> tbmCutDiskAlarmDetails = info.getList();
            List<TbmSystemAlarmVO> tbmSystemAlarmVOS = new ArrayList<>();

            for (TbmCutDiskAlarmDetails cutDiskAlarmDetails : tbmCutDiskAlarmDetails) {
                TbmSystemAlarmVO tbmSystemAlarmVO = BeanUtil.toBean(cutDiskAlarmDetails, TbmSystemAlarmVO.class);
                tbmSystemAlarmVOS.add(tbmSystemAlarmVO);
            }

            PageInfo<TbmSystemAlarmVO> tbmSystemAlarmVOPageInfo = BeanUtil.toBean(info, PageInfo.class);
            tbmSystemAlarmVOPageInfo.setList(tbmSystemAlarmVOS);
            return tbmSystemAlarmVOPageInfo;
        }

        if(StringUtils.equals(alarmType, MyBatisPlusConfig.TBM_DRIVE_SYSTEM_ALARM)){
            LambdaQueryWrapper<TbmDriveSystemAlarmDetails> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.orderByDesc(TbmDriveSystemAlarmDetails::getCreateTime);
            queryWrapper.eq(TbmDriveSystemAlarmDetails::getAlarmId, alarmId);
            List<TbmDriveSystemAlarmDetails> tbmDriveSystemAlarmDetailsList = this.tbmDriveSystemAlarmDetailsMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(tbmDriveSystemAlarmDetailsList)) {
                return new PageInfo<>();
            }

            PageInfo<TbmDriveSystemAlarmDetails> info = new PageInfo<>(tbmDriveSystemAlarmDetailsList);
            List<TbmDriveSystemAlarmDetails> tbmDriveSystemAlarmDetails = info.getList();
            List<TbmSystemAlarmVO> tbmSystemAlarmVOS = new ArrayList<>();
            for (TbmDriveSystemAlarmDetails driveSystemAlarmDetails : tbmDriveSystemAlarmDetails) {
                TbmSystemAlarmVO tbmSystemAlarmVO = BeanUtil.toBean(driveSystemAlarmDetails, TbmSystemAlarmVO.class);
                tbmSystemAlarmVOS.add(tbmSystemAlarmVO);
            }

            PageInfo<TbmSystemAlarmVO> tbmSystemAlarmVOPageInfo = BeanUtil.toBean(info, PageInfo.class);
            tbmSystemAlarmVOPageInfo.setList(tbmSystemAlarmVOS);
            return tbmSystemAlarmVOPageInfo;
        }

        if(StringUtils.equals(alarmType, MyBatisPlusConfig.TBM_EARTH_SUPPORT_ALARM)){
            LambdaQueryWrapper<TbmEarthSupportAlarmDetails> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.orderByDesc(TbmEarthSupportAlarmDetails::getCreateTime);
            queryWrapper.eq(TbmEarthSupportAlarmDetails::getAlarmId, alarmId);
            List<TbmEarthSupportAlarmDetails> tbmEarthSupportAlarmDetailsList = this.tbmEarthSupportAlarmDetailsMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(tbmEarthSupportAlarmDetailsList)) {
                return new PageInfo<>();
            }
            PageInfo<TbmEarthSupportAlarmDetails> info = new PageInfo<>(tbmEarthSupportAlarmDetailsList);
            List<TbmEarthSupportAlarmDetails> tbmEarthSupportAlarmDetails = info.getList();
            List<TbmSystemAlarmVO> tbmSystemAlarmVOS = new ArrayList<>();
            for (TbmEarthSupportAlarmDetails earthSupportAlarmDetails : tbmEarthSupportAlarmDetails) {
                TbmSystemAlarmVO tbmSystemAlarmVO = BeanUtil.toBean(earthSupportAlarmDetails, TbmSystemAlarmVO.class);
                tbmSystemAlarmVOS.add(tbmSystemAlarmVO);
            }
            PageInfo<TbmSystemAlarmVO> tbmSystemAlarmVOPageInfo = BeanUtil.toBean(info, PageInfo.class);
            tbmSystemAlarmVOPageInfo.setList(tbmSystemAlarmVOS);
            return tbmSystemAlarmVOPageInfo;
        }

        if(StringUtils.equals(alarmType, MyBatisPlusConfig.TBM_ATTITUDE_ALARM)){
            LambdaQueryWrapper<TbmAttitudeAlarmDetails> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.orderByDesc(TbmAttitudeAlarmDetails::getCreateTime);
            queryWrapper.eq(TbmAttitudeAlarmDetails::getAlarmId, alarmId);
            List<TbmAttitudeAlarmDetails> tbmAttitudeAlarmDetailsList = this.tbmAttitudeAlarmDetailsMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(tbmAttitudeAlarmDetailsList)) {
                return new PageInfo<>();
            }
            PageInfo<TbmAttitudeAlarmDetails> info = new PageInfo<>(tbmAttitudeAlarmDetailsList);
            List<TbmAttitudeAlarmDetails> tbmAttitudeAlarmDetails = info.getList();
            List<TbmSystemAlarmVO> tbmSystemAlarmVOS = new ArrayList<>();
            for (TbmAttitudeAlarmDetails attitudeAlarmDetails : tbmAttitudeAlarmDetails) {
                TbmSystemAlarmVO tbmSystemAlarmVO = BeanUtil.toBean(attitudeAlarmDetails, TbmSystemAlarmVO.class);
                tbmSystemAlarmVOS.add(tbmSystemAlarmVO);
            }
            PageInfo<TbmSystemAlarmVO> tbmSystemAlarmVOPageInfo = BeanUtil.toBean(info, PageInfo.class);
            tbmSystemAlarmVOPageInfo.setList(tbmSystemAlarmVOS);
            return tbmSystemAlarmVOPageInfo;
        }

        if(StringUtils.equals(alarmType, MyBatisPlusConfig.TBM_PROPULSION_SYSTEM_ALARM)){
            LambdaQueryWrapper<TbmPropulsionSystemAlarmDetails> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.orderByDesc(TbmPropulsionSystemAlarmDetails::getCreateTime);
            queryWrapper.eq(TbmPropulsionSystemAlarmDetails::getAlarmId, alarmId);
            List<TbmPropulsionSystemAlarmDetails> tbmEquipmentAlarmDetailsList = this.tbmPropulsionSystemAlarmDetailsMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(tbmEquipmentAlarmDetailsList)) {
                return new PageInfo<>();
            }
            PageInfo<TbmPropulsionSystemAlarmDetails> info = new PageInfo<>(tbmEquipmentAlarmDetailsList);
            List<TbmPropulsionSystemAlarmDetails> tbmPropulsionSystemAlarmDetails = info.getList();
            List<TbmSystemAlarmVO> tbmSystemAlarmVOS = new ArrayList<>();
            for (TbmPropulsionSystemAlarmDetails propulsionSystemAlarmDetails : tbmPropulsionSystemAlarmDetails) {
                TbmSystemAlarmVO tbmSystemAlarmVO = BeanUtil.toBean(propulsionSystemAlarmDetails, TbmSystemAlarmVO.class);
                tbmSystemAlarmVOS.add(tbmSystemAlarmVO);
            }
            PageInfo<TbmSystemAlarmVO> tbmSystemAlarmVOPageInfo = BeanUtil.toBean(info, PageInfo.class);
            tbmSystemAlarmVOPageInfo.setList(tbmSystemAlarmVOS);
            return tbmSystemAlarmVOPageInfo;
        }

        if(StringUtils.equals(alarmType, MyBatisPlusConfig.TBM_SUPPORT_SYSTEM_ALARM)){
            LambdaQueryWrapper<TbmSupportSystemAlarmDetails> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.orderByDesc(TbmSupportSystemAlarmDetails::getCreateTime);
            queryWrapper.eq(TbmSupportSystemAlarmDetails::getAlarmId, alarmId);
            List<TbmSupportSystemAlarmDetails> tbmEquipmentAlarmDetailsList = this.tbmSupportSystemAlarmDetailsMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(tbmEquipmentAlarmDetailsList)) {
                return new PageInfo<>();
            }
            PageInfo<TbmSupportSystemAlarmDetails> info = new PageInfo<>(tbmEquipmentAlarmDetailsList);
            List<TbmSupportSystemAlarmDetails> tbmSupportSystemAlarmDetails = info.getList();
            List<TbmSystemAlarmVO> tbmSystemAlarmVOS = new ArrayList<>();
            for (TbmSupportSystemAlarmDetails supportSystemAlarmDetails : tbmSupportSystemAlarmDetails) {
                TbmSystemAlarmVO tbmSystemAlarmVO = BeanUtil.toBean(supportSystemAlarmDetails, TbmSystemAlarmVO.class);
                tbmSystemAlarmVOS.add(tbmSystemAlarmVO);
            }
            PageInfo<TbmSystemAlarmVO> tbmSystemAlarmVOPageInfo = BeanUtil.toBean(info, PageInfo.class);
            tbmSystemAlarmVOPageInfo.setList(tbmSystemAlarmVOS);
            return tbmSystemAlarmVOPageInfo;
        }

        if(StringUtils.equals(alarmType, MyBatisPlusConfig.TBM_TAPPING_SYSTEM_ALARM)){
            LambdaQueryWrapper<TbmTappingSystemAlarmDetails> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.orderByDesc(TbmTappingSystemAlarmDetails::getCreateTime);
            queryWrapper.eq(TbmTappingSystemAlarmDetails::getAlarmId, alarmId);
            List<TbmTappingSystemAlarmDetails> tbmEquipmentAlarmDetailsList = this.tbmTappingSystemAlarmDetailsMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(tbmEquipmentAlarmDetailsList)) {
                return new PageInfo<>();
            }
            PageInfo<TbmTappingSystemAlarmDetails> info = new PageInfo<>(tbmEquipmentAlarmDetailsList);
            List<TbmTappingSystemAlarmDetails> tbmTappingSystemAlarmDetails = info.getList();
            List<TbmSystemAlarmVO> tbmSystemAlarmVOS = new ArrayList<>();
            for (TbmTappingSystemAlarmDetails tappingSystemAlarmDetails : tbmTappingSystemAlarmDetails) {
                TbmSystemAlarmVO tbmSystemAlarmVO = BeanUtil.toBean(tappingSystemAlarmDetails, TbmSystemAlarmVO.class);
                tbmSystemAlarmVOS.add(tbmSystemAlarmVO);
            }
            PageInfo<TbmSystemAlarmVO> tbmSystemAlarmVOPageInfo = BeanUtil.toBean(info, PageInfo.class);
            tbmSystemAlarmVOPageInfo.setList(tbmSystemAlarmVOS);
            return tbmSystemAlarmVOPageInfo;
        }
        MonthTableNameHandler.removeMonthData();
        return new PageInfo<>();
    }

    @Override
    public TunnelingProgressVO getTunnelingProgress(String deviceCode) {
        TunnelingProgressVO result = new TunnelingProgressVO();
        // 设备基础信息
        DeviceManage deviceManage = deviceManageService.lambdaQuery().eq(DeviceManage::getCode, deviceCode).last("LIMIT 1").one();
        result.setTotalMileage(deviceManage.getTotalMileage());
        result.setTotalRingNum(deviceManage.getRingNum());

        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        // 累计里程
        TbmAttitude tbmAttitude = tbmAttitudeService.lambdaQuery().eq(TbmAttitude::getCode, deviceCode).orderByDesc(TbmAttitude::getCreateTime).last("LIMIT 1").one();
        MonthTableNameHandler.removeMonthData();
        if(tbmAttitude == null){
            result.setCumulativeMileage("0");
            result.setCumulativeRingNum("0");
            result.setCumulativeMileageRatio("0.00%");
            result.setCumulativeRingNumRatio("0.00%");
            return result;
        }

        //累计里程的计算公式为：累计里程减去 14.950；这里如果累计里程不大于14.950都显示0
        result.setCumulativeMileage(String.valueOf(getCumulativeMileage(tbmAttitude.getMileage())));
        //这里的累计安装环数 返回最新环号的上一个环号
        List<TbmAttitudeRing> tbmAttitudeRings = tbmAttitudeRingService.queryList(Wrappers.<TbmAttitudeRing>lambdaQuery()
                .eq(TbmAttitudeRing::getCode, deviceCode).orderByDesc(TbmAttitudeRing::getMaxTime)
                .last("limit 10"));
        if(CollectionUtils.isEmpty(tbmAttitudeRings)){
            result.setCumulativeRingNum("0");
            result.setCumulativeRingNumRatio("0.00%");
        }else{
            //取第二个数据
            TbmAttitudeRing tbmAttitudeRing = null;
            if(tbmAttitudeRings.size() >= 2){
                tbmAttitudeRing = tbmAttitudeRings.get(1);
            }else {
                tbmAttitudeRing = tbmAttitudeRings.get(0);
            }
            String takePositiveNumbers = takePositiveNumbers(tbmAttitudeRing.getRingNum());
            result.setCumulativeRingNum(takePositiveNumbers);
            result.setCumulativeRingNumRatio(calculateThePercentage(deviceManage.getRingNum(), takePositiveNumbers));
        }
        result.setCumulativeMileageRatio(calculateThePercentage(deviceManage.getTotalMileage(), String.valueOf(tbmAttitude.getMileage())));
        return result;
    }

    /**
     * 累计里程的计算公式为：累计里程减去 14.950；这里如果累计里程不大于14.950都显示0
     *
     * @param mileage
     * @return
     */
    private BigDecimal getCumulativeMileage(BigDecimal mileage){
        if(mileage == null){
            return new BigDecimal("0");
        }

        if(mileage.compareTo(new BigDecimal("14.950")) <= 0){
            return new BigDecimal("0");
        }

        return mileage.subtract(new BigDecimal("14.950"));
    }

    /**
     * 取正数
     *
     * @param value
     * @return
     */
    private String takePositiveNumbers(Integer value) {
        if(value == null){
            return null;
        }

        if (value < 0) {
            // 取反操作
            value = Math.abs(value);
        }

        return String.valueOf(value);
    }

    /**
     * 计算百分比
     *
     * @param total
     * @param current
     * @return
     */
    private String calculateThePercentage(String total, String current){
        if(StringUtils.isBlank(total) || StringUtils.isBlank(current)){
            return "0%";
        }

        BigDecimal totalBigDecimal = new BigDecimal(total);
        BigDecimal currentBigDecimal = new BigDecimal(current);
        if(totalBigDecimal.compareTo(BigDecimal.ZERO) == 0){
            return "0%";
        }

        //如果currentBigDecimal大于totalBigDecimal，返回100%
        if(currentBigDecimal.compareTo(totalBigDecimal) > 0){
            return "100%";
        }

        BigDecimal result = currentBigDecimal.divide(totalBigDecimal, 6, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
        return result.toEngineeringString() + "%";
    }

    /**
     * 格式化 BigDecimal 为保留两位小数的字符串，不足两位小数补0。
     *
     * @param value 要格式化的 BigDecimal 值
     * @return 格式化后的字符串
     */
    public static String formatToTwoDecimalPlaces(BigDecimal value) {
        if (value == null) {
            value = BigDecimal.ZERO;
        }

        // 使用 setScale 保留两位小数，并选择舍入模式（如 RoundingMode.HALF_UP）
        BigDecimal scaledValue = value.setScale(2, RoundingMode.HALF_UP);

        // 使用 DecimalFormat 格式化输出，确保小数点后始终有两位
        DecimalFormat df = new DecimalFormat("0.00");

        return df.format(scaledValue);
    }
}
