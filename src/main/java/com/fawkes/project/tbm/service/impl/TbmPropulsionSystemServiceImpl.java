package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.constants.IOTDeviceConstants;
import com.fawkes.project.tbm.common.handler.MonthTableNameHandler;
import com.fawkes.project.tbm.common.mapper.TbmPropulsionSystemMapper;
import com.fawkes.project.tbm.common.model.TbmPropulsionSystem;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.common.utils.MyEntityTool;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.common.vo.TbmPropulsionSystemXYVO;
import com.fawkes.project.tbm.common.vo.TbmXYVO;
import com.fawkes.project.tbm.service.TbmPropulsionSystemService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * TBM推进系统参数(此表为原始表，数据按月分表) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Service
public class TbmPropulsionSystemServiceImpl extends ServiceImpl<TbmPropulsionSystemMapper, TbmPropulsionSystem> implements TbmPropulsionSystemService {

    @Resource
    private TbmPropulsionSystemMapper tbmPropulsionSystemMapper;

    @Override
    public TbmPropulsionSystem getLastTbmPropulsionSystem(String deviceCode) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        List<TbmPropulsionSystem> tbmDriveSystems = this.lambdaQuery()
                .eq(StringUtils.isNotBlank(deviceCode),TbmPropulsionSystem::getCode, deviceCode)
                .orderByDesc(TbmPropulsionSystem::getCreateTime).last("LIMIT  1").list();
        MonthTableNameHandler.removeMonthData();
        if (CollectionUtils.isNotEmpty(tbmDriveSystems)) {
            return tbmDriveSystems.get(0);
        }
        return null;

    }

    @Override
    public List<TbmPropulsionSystem> getLastTenRingNumTbmPropulsionSystem(String deviceCode, Integer ringNum) {
        Integer minRingNum = 0;
        if (ringNum - 9 > 0) {
            minRingNum = ringNum - 9;
        }
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        List<TbmPropulsionSystem> tbmDriveSystems = this.lambdaQuery()
                .eq(StringUtils.isNotBlank(deviceCode),TbmPropulsionSystem::getCode, deviceCode)
                .between(TbmPropulsionSystem::getRingNum, minRingNum, ringNum).list();
        MonthTableNameHandler.removeMonthData();
        return tbmDriveSystems;
    }


    /**
     * 获取最后十个环号对应的 TbmPropulsionSystem 对象
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     * @param cylinderPressureStr 油箱压力
     * @return
     */
    @Override
    public List<TbmPropulsionSystemXYVO> getRingNumTbmPropulsionSystem(String deviceCode, List<Integer> ringNumList, String cylinderPressureStr) {
        String tableName = "tbm_propulsion_system_" + DateUtils.getYyyyMM();
        return tbmPropulsionSystemMapper.getRingNumTbmPropulsionSystem(deviceCode,ringNumList,cylinderPressureStr,tableName);
    }

    @Override
    public Boolean syncIotDeviceData(List<IOTResponse> iotResponseList, String deviceCode) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        String thrust = null;
        String propSpeed = null;
        String oilTemp = null;
        String ringNum = null;
        Optional<String> optionalS = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.THRUST)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalS.isPresent()) {
            thrust = optionalS.get();
        }

        Optional<String> optionalT = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.PROP_SPEED)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalT.isPresent()) {
            propSpeed = optionalT.get();
        }

        String cylinderPressureA = null;
        Optional<String> optionalA = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.CYLINDER_PRESSURE_A)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalA.isPresent()) {
            cylinderPressureA = optionalA.get();
        }

        String cylinderPressureB = null;
        Optional<String> optionalB = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.CYLINDER_PRESSURE_B)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalB.isPresent()) {
            cylinderPressureB = optionalB.get();
        }

        String cylinderPressureC = null;
        Optional<String> optionalC = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.CYLINDER_PRESSURE_C)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalC.isPresent()) {
            cylinderPressureC = optionalC.get();
        }

        String cylinderPressureD = null;
        Optional<String> optionalD = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.CYLINDER_PRESSURE_D)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalD.isPresent()) {
            cylinderPressureD = optionalD.get();
        }

        String cylinderPressureE = null;
        Optional<String> optionalE = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.CYLINDER_PRESSURE_E)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalE.isPresent()) {
            cylinderPressureE = optionalE.get();
        }

        String cylinderPressureF = null;
        Optional<String> optionalF = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.CYLINDER_PRESSURE_F)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalF.isPresent()) {
            cylinderPressureF = optionalF.get();
        }

        Optional<String> optionalO = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.OIL_TEMP)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalO.isPresent()) {
            oilTemp = optionalO.get();
        }

        Optional<String> optionalR = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.RING_NUM)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalR.isPresent()) {
            ringNum = optionalR.get();
        }

        TbmPropulsionSystem tbmPropulsionSystem = new TbmPropulsionSystem();
        tbmPropulsionSystem.setCode(deviceCode);
        if (thrust != null) {
            tbmPropulsionSystem.setThrust(new BigDecimal(thrust));
        }
        if (propSpeed != null) {
            tbmPropulsionSystem.setPropSpeed(new BigDecimal(propSpeed));
        }

        if (oilTemp != null) {
            tbmPropulsionSystem.setOilTemp(new BigDecimal(oilTemp));
        }
        if (ringNum != null) {
            tbmPropulsionSystem.setRingNum(Integer.valueOf(ringNum));
        }

        if (cylinderPressureA != null) {
            tbmPropulsionSystem.setCylinderPressureOne(new BigDecimal(cylinderPressureA));
        }

        if (cylinderPressureB != null) {
            tbmPropulsionSystem.setCylinderPressureTwo(new BigDecimal(cylinderPressureB));
        }

        if (cylinderPressureC != null) {
            tbmPropulsionSystem.setCylinderPressureThree(new BigDecimal(cylinderPressureC));
        }

        if (cylinderPressureD != null) {
            tbmPropulsionSystem.setCylinderPressureFour(new BigDecimal(cylinderPressureD));
        }

        if (cylinderPressureE != null) {
            tbmPropulsionSystem.setCylinderPressureFive(new BigDecimal(cylinderPressureE));
        }

        if (cylinderPressureF != null) {
            tbmPropulsionSystem.setCylinderPressureSix(new BigDecimal(cylinderPressureF));
        }
        tbmPropulsionSystem.setCreateTime(new Timestamp(System.currentTimeMillis()));
        tbmPropulsionSystem.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        int insert = tbmPropulsionSystemMapper.insert(tbmPropulsionSystem);
        MonthTableNameHandler.removeMonthData();
        return insert > 0;
    }
}
