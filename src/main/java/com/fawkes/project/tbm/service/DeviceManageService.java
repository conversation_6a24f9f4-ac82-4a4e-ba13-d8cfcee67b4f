package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.DeviceManage;
import com.fawkes.project.tbm.common.param.DeviceManageParam;
import com.fawkes.project.tbm.common.vo.DeviceManageVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 设备管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
public interface DeviceManageService extends IService<DeviceManage> {

    List<DeviceManageVO> list(String code, String name);

    void add(DeviceManageParam param);

    void update(DeviceManageParam param);

    boolean delete(String id);

    DeviceManageVO get(String id);

    /**
     * 设备code查询code-区段
     * @param codeList 区段
     * @return (code,区段)
     */
     Map<String,String> getCodeSectionMap(List<String> codeList);

}
