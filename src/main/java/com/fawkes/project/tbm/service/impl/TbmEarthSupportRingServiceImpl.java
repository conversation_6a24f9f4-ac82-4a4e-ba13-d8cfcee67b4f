package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.mapper.TbmEarthSupportRingMapper;
import com.fawkes.project.tbm.common.model.TbmEarthSupportRing;
import com.fawkes.project.tbm.service.TbmEarthSupportRingService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version TbmEarthSupportRingService 2025/6/11 9:34
 */
@Service
public class TbmEarthSupportRingServiceImpl
        extends ServiceImpl<TbmEarthSupportRingMapper, TbmEarthSupportRing>
        implements TbmEarthSupportRingService {
    /**
     * 多条件查询
     */
    @Override
    public List<TbmEarthSupportRing> queryList(LambdaQueryWrapper<TbmEarthSupportRing> wrapper) {
        return this.list(wrapper);
    }

    /**
     * 批量新增
     */
    @Override
    public Boolean addBatch(List<TbmEarthSupportRing> list) {
        return this.saveBatch(list);
    }

    /**
     * 条件修改
     */
    @Override
    public Boolean edit(LambdaUpdateWrapper<TbmEarthSupportRing> wrapper) {
        return this.update(wrapper);
    }
}
