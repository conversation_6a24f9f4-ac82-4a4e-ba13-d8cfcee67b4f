package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.SafeRuning;
import com.fawkes.project.tbm.common.param.SafeRuningParam;
import com.fawkes.project.tbm.common.vo.SafeRuningVO;

/**
 * <p>
 * 安全运行配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
public interface SafeRuningService extends IService<SafeRuning> {

    void set(SafeRuningParam param);

    SafeRuningVO get();
}
