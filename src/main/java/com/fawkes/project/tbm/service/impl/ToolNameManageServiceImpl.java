package com.fawkes.project.tbm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.project.tbm.common.mapper.ToolNameManageMapper;
import com.fawkes.project.tbm.common.model.ToolNameManage;
import com.fawkes.project.tbm.common.utils.ListUtil;
import com.fawkes.project.tbm.common.vo.ToolNameManageVO;
import com.fawkes.project.tbm.service.ToolNameManageService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ToolNameManageServiceImpl extends ServiceImpl<ToolNameManageMapper, ToolNameManage> implements ToolNameManageService {


    @Override
    public List<ToolNameManageVO> listToolNames(String section) {
        List<ToolNameManage> list = super.list(new LambdaQueryWrapper<>(ToolNameManage.class)
                .eq(ToolNameManage::getSection, section)
                .eq(ToolNameManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .orderByDesc(ToolNameManage::getCreateDate));
        return BeanUtil.copyToList(list, ToolNameManageVO.class);

    }

    /**
     * 根据id获取map
     *
     * @param toolNameManageId 刀具id
     * @return <id,name>
     */
    @Override
    public Map<Long, String> getToolNameMap(List<Long> toolNameManageId) {
        if (ListUtil.isEmpty(toolNameManageId)) {
            return new HashMap<>(1);
        }
        List<ToolNameManage> list = super.list(new LambdaQueryWrapper<>(ToolNameManage.class)
                .in(ToolNameManage::getId, toolNameManageId)
                .eq(ToolNameManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .select(ToolNameManage::getId, ToolNameManage::getTooName));
        return list.stream().collect(Collectors.toMap(ToolNameManage::getId, ToolNameManage::getTooName));
    }
}
