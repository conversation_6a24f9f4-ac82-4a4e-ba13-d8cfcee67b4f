package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.constants.IOTDeviceConstants;
import com.fawkes.project.tbm.common.constants.TbmPositionConstants;
import com.fawkes.project.tbm.common.handler.MonthTableNameHandler;
import com.fawkes.project.tbm.common.mapper.TbmSupportSystemMapper;
import com.fawkes.project.tbm.common.model.TbmSupportSystem;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.common.utils.StringUtils;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.common.vo.TbmXYVO;
import com.fawkes.project.tbm.service.TbmSupportSystemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * TBM支撑系统参数表(此表为原始表，数据按月分表) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Service
public class TbmSupportSystemServiceImpl extends ServiceImpl<TbmSupportSystemMapper, TbmSupportSystem> implements TbmSupportSystemService {

    @Resource
    private TbmSupportSystemMapper tbmSupportSystemMapper;

    @Override
    public TbmSupportSystem getLastTbmEarthSupport(String deviceCode) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        TbmSupportSystem tbmSupportSystem = this.lambdaQuery().eq(TbmSupportSystem::getCode, deviceCode)
                .orderByDesc(TbmSupportSystem::getCreateTime).last("LIMIT  1").one();
        MonthTableNameHandler.removeMonthData();
        return tbmSupportSystem;
    }

    @Override
    public List<TbmXYVO> getRingNumTbmSupportSystem(String deviceCode, List<Integer> ringNumList, String position) {
        String tableName = "tbm_support_system_" + DateUtils.getYyyyMM();
        //处理position 和 撑靴压力字段的对应关系
        if(StringUtils.equals(position, TbmPositionConstants.POSITION_ONE)){
            position = IOTDeviceConstants.UP_BRACE_PRESSURE1;
        }

        else if(StringUtils.equals(position, TbmPositionConstants.POSITION_TWO)){
            position = IOTDeviceConstants.UP_BRACE_PRESSURE2;
        }

        else if(StringUtils.equals(position, TbmPositionConstants.POSITION_THREE)){
            position = IOTDeviceConstants.UP_BRACE_PRESSURE3;
        }

        else if(StringUtils.equals(position, TbmPositionConstants.POSITION_FOUE)){
            position = IOTDeviceConstants.UP_BRACE_PRESSURE4;
        }

        else if(StringUtils.equals(position, TbmPositionConstants.POSITION_FIVE)){
            position = IOTDeviceConstants.UP_BRACE_PRESSURE5;
        }

        else if(StringUtils.equals(position, TbmPositionConstants.POSITION_SIX)){
            position = IOTDeviceConstants.UP_BRACE_PRESSURE6;
        }

        else {
            position = IOTDeviceConstants.UP_BRACE_PRESSURE1;
        }
        return tbmSupportSystemMapper.getRingNumTbmSupportSystem(deviceCode, ringNumList, position, tableName);
    }

    @Override
    public Boolean syncTbmSupportSystem(List<IOTResponse> iotResponseList, String deviceCode) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        String upBracePressure1 = null;
        String upBracePressure2 = null;
        String upBracePressure3 = null;
        String upBracePressure4 = null;
        String upBracePressure5 = null;
        String upBracePressure6 = null;
        Optional<String> optionalS = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty()
                .equals(IOTDeviceConstants.UP_BRACE_PRESSURE1)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalS.isPresent()) {
            upBracePressure1 = optionalS.get();
        }

        Optional<String> optionalA = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty()
                .equals(IOTDeviceConstants.UP_BRACE_PRESSURE2)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalA.isPresent()) {
            upBracePressure2 = optionalA.get();
        }

        Optional<String> optionalP = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty()
                .equals(IOTDeviceConstants.UP_BRACE_PRESSURE3)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalP.isPresent()) {
            upBracePressure3 = optionalP.get();
        }

        Optional<String> optionalK = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty()
                .equals(IOTDeviceConstants.UP_BRACE_PRESSURE4)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalK.isPresent()) {
            upBracePressure4 = optionalK.get();
        }

        Optional<String> optionalM = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty()
                .equals(IOTDeviceConstants.UP_BRACE_PRESSURE5)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalM.isPresent()) {
            upBracePressure5 = optionalM.get();
        }

        Optional<String> optionalN = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty()
                .equals(IOTDeviceConstants.UP_BRACE_PRESSURE6)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalN.isPresent()) {
            upBracePressure6 = optionalN.get();
        }

        Integer ringNum = null;
        Optional<String> optionalR = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty()
                .equals(IOTDeviceConstants.RING_NUM)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalR.isPresent()) {
            ringNum = Integer.valueOf(optionalR.get());
        }

        TbmSupportSystem tbmSupportSystem = new TbmSupportSystem();
        tbmSupportSystem.setCode(deviceCode);
        tbmSupportSystem.setUpBracePressure1(Optional.ofNullable(upBracePressure1).map(BigDecimal::new).orElse(null));
        tbmSupportSystem.setUpBracePressure2(Optional.ofNullable(upBracePressure2).map(BigDecimal::new).orElse(null));
        tbmSupportSystem.setUpBracePressure3(Optional.ofNullable(upBracePressure3).map(BigDecimal::new).orElse(null));
        tbmSupportSystem.setUpBracePressure4(Optional.ofNullable(upBracePressure4).map(BigDecimal::new).orElse(null));
        tbmSupportSystem.setUpBracePressure5(Optional.ofNullable(upBracePressure5).map(BigDecimal::new).orElse(null));
        tbmSupportSystem.setUpBracePressure6(Optional.ofNullable(upBracePressure6).map(BigDecimal::new).orElse(null));
        tbmSupportSystem.setCreateTime(new Timestamp(System.currentTimeMillis()));
        tbmSupportSystem.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        tbmSupportSystem.setRingNum(ringNum);
        int insert = tbmSupportSystemMapper.insert(tbmSupportSystem);
        MonthTableNameHandler.removeMonthData();
        return insert > 0;
    }
}
