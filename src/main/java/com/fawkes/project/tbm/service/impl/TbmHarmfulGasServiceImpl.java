package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.constants.IOTDeviceConstants;
import com.fawkes.project.tbm.common.handler.MonthTableNameHandler;
import com.fawkes.project.tbm.common.mapper.TbmHarmfulGasMapper;
import com.fawkes.project.tbm.common.model.TbmHarmfulGas;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.common.utils.MyEntityTool;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.service.TbmHarmfulGasService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * TBM有害气体参数表(此表为原始表，数据按月分表) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Service
public class TbmHarmfulGasServiceImpl extends ServiceImpl<TbmHarmfulGasMapper, TbmHarmfulGas> implements TbmHarmfulGasService {
    @Resource
    private TbmHarmfulGasMapper tbmHarmfulGasMapper;

    @Override
    public List<TbmHarmfulGas> selectByCodeInLastOne(List<String> codes) {
        String tableName = "tbm_harmful_gas_" + DateUtils.getYyyyMM();
        return tbmHarmfulGasMapper.selectByCodeInLastOne(codes, tableName);
    }

    @Override
    public Boolean syncIotDeviceData(List<IOTResponse> iotResponseList, String deviceCode) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        String ch4Level = null;
        String h2sLevel = null;
        String o2Level = null;
        String coLevel = null;
        Optional<String> optionalS = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.CH4_LEVEL)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalS.isPresent()) {
            ch4Level = optionalS.get();
        }

        Optional<String> optionalT = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.H2S_LEVEL)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalT.isPresent()) {
            h2sLevel = optionalT.get();
        }

        Optional<String> optionalL = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.O2_LEVEL)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalL.isPresent()) {
            o2Level = optionalL.get();
        }

        Optional<String> optionalC = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty().equals(IOTDeviceConstants.CO_LEVEL)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalC.isPresent()) {
            coLevel = optionalC.get();
        }

        TbmHarmfulGas tbmHarmfulGas = new TbmHarmfulGas();
        tbmHarmfulGas.setCode(deviceCode);
        if (ch4Level != null) {
            tbmHarmfulGas.setCh4Level(new BigDecimal(ch4Level));
        }
        if (h2sLevel != null) {
            tbmHarmfulGas.setH2sLevel(new BigDecimal(h2sLevel));
        }
        if (o2Level != null) {
            tbmHarmfulGas.setO2Level(new BigDecimal(o2Level));
        }
        if (coLevel != null) {
            tbmHarmfulGas.setCoLevel(new BigDecimal(coLevel));
        }

        tbmHarmfulGas.setCreateTime(new Timestamp(System.currentTimeMillis()));
        tbmHarmfulGas.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        int insert = tbmHarmfulGasMapper.insert(tbmHarmfulGas);
        MonthTableNameHandler.removeMonthData();
        return insert > 0;
    }
}
