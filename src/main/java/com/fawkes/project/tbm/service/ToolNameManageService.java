package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.ToolNameManage;
import com.fawkes.project.tbm.common.vo.ToolNameManageVO;

import java.util.List;
import java.util.Map;

public interface ToolNameManageService extends IService<ToolNameManage> {


    List<ToolNameManageVO> listToolNames(String section);

    /**
     * 根据id获取map
     * @param toolNameManageId 刀具id
     * @return <id,name>
     */
    Map<Long, String> getToolNameMap(List<Long> toolNameManageId);

}
