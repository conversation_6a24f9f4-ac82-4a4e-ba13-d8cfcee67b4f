package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.mapper.TbmPropulsionSystemRingMapper;
import com.fawkes.project.tbm.common.model.TbmPropulsionSystemRing;
import com.fawkes.project.tbm.service.TbmPropulsionSystemRingService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version TbmPropulsionSystemRingService 2025/6/11 9:35
 */
@Service
public class TbmPropulsionSystemRingServiceImpl
        extends ServiceImpl<TbmPropulsionSystemRingMapper, TbmPropulsionSystemRing>
        implements TbmPropulsionSystemRingService {
    /**
     * 多条件查询
     */
    @Override
    public List<TbmPropulsionSystemRing> queryList(LambdaQueryWrapper<TbmPropulsionSystemRing> wrapper) {
        return this.list(wrapper);
    }

    /**
     * 批量新增
     */
    @Override
    public Boolean addBatch(List<TbmPropulsionSystemRing> list) {
        return this.saveBatch(list);
    }

    /**
     * 条件修改
     */
    @Override
    public Boolean edit(LambdaUpdateWrapper<TbmPropulsionSystemRing> wrapper) {
        return this.update(wrapper);
    }
}
