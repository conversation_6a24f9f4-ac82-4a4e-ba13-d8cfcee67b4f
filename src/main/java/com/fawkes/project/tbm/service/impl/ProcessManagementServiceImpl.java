package com.fawkes.project.tbm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.core.exception.BusinessException;
import com.fawkes.project.tbm.common.enums.DataSourceEnums;
import com.fawkes.project.tbm.common.enums.ProcessCodeNameEnums;
import com.fawkes.project.tbm.common.mapper.DeviceManageMapper;
import com.fawkes.project.tbm.common.mapper.ProcessManagementMapper;
import com.fawkes.project.tbm.common.model.DeviceManage;
import com.fawkes.project.tbm.common.model.ProcessManagement;
import com.fawkes.project.tbm.common.param.ConstructionSituationDescriptionParam;
import com.fawkes.project.tbm.common.param.ProcessManagementImportResult;
import com.fawkes.project.tbm.common.param.ProcessManagementParam;
import com.fawkes.project.tbm.common.query.ProcessManagementExportQuery;
import com.fawkes.project.tbm.common.tools.verify.ObjectValidator;
import com.fawkes.project.tbm.common.tools.verify.config.VerifyConfig;
import com.fawkes.project.tbm.common.tools.verify.domain.RowVerifyError;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.common.utils.ListUtil;
import com.fawkes.project.tbm.common.utils.MyEntityTool;
import com.fawkes.project.tbm.common.utils.TencentCloudApiTools;
import com.fawkes.project.tbm.common.vo.*;
import com.fawkes.project.tbm.common.vo.app.ProcessManagementAppVO;
import com.fawkes.project.tbm.service.IProcessManagementService;
import com.fawkes.project.tbm.service.IOperationLogService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/6/3 18:02
 */
@Service
@Slf4j
public class ProcessManagementServiceImpl extends ServiceImpl<ProcessManagementMapper, ProcessManagement> implements IProcessManagementService {

    @Resource
    private ProcessManagementMapper processManagementMapper;

    @Resource
    private TencentCloudApiTools tencentCloudApiTools;

    @Resource
    private DeviceManageMapper deviceManageMapper;

    /**
     * 对象验证器
     */
    @Resource
    private ObjectValidator objectValidator;

    @Resource
    private IOperationLogService operationLogService;

    /**
     * 整数正则（只能是正整数、0）
     */
    private static final String REGEXP_INTEGER = "^[0-9]*$";

    /**
     * 整数正则（包含正整数和负整数、0）
     */
    private static final String REGEXP_INTEGER_ALL = "^(0|[+\\-]?[1-9][0-9]*)$";

    @Override
    public PageInfo page(String processName, List<String> sectionList, String minRingNum, String maxRingNum, String createName, String createStartDate, String createEndDate, Integer pageNo, Integer pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        LambdaQueryWrapper<ProcessManagement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProcessManagement::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag());
        if (StringUtils.isNotEmpty(processName)) {
            queryWrapper.like(ProcessManagement::getProcessName, processName);
        }

        if (CollectionUtils.isNotEmpty(sectionList)) {
            queryWrapper.in(ProcessManagement::getSection, sectionList);
        }

        if (StringUtils.isNotEmpty(minRingNum)) {
            queryWrapper.ge(ProcessManagement::getRingNum, Integer.valueOf(minRingNum));
        }

        if (StringUtils.isNotEmpty(maxRingNum)) {
            queryWrapper.le(ProcessManagement::getRingNum, Integer.valueOf(maxRingNum));
        }

        if (StringUtils.isNotEmpty(createName)) {
            queryWrapper.like(ProcessManagement::getUpdateName, createName);
        }

        if (StringUtils.isNotEmpty(createStartDate)) {
            queryWrapper.gt(ProcessManagement::getUpdateDate, createStartDate);
        }

        if (StringUtils.isNotEmpty(createEndDate)) {
            queryWrapper.lt(ProcessManagement::getUpdateDate, createEndDate);
        }
        queryWrapper.orderByDesc(ProcessManagement::getUpdateDate);

        List<ProcessManagement> processManagementList = this.list(queryWrapper);

        if (CollectionUtils.isEmpty(processManagementList)) {
            return new PageInfo<>();
        }

        PageInfo<ProcessManagement> info = new PageInfo<>(processManagementList);
        List<ProcessManagement> managementList = info.getList();
        List<ProcessManagementVO> processManagementVOS = new ArrayList<>();

        for (ProcessManagement processManagement : managementList) {
            ProcessManagementVO processManagementVO = BeanUtil.toBean(processManagement, ProcessManagementVO.class);
            processManagementVO.setUpdateName(processManagement.getUpdateName());
            //区段名称，需要根据设备code查询
            LambdaQueryWrapper<DeviceManage> manageLambdaQueryWrapper = new LambdaQueryWrapper<>();
            manageLambdaQueryWrapper.eq(DeviceManage::getCode, processManagement.getSection());
            manageLambdaQueryWrapper.eq(DeviceManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag());
            DeviceManage deviceManage = deviceManageMapper.selectOne(manageLambdaQueryWrapper);
            processManagementVO.setSection(deviceManage == null ? "" : deviceManage.getSection());
            processManagementVOS.add(processManagementVO);
        }

        PageInfo<ProcessManagementVO> managementVOPageInfo = BeanUtil.toBean(info, PageInfo.class);
        managementVOPageInfo.setList(processManagementVOS);
        return managementVOPageInfo;
    }

    /**
     * 判断开仓换刀数据，三个任何一个为空就提示错误
     *
     * @param processManagementParam
     * @return
     */
    private Boolean getOpenWarehouseChangeToolResult(ProcessManagementParam processManagementParam){
        Integer openWarehouseChangeToolNum = processManagementParam.getOpenWarehouseChangeToolNum();
        Timestamp openWarehouseChangeToolStartTime = processManagementParam.getOpenWarehouseChangeToolStartTime();
        Timestamp openWarehouseChangeToolEndTime = processManagementParam.getOpenWarehouseChangeToolEndTime();

        //3个都为空返回true
        if(openWarehouseChangeToolNum == null && openWarehouseChangeToolStartTime == null && openWarehouseChangeToolEndTime == null){
            return true;
        }

        //任何1个为空返回false
        if(openWarehouseChangeToolNum == null || openWarehouseChangeToolStartTime == null || openWarehouseChangeToolEndTime == null){
            return false;
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(ProcessManagementParam processManagementParam) throws IllegalAccessException {
        //参数校验
        String section = processManagementParam.getSection();
        processManagementParamVerify(processManagementParam, section);

        //工序名称拼接，按照填写的工序判断
        String s = getProcessNameStr(processManagementParam);

        processManagementParam.setProcessName(s);
        ProcessManagement processManagement = BeanUtil.toBean(processManagementParam, ProcessManagement.class);
        Long id = processManagement.getId();
        if(id == null){
            //新增
            MyEntityTool.insertEntityWithNameSilent(processManagement);
            boolean save = this.save(processManagement);
            if (save) {
                // 记录新增操作
                operationLogService.recordProcessManagementAddOperation(processManagement, processManagementParam.getDataSource());
            }
            return save;
        }

        //编辑
        ProcessManagement oldProcessManagement = this.getById(id);
        MyEntityTool.updateEntityWithNameSilent(processManagement);
        boolean update = this.updateById(processManagement);
        if (update) {
            // 记录修改操作
            operationLogService.recordProcessManagementUpdateOperation(oldProcessManagement, processManagement, processManagementParam.getDataSource());
        }
        return update;
    }

    /**
     * 工序参数校验
     * 
     * @param processManagementParam 
     * @param section
     */
    private void processManagementParamVerify(ProcessManagementParam processManagementParam, String section) {
        if (StringUtils.isBlank(section)) {
            throw new BusinessException("区段不能为空");
        }else {
            //判断区段在不在数据库中
            LambdaQueryWrapper<DeviceManage> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DeviceManage::getCode, section);
            queryWrapper.eq(DeviceManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag());
            List<DeviceManage> deviceManageList = deviceManageMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(deviceManageList)) {
                throw new BusinessException("区段不存在");
            }
        }

        Integer ringNum = processManagementParam.getRingNum();
        if (ringNum == null || !String.valueOf(ringNum).matches(REGEXP_INTEGER_ALL) || String.valueOf(ringNum).length() > 8) {
            throw new BusinessException("环号数据格式不正确");
        }

        //小数位不超过3位，整数位不超过5位数
        BigDecimal assemblyPoint = processManagementParam.getAssemblyPoint();
        if (assemblyPoint == null || !getRegexpIntegerDecimalMatchResult(assemblyPoint.toPlainString(), 5, 3)) {
            throw new BusinessException("拼装点位数据格式不正确");
        }

        Timestamp tunnelingStartTime = processManagementParam.getTunnelingStartTime();
        Timestamp tunnelingEndTime = processManagementParam.getTunnelingEndTime();
        if (!getTimeResult(tunnelingStartTime, tunnelingEndTime)) {
            throw new BusinessException("掘进开始时间或结束时间不正确，时间格式为yyyy-MM-dd HH:mm");
        }

        Timestamp changeStepsStartTime = processManagementParam.getChangeStepsStartTime();
        Timestamp changeStepsEndTime = processManagementParam.getChangeStepsEndTime();
        if (!getTimeResult(changeStepsStartTime, changeStepsEndTime)) {
            throw new BusinessException("换步开始时间或结束时间不正确，时间格式为yyyy-MM-dd HH:mm");
        }

        Timestamp segmentAssemblyStartTime = processManagementParam.getSegmentAssemblyStartTime();
        Timestamp segmentAssemblyEndTime = processManagementParam.getSegmentAssemblyEndTime();
        if (!getTimeResult(segmentAssemblyStartTime, segmentAssemblyEndTime)) {
            throw new BusinessException("管片拼装开始时间或结束时间不正确，时间格式为yyyy-MM-dd HH:mm");
        }

        Timestamp othersJobStartTime = processManagementParam.getOthersJobStartTime();
        Timestamp othersJobEndTime = processManagementParam.getOthersJobEndTime();
        if (!getTimeResult(othersJobStartTime, othersJobEndTime)) {
            throw new BusinessException("其它工作开始时间或结束时间不正确，时间格式为yyyy-MM-dd HH:mm");
        }

        Timestamp commonMaintenanceAndRepairStartTime = processManagementParam.getCommonMaintenanceAndRepairStartTime();
        Timestamp commonMaintenanceAndRepairEndTime = processManagementParam.getCommonMaintenanceAndRepairEndTime();
        if (!getTimeResult(commonMaintenanceAndRepairStartTime, commonMaintenanceAndRepairEndTime)) {
            throw new BusinessException("维修保养-常规保养开始时间或结束时间不正确，时间格式为yyyy-MM-dd HH:mm");
        }

        Timestamp shutdownMaintenanceAndRepairStartTime = processManagementParam.getShutdownMaintenanceAndRepairStartTime();
        Timestamp shutdownMaintenanceAndRepairEndTime = processManagementParam.getShutdownMaintenanceAndRepairEndTime();
        if (!getTimeResult(shutdownMaintenanceAndRepairStartTime, shutdownMaintenanceAndRepairEndTime)) {
            throw new BusinessException("维修保养-故障停机开始时间或结束时间不正确，时间格式为yyyy-MM-dd HH:mm");
        }

        //判断开仓换刀时间、第几仓数据，三个只要一个为空，就提示错误
        Boolean openWarehouseChangeToolResult = getOpenWarehouseChangeToolResult(processManagementParam);
        if(!openWarehouseChangeToolResult){
            throw new BusinessException("开仓换刀数据内容不正确，时间格式为yyyy-MM-dd HH:mm");
        }else {
            Timestamp openWarehouseChangeToolStartTime = processManagementParam.getOpenWarehouseChangeToolStartTime();
            Timestamp openWarehouseChangeToolEndTime = processManagementParam.getOpenWarehouseChangeToolEndTime();
            if (!getTimeResult(openWarehouseChangeToolStartTime, openWarehouseChangeToolEndTime)) {
                throw new BusinessException("开仓换刀时间参数错误");
            }

            Integer openWarehouseChangeToolNum = processManagementParam.getOpenWarehouseChangeToolNum();
            if (openWarehouseChangeToolNum != null) {
                if (!String.valueOf(openWarehouseChangeToolNum).matches(REGEXP_INTEGER) || String.valueOf(openWarehouseChangeToolNum).length() > 5) {
                    throw new BusinessException("开仓换刀-第几仓参数错误");
                }
            }
        }

        String descriptionOfAbnormalSituations = processManagementParam.getDescriptionOfAbnormalSituations();
        if (StringUtils.isNotBlank(descriptionOfAbnormalSituations) && descriptionOfAbnormalSituations.length() > 500) {
            throw new BusinessException("异常情况说明不能超过500个字");
        }

        //施工情况描述 --- 参见 ConstructionSituationDescriptionParam 结构
        String descriptionOfConstructionSituation = processManagementParam.getDescriptionOfConstructionSituation();
        if (StringUtils.isNotBlank(descriptionOfConstructionSituation)) {
            ConstructionSituationDescriptionParam constructionSituationDescriptionParam = getConstructionSituationDescriptionParam(descriptionOfConstructionSituation);
            if(constructionSituationDescriptionParam == null){
                throw new BusinessException("施工情况描述数据格式不正确");
            }
            String constructionWasteSoilProperties = constructionSituationDescriptionParam.getConstructionWasteSoilProperties();
            if (StringUtils.isNotBlank(constructionWasteSoilProperties) && constructionWasteSoilProperties.length() > 500) {
                throw new BusinessException("施工情况描述-渣土性状不能超过500个字");
            }

            String residueDischargeVolume = constructionSituationDescriptionParam.getResidueDischargeVolume();
            if (StringUtils.isNotBlank(residueDischargeVolume)) {
                if (!getRegexpIntegerDecimalMatchResult(residueDischargeVolume, 5, 3)) {
                    throw new BusinessException("施工情况描述-出渣量（m³）数据格式不正确");
                }
            }

            String slopeOfTheSegment = constructionSituationDescriptionParam.getSlopeOfTheSegment();
            if (StringUtils.isNotBlank(slopeOfTheSegment) && slopeOfTheSegment.length() > 500) {
                throw new BusinessException("施工情况描述-管片坡度不能超过500个字");
            }

            String useOfFoamingAgent = constructionSituationDescriptionParam.getUseOfFoamingAgent();
            if (StringUtils.isNotBlank(useOfFoamingAgent)) {
                if (!getRegexpIntegerDecimalMatchResult(useOfFoamingAgent, 5, 3)) {
                    throw new BusinessException("施工情况描述-泡沫剂使用（KG）数据格式不正确");
                }
            }

            String greaseUsage = constructionSituationDescriptionParam.getGreaseUsage();
            if (StringUtils.isNotBlank(greaseUsage)) {
                if (!getRegexpIntegerDecimalMatchResult(greaseUsage, 5, 3)) {
                    throw new BusinessException("施工情况描述-油脂使用（KG）数据格式不正确");
                }
            }

            String bentonite = constructionSituationDescriptionParam.getBentonite();
            if (StringUtils.isNotBlank(bentonite) && bentonite.length() > 500) {
                throw new BusinessException("施工情况描述-膨润土不能超过500个字");
            }

            String others = constructionSituationDescriptionParam.getOthers();
            if (StringUtils.isNotBlank(others) && others.length() > 500) {
                throw new BusinessException("施工情况描述-其他不能超过500个字");
            }
        }

        String remark = processManagementParam.getRemark();
        if (StringUtils.isNotBlank(remark) && remark.length() > 200) {
            throw new BusinessException("备注不能超过200个字");
        }
    }

    /**
     * 获取工序名称
     * 
     * @param processManagementParam 
     * @return
     */
    private String getProcessNameStr(ProcessManagementParam processManagementParam) {
        StringBuilder stringBuilder = new StringBuilder();
        if (processManagementParam.getTunnelingStartTime() != null && processManagementParam.getTunnelingEndTime() != null) {
            stringBuilder.append(ProcessCodeNameEnums.TUNNELING.getName()).append(",");
        }

        if (processManagementParam.getChangeStepsStartTime() != null && processManagementParam.getChangeStepsEndTime() != null) {
            stringBuilder.append(ProcessCodeNameEnums.CHANGE_STEPS.getName()).append(",");
        }

        if (processManagementParam.getSegmentAssemblyStartTime() != null && processManagementParam.getSegmentAssemblyEndTime() != null) {
            stringBuilder.append(ProcessCodeNameEnums.SEGMENT_ASSEMBLY.getName()).append(",");
        }

        if (processManagementParam.getOpenWarehouseChangeToolStartTime() != null && processManagementParam.getOpenWarehouseChangeToolEndTime() != null) {
            stringBuilder.append(ProcessCodeNameEnums.OPEN_WAREHOUSE_CHANGE_TOOL.getName()).append(",");
        }

        if (processManagementParam.getCommonMaintenanceAndRepairStartTime() != null && processManagementParam.getCommonMaintenanceAndRepairEndTime() != null) {
            stringBuilder.append(ProcessCodeNameEnums.COMMON_MAINTENANCE_AND_REPAIR.getName()).append(",");
        }

        if (processManagementParam.getShutdownMaintenanceAndRepairStartTime() != null && processManagementParam.getShutdownMaintenanceAndRepairEndTime() != null) {
            stringBuilder.append(ProcessCodeNameEnums.SHUTDOWN_MAINTENANCE_AND_REPAIR.getName()).append(",");
        }

        if (processManagementParam.getOthersJobStartTime() != null && processManagementParam.getOthersJobEndTime() != null) {
            stringBuilder.append(ProcessCodeNameEnums.OTHERS_JOB.getName()).append(",");
        }
        String s = stringBuilder.toString();
        if(s.contains(",")){
            s = s.substring(0, s.lastIndexOf(","));
        }
        return s;
    }

    /**
     * json  转实体
     *
     * @param descriptionOfConstructionSituation
     * @return
     */
    private ConstructionSituationDescriptionParam getConstructionSituationDescriptionParam(String descriptionOfConstructionSituation) {
        try {
            ConstructionSituationDescriptionParam constructionSituationDescriptionParam = JSONObject.parseObject(descriptionOfConstructionSituation, ConstructionSituationDescriptionParam.class);
            return constructionSituationDescriptionParam;
        } catch (Exception e) {
            log.error("getConstructionSituationDescriptionParam error: {}", e);
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public ProcessManagementVO get(String id) {
        ProcessManagement processManagement = this.getById(Long.valueOf(id));
        return BeanUtil.copyProperties(processManagement, ProcessManagementVO.class);
    }

    @Override
    public ProcessManagementVO ocrByToken(String token) {
        return tencentCloudApiTools.getOcrResult(token);
    }

    @Override
    public OcrProcessManagementVO ocrByFile(MultipartFile file) {
        //判断文件格式，只能是pdf+图片
        boolean pdf = isPdf(file);
        boolean image = isImage(file);
        if (!pdf && !image) {
            throw new BusinessException("请上传pdf或者图片文件");
        }
        return tencentCloudApiTools.getOcrResult(file);
    }

    /**
     * 判断是否是pdf
     *
     * @param file
     * @return
     */
    public boolean isPdf(MultipartFile file) {
        if(file == null || file.isEmpty()){
            return false;
        }
        String contentType = file.getContentType();
        return "application/pdf".equals(contentType);
    }

    /**
     * 判断是否是图片
     *
     * @param file
     * @return
     */
    public boolean isImage(MultipartFile file) {
        if(file == null || file.isEmpty()){
            return false;
        }
        String contentType = file.getContentType();
        return contentType.startsWith("image/");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(String id, Integer dataSource) throws IllegalAccessException {
        List<ProcessManagement> processManagementList = this.listByIds(Arrays.asList(Long.valueOf(id)));
        for (ProcessManagement processManagement : processManagementList) {
            processManagement.setDeleteFlag(DeleteFlagEnum.DATA_WARN.getFlag());
            MyEntityTool.updateEntityWithNameSilent(processManagement);
        }

        boolean updateBatchById = this.updateBatchById(processManagementList);
        if (updateBatchById) {
            // 记录删除操作
            for (ProcessManagement processManagement : processManagementList) {
                operationLogService.recordProcessManagementDeleteOperation(processManagement, dataSource);
            }
        }
        return updateBatchById;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessManagementImportResult importExcel(MultipartFile file, HttpServletResponse response) throws IOException, IllegalAccessException {
        InputStream inputStream = file.getInputStream();
        List<ProcessManagementAddImportVO> excelList = EasyExcel.read(inputStream).head(ProcessManagementAddImportVO.class).sheet(0).doReadSync();
        if (CollectionUtils.isEmpty(excelList)) {
            throw new BusinessException("excel文件读取错误！请检查文件");
        }

        ListUtil.forI(excelList, (index, vo) -> vo.setSerialNumber(index + 1));

        VerifyConfig<ProcessManagementAddImportVO> verifyConfig = ProcessManagementAddImportVO.getVerifyConfig();
        List<RowVerifyError<ProcessManagementAddImportVO>> verifyErrorList = this.objectValidator.verifyImportList(excelList, verifyConfig);
        excelList = RowVerifyError.filterErrorData(excelList, verifyErrorList);
        if (ListUtil.isEmpty(excelList)) {
            return new ProcessManagementImportResult(verifyErrorList);
        }

        for (ProcessManagementAddImportVO processManagementAddImportVO : excelList) {
            String section = processManagementAddImportVO.getSection();
            List<String> errorMsgList = getErrorMsgList(processManagementAddImportVO, section);

            if(CollectionUtils.isNotEmpty(errorMsgList)){
                RowVerifyError<ProcessManagementAddImportVO> verifyError = new RowVerifyError<>();
                verifyError.setRowNo(processManagementAddImportVO.getSerialNumber());
                verifyError.setErrorObject(processManagementAddImportVO);
                verifyError.setErrorMsgList(errorMsgList);
                verifyErrorList.add(verifyError);
            }
        }

        excelList = RowVerifyError.filterErrorData(excelList, verifyErrorList);
        if (ListUtil.isEmpty(excelList)) {
            return new ProcessManagementImportResult(verifyErrorList);
        }

        ProcessManagementImportResult result = new ProcessManagementImportResult();
        result.setErrorList(verifyErrorList);
        List<ProcessManagementVO> processManagementVOList = BeanUtil.copyToList(excelList, ProcessManagementVO.class);
        result.setRight(processManagementVOList);

        if (CollectionUtils.isNotEmpty(result.getError())) {
            return result;
        }

        //数据入库
        List<ProcessManagement> processManagementList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(excelList)){
            for (ProcessManagementAddImportVO processManagementAddImportVO : excelList){
                String descriptionOfConstructionSituation = getDescriptionOfConstructionSituation(processManagementAddImportVO);
                ProcessManagement processManagement = BeanUtil.toBean(processManagementAddImportVO, ProcessManagement.class);
                if(StringUtils.isNotBlank(descriptionOfConstructionSituation)){
                    processManagement.setDescriptionOfConstructionSituation(descriptionOfConstructionSituation);
                }

                //设置区段code
                processManagement.setSection(getSectionCode(processManagementAddImportVO.getSection()));
                processManagement.setProcessName(getProcessNameStr(processManagementAddImportVO));
                MyEntityTool.insertEntityWithNameSilent(processManagement);
                processManagementList.add(processManagement);
            }
        }
        boolean saveBatch = this.saveBatch(processManagementList);
        log.info("function=insertEntityWithNameSilent, saveBatch:{}", saveBatch);

        if (saveBatch) {
            for (ProcessManagement processManagement : processManagementList) {
                // 记录新增操作
                operationLogService.recordProcessManagementAddOperation(processManagement, DataSourceEnums.PC.getCode());
            }
        }
        return result;
    }

    /**
     * 获取工序名称
     *
     * @param processManagementAddImportVO
     * @return
     */
    private String getProcessNameStr(ProcessManagementAddImportVO processManagementAddImportVO) {
        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(processManagementAddImportVO.getTunnelingStartTime()) && StringUtils.isNotBlank(processManagementAddImportVO.getTunnelingEndTime())) {
            stringBuilder.append(ProcessCodeNameEnums.TUNNELING.getName()).append(",");
        }

        if (StringUtils.isNotBlank(processManagementAddImportVO.getChangeStepsStartTime()) && StringUtils.isNotBlank(processManagementAddImportVO.getChangeStepsEndTime())) {
            stringBuilder.append(ProcessCodeNameEnums.CHANGE_STEPS.getName()).append(",");
        }

        if (StringUtils.isNotBlank(processManagementAddImportVO.getSegmentAssemblyStartTime()) && StringUtils.isNotBlank(processManagementAddImportVO.getSegmentAssemblyEndTime())) {
            stringBuilder.append(ProcessCodeNameEnums.SEGMENT_ASSEMBLY.getName()).append(",");
        }

        if (StringUtils.isNotBlank(processManagementAddImportVO.getOpenWarehouseChangeToolStartTime()) && StringUtils.isNotBlank(processManagementAddImportVO.getOpenWarehouseChangeToolEndTime())) {
            stringBuilder.append(ProcessCodeNameEnums.OPEN_WAREHOUSE_CHANGE_TOOL.getName()).append(",");
        }

        if (StringUtils.isNotBlank(processManagementAddImportVO.getCommonMaintenanceAndRepairStartTime()) && StringUtils.isNotBlank(processManagementAddImportVO.getCommonMaintenanceAndRepairEndTime())) {
            stringBuilder.append(ProcessCodeNameEnums.COMMON_MAINTENANCE_AND_REPAIR.getName()).append(",");
        }

        if (StringUtils.isNotBlank(processManagementAddImportVO.getShutdownMaintenanceAndRepairStartTime()) && StringUtils.isNotBlank(processManagementAddImportVO.getShutdownMaintenanceAndRepairEndTime())) {
            stringBuilder.append(ProcessCodeNameEnums.SHUTDOWN_MAINTENANCE_AND_REPAIR.getName()).append(",");
        }

        if (StringUtils.isNotBlank(processManagementAddImportVO.getOthersJobStartTime()) && StringUtils.isNotBlank(processManagementAddImportVO.getOthersJobEndTime())) {
            stringBuilder.append(ProcessCodeNameEnums.OTHERS_JOB.getName()).append(",");
        }
        String s = stringBuilder.toString();
        //去掉最后一个,
        if(s.contains(",")){
            s = s.substring(0, s.lastIndexOf(","));
        }
        return s;
    }

    /**
     * 根据区段名称获取区段code
     *
     * @param section
     * @return
     */
    private String getSectionCode(String section){
        LambdaQueryWrapper<DeviceManage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceManage::getSection, section);
        queryWrapper.eq(DeviceManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag());
        List<DeviceManage> deviceManageList = deviceManageMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(deviceManageList)) {
            throw new BusinessException("区段不存在");
        }

        return deviceManageList.get(0).getCode();
    }

    /**
     * 构造施工情况数据
     *
     * @param processManagementAddImportVO
     * @return
     */
    private String getDescriptionOfConstructionSituation(ProcessManagementAddImportVO processManagementAddImportVO){
        String bentonite = processManagementAddImportVO.getBentonite();
        String slopeOfTheSegment = processManagementAddImportVO.getSlopeOfTheSegment();
        String greaseUsage = processManagementAddImportVO.getGreaseUsage();
        String useOfFoamingAgent = processManagementAddImportVO.getUseOfFoamingAgent();
        String residueDischargeVolume = processManagementAddImportVO.getResidueDischargeVolume();
        String constructionWasteSoilProperties = processManagementAddImportVO.getConstructionWasteSoilProperties();
        String others = processManagementAddImportVO.getOthers();
        boolean allEmpty = StringUtils.isEmpty(bentonite) && StringUtils.isEmpty(slopeOfTheSegment) && StringUtils.isEmpty(greaseUsage) &&
                StringUtils.isEmpty(useOfFoamingAgent) && StringUtils.isEmpty(residueDischargeVolume) && StringUtils.isEmpty(constructionWasteSoilProperties) && StringUtils.isEmpty(others);
        if(!allEmpty){
            ConstructionSituationDescriptionParam constructionSituationDescriptionParam = new ConstructionSituationDescriptionParam();
            constructionSituationDescriptionParam.setBentonite(bentonite);
            constructionSituationDescriptionParam.setConstructionWasteSoilProperties(constructionWasteSoilProperties);
            constructionSituationDescriptionParam.setGreaseUsage(greaseUsage);
            constructionSituationDescriptionParam.setResidueDischargeVolume(residueDischargeVolume);
            constructionSituationDescriptionParam.setSlopeOfTheSegment(slopeOfTheSegment);
            constructionSituationDescriptionParam.setUseOfFoamingAgent(useOfFoamingAgent);
            constructionSituationDescriptionParam.setOthers(others);
            return JSONObject.toJSONString(constructionSituationDescriptionParam);
        }

        return null;
    }

    /**
     * 校验导入参数
     * 
     * @param processManagementAddImportVO 
     * @param section
     * @return
     */
    private List<String> getErrorMsgList(ProcessManagementAddImportVO processManagementAddImportVO, String section) {
        List<String> errorMsgList = new ArrayList<>();
        if (StringUtils.isBlank(section)) {
            errorMsgList.add("区段不能为空");
        }else{
            //判断区段在不在数据库中
            LambdaQueryWrapper<DeviceManage> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DeviceManage::getSection, section);
            queryWrapper.eq(DeviceManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag());
            List<DeviceManage> deviceManageList = deviceManageMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(deviceManageList)) {
                errorMsgList.add("区段不存在");
            }
        }

        String ringNum = processManagementAddImportVO.getRingNum();
        if(StringUtils.isBlank(ringNum)){
            errorMsgList.add("环号不能为空");
        }else {
            if (!ringNum.matches(REGEXP_INTEGER_ALL) || ringNum.length() > 8) {
                errorMsgList.add("环号数据格式不正确");
            }
        }

        //小数位不超过3位，整数位不超过5位数
        String assemblyPoint = processManagementAddImportVO.getAssemblyPoint();
        if(StringUtils.isBlank(assemblyPoint)){
            errorMsgList.add("拼装点位不能为空");
        }else {
            if (!getRegexpIntegerDecimalMatchResult(assemblyPoint, 5, 3)) {
                errorMsgList.add("拼装点位数据格式不正确");
            }
        }

        String tunnelingStartTime = processManagementAddImportVO.getTunnelingStartTime();
        String tunnelingEndTime = processManagementAddImportVO.getTunnelingEndTime();
        if(!getTimeBlankResult(tunnelingStartTime, tunnelingEndTime)){
            errorMsgList.add("掘进开始时间和结束时间必须全不填或者全填");
        }else {
            String timeResult = getTimeResult(tunnelingStartTime, tunnelingEndTime, "掘进");
            if (!StringUtils.isEmpty(timeResult)) {
                errorMsgList.add(timeResult);
            }
        }

        String changeStepsStartTime = processManagementAddImportVO.getChangeStepsStartTime();
        String changeStepsEndTime = processManagementAddImportVO.getChangeStepsEndTime();
        if(!getTimeBlankResult(changeStepsStartTime, changeStepsEndTime)){
            errorMsgList.add("换步开始时间和结束时间必须全不填或者全填");
        }else {
            String timeResult = getTimeResult(changeStepsStartTime, changeStepsEndTime, "换步");
            if (!StringUtils.isEmpty(timeResult)) {
                errorMsgList.add(timeResult);
            }
        }

        String segmentAssemblyStartTime = processManagementAddImportVO.getSegmentAssemblyStartTime();
        String segmentAssemblyEndTime = processManagementAddImportVO.getSegmentAssemblyEndTime();
        if(!getTimeBlankResult(segmentAssemblyStartTime, segmentAssemblyEndTime)){
            errorMsgList.add("管片拼装开始时间和结束时间必须全不填或者全填");
        }else {
            String timeResult = getTimeResult(segmentAssemblyStartTime, segmentAssemblyEndTime, "管片拼装");
            if (!StringUtils.isEmpty(timeResult)) {
                errorMsgList.add(timeResult);
            }
        }

        String othersJobStartTime = processManagementAddImportVO.getOthersJobStartTime();
        String othersJobEndTime = processManagementAddImportVO.getOthersJobEndTime();
        if(!getTimeBlankResult(othersJobStartTime, othersJobEndTime)){
            errorMsgList.add("其它工作开始时间和结束时间必须全不填或者全填");
        }else {
            String timeResult = getTimeResult(othersJobStartTime, othersJobEndTime, "其它工作");
            if (!StringUtils.isEmpty(timeResult)) {
                errorMsgList.add(timeResult);
            }
        }

        String commonMaintenanceAndRepairStartTime = processManagementAddImportVO.getCommonMaintenanceAndRepairStartTime();
        String commonMaintenanceAndRepairEndTime = processManagementAddImportVO.getCommonMaintenanceAndRepairEndTime();
        if(!getTimeBlankResult(commonMaintenanceAndRepairStartTime, commonMaintenanceAndRepairEndTime)){
            errorMsgList.add("维修保养-常规保养开始时间和结束时间必须全不填或者全填");
        }else {
            String timeResult = getTimeResult(commonMaintenanceAndRepairStartTime, commonMaintenanceAndRepairEndTime, "维修保养-常规保养");
            if (!StringUtils.isEmpty(timeResult)) {
                errorMsgList.add(timeResult);
            }
        }

        String shutdownMaintenanceAndRepairStartTime = processManagementAddImportVO.getShutdownMaintenanceAndRepairStartTime();
        String shutdownMaintenanceAndRepairEndTime = processManagementAddImportVO.getShutdownMaintenanceAndRepairEndTime();
        if(!getTimeBlankResult(shutdownMaintenanceAndRepairStartTime, shutdownMaintenanceAndRepairEndTime)){
            errorMsgList.add("维修保养-故障停机开始时间和结束时间必须全不填或者全填");
        }else {
            String timeResult = getTimeResult(shutdownMaintenanceAndRepairStartTime, shutdownMaintenanceAndRepairEndTime, "维修保养-故障停机");
            if (!StringUtils.isEmpty(timeResult)) {
                errorMsgList.add(timeResult);
            }
        }

        //判断开仓换刀时间、第几仓数据，三个只要一个为空，就提示错误
        Boolean openWarehouseChangeToolResult = getOpenWarehouseChangeToolResult(processManagementAddImportVO);
        if(!openWarehouseChangeToolResult){
            errorMsgList.add("开仓换刀第几仓、开仓换刀开始时间或结束时间必须全不填或者全填");
        }else {
            String openWarehouseChangeToolStartTime = processManagementAddImportVO.getOpenWarehouseChangeToolStartTime();
            String openWarehouseChangeToolEndTime = processManagementAddImportVO.getOpenWarehouseChangeToolEndTime();
            String timeResult = getTimeResult(openWarehouseChangeToolStartTime, openWarehouseChangeToolEndTime, "开仓换刀");
            if (!StringUtils.isEmpty(timeResult)) {
                errorMsgList.add(timeResult);
            }

            String openWarehouseChangeToolNum = processManagementAddImportVO.getOpenWarehouseChangeToolNum();
            if (StringUtils.isNotBlank(openWarehouseChangeToolNum)) {
                if (!openWarehouseChangeToolNum.matches(REGEXP_INTEGER) || openWarehouseChangeToolNum.length() > 5) {
                    errorMsgList.add("第几仓数据格式不正确");
                }
            }
        }

        String descriptionOfAbnormalSituations = processManagementAddImportVO.getDescriptionOfAbnormalSituations();
        if (StringUtils.isNotBlank(descriptionOfAbnormalSituations) && descriptionOfAbnormalSituations.length() > 500) {
            errorMsgList.add("异常情况说明不能超过500个字");
        }

        String constructionWasteSoilProperties = processManagementAddImportVO.getConstructionWasteSoilProperties();
        if (StringUtils.isNotBlank(constructionWasteSoilProperties) && constructionWasteSoilProperties.length() > 500) {
            errorMsgList.add("渣土性状不能超过500个字");
        }

        String residueDischargeVolume = processManagementAddImportVO.getResidueDischargeVolume();
        if (StringUtils.isNotBlank(residueDischargeVolume)) {
            if (!getRegexpIntegerDecimalMatchResult(residueDischargeVolume, 5, 3)) {
                errorMsgList.add("出渣量（m³）数据格式不正确");
            }
        }

        String slopeOfTheSegment = processManagementAddImportVO.getSlopeOfTheSegment();
        if (StringUtils.isNotBlank(slopeOfTheSegment) && slopeOfTheSegment.length() > 500) {
            errorMsgList.add("管片坡度不能超过500个字");
        }

        String useOfFoamingAgent = processManagementAddImportVO.getUseOfFoamingAgent();
        if (StringUtils.isNotBlank(useOfFoamingAgent)) {
            if (!getRegexpIntegerDecimalMatchResult(useOfFoamingAgent, 5, 3)) {
                errorMsgList.add("泡沫剂使用（KG）数据格式不正确");
            }
        }

        String greaseUsage = processManagementAddImportVO.getGreaseUsage();
        if (StringUtils.isNotBlank(greaseUsage)) {
            if (!getRegexpIntegerDecimalMatchResult(greaseUsage, 5, 3)) {
                errorMsgList.add("油脂使用（KG）数据格式不正确");
            }
        }

        String bentonite = processManagementAddImportVO.getBentonite();
        if (StringUtils.isNotBlank(bentonite) && bentonite.length() > 500) {
            errorMsgList.add("膨润土不能超过500个字");
        }

        String others = processManagementAddImportVO.getOthers();
        if (StringUtils.isNotBlank(others) && others.length() > 500) {
            errorMsgList.add("其他不能超过500个字");
        }

        String remark = processManagementAddImportVO.getRemark();
        if (StringUtils.isNotBlank(remark) && remark.length() > 200) {
            errorMsgList.add("备注不能超过500个字");
        }
        return errorMsgList;
    }

    /**
     * 判断开仓换刀数据，三个任何一个为空就提示错误
     *
     * @param processManagementAddImportVO
     * @return
     */
    private Boolean getOpenWarehouseChangeToolResult(ProcessManagementAddImportVO processManagementAddImportVO){
        String openWarehouseChangeToolNum = processManagementAddImportVO.getOpenWarehouseChangeToolNum();
        String openWarehouseChangeToolStartTime = processManagementAddImportVO.getOpenWarehouseChangeToolStartTime();
        String openWarehouseChangeToolEndTime = processManagementAddImportVO.getOpenWarehouseChangeToolEndTime();

        //3个都为空返回true
        if(StringUtils.isBlank(openWarehouseChangeToolNum) && StringUtils.isBlank(openWarehouseChangeToolStartTime) && StringUtils.isBlank(openWarehouseChangeToolEndTime)){
            return true;
        }

        //任何1个为空返回false
        if(StringUtils.isBlank(openWarehouseChangeToolNum) || StringUtils.isBlank(openWarehouseChangeToolStartTime) || StringUtils.isBlank(openWarehouseChangeToolEndTime)){
            return false;
        }

        return true;
    }

    @Override
    public void export(ProcessManagementExportQuery query, HttpServletResponse response) throws IOException {
        List<Long> exportIdList = query.getExportIdList();
        if (CollectionUtils.isEmpty(exportIdList)) {
            throw new BusinessException("请选择导出数据");
        }

        List<ProcessManagement> processManagements = this.lambdaQuery().in(ProcessManagement::getId, exportIdList).orderByDesc(ProcessManagement::getUpdateDate).list();
        List<ProcessManagementExportVO> processManagementExportVOList = dealExportData(processManagements);
        exportData(response, processManagementExportVOList, "工序管理");
    }

    /**
     * 处理导出数据
     *
     * @param processManagementList
     */
    private List<ProcessManagementExportVO> dealExportData(List<ProcessManagement> processManagementList) {
        List<ProcessManagementExportVO> processManagementExportVOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(processManagementList)){
            for (ProcessManagement processManagement : processManagementList) {
                String section = processManagement.getSection();
                String sectionName = getSectionName(section);
                processManagement.setSection(sectionName);
                ProcessManagementExportVO processManagementExportVO = BeanUtil.toBean(processManagement, ProcessManagementExportVO.class);
                processManagementExportVO.setTunnelingStartTime(getTimeStr(processManagement.getTunnelingStartTime()));
                processManagementExportVO.setTunnelingEndTime(getTimeStr(processManagement.getTunnelingEndTime()));
                processManagementExportVO.setChangeStepsStartTime(getTimeStr(processManagement.getChangeStepsStartTime()));
                processManagementExportVO.setChangeStepsEndTime(getTimeStr(processManagement.getChangeStepsEndTime()));
                processManagementExportVO.setSegmentAssemblyStartTime(getTimeStr(processManagement.getSegmentAssemblyStartTime()));
                processManagementExportVO.setSegmentAssemblyEndTime(getTimeStr(processManagement.getSegmentAssemblyEndTime()));
                processManagementExportVO.setOthersJobStartTime(getTimeStr(processManagement.getOthersJobStartTime()));
                processManagementExportVO.setOthersJobEndTime(getTimeStr(processManagement.getOthersJobEndTime()));
                processManagementExportVO.setCommonMaintenanceAndRepairStartTime(getTimeStr(processManagement.getCommonMaintenanceAndRepairStartTime()));
                processManagementExportVO.setCommonMaintenanceAndRepairEndTime(getTimeStr(processManagement.getCommonMaintenanceAndRepairEndTime()));
                processManagementExportVO.setShutdownMaintenanceAndRepairEndTime(getTimeStr(processManagement.getShutdownMaintenanceAndRepairEndTime()));
                processManagementExportVO.setShutdownMaintenanceAndRepairStartTime(getTimeStr(processManagement.getShutdownMaintenanceAndRepairStartTime()));
                processManagementExportVO.setOpenWarehouseChangeToolStartTime(getTimeStr(processManagement.getOpenWarehouseChangeToolStartTime()));
                processManagementExportVO.setOpenWarehouseChangeToolEndTime(getTimeStr(processManagement.getOpenWarehouseChangeToolEndTime()));
                BigDecimal assemblyPoint = processManagement.getAssemblyPoint();
                processManagementExportVO.setAssemblyPoint(getAssemblyPoint(assemblyPoint));

                String descriptionOfConstructionSituation = processManagement.getDescriptionOfConstructionSituation();
                if(!StringUtils.isEmpty(descriptionOfConstructionSituation)){
                    ConstructionSituationDescriptionParam constructionSituationDescriptionParam = JSONObject.parseObject(descriptionOfConstructionSituation, ConstructionSituationDescriptionParam.class);
                    processManagementExportVO.setConstructionWasteSoilProperties(constructionSituationDescriptionParam.getConstructionWasteSoilProperties());
                    processManagementExportVO.setResidueDischargeVolume(constructionSituationDescriptionParam.getResidueDischargeVolume());
                    processManagementExportVO.setSlopeOfTheSegment(constructionSituationDescriptionParam.getSlopeOfTheSegment());
                    processManagementExportVO.setUseOfFoamingAgent(constructionSituationDescriptionParam.getUseOfFoamingAgent());
                    processManagementExportVO.setGreaseUsage(constructionSituationDescriptionParam.getGreaseUsage());
                    processManagementExportVO.setBentonite(constructionSituationDescriptionParam.getBentonite());
                    processManagementExportVO.setOthers(constructionSituationDescriptionParam.getOthers());
                }
                processManagementExportVOList.add(processManagementExportVO);
            }
        }

        return processManagementExportVOList;
    }

    /**
     * bigDecimal转字符串
     *
     * @param assemblyPoint
     * @return
     */
    private String getAssemblyPoint(BigDecimal assemblyPoint){
        if(assemblyPoint == null){
            return null;
        }
        double d = assemblyPoint.doubleValue();
        String numStr = Double.toString(d).replaceAll("\\.0*$", "");
        return numStr;
    }

    /**
     * 根据区段code获取区段名称
     *
     * @param section
     * @return
     */
    private String getSectionName(String section){
        LambdaQueryWrapper<DeviceManage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceManage::getCode, section);
        queryWrapper.eq(DeviceManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag());
        List<DeviceManage> deviceManageList = deviceManageMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(deviceManageList)) {
            return null;
        }

        return deviceManageList.get(0).getSection();
    }

    @Override
    public List<ProcessManagementRingNumVO> listProcessManagementRingNum(String section) {
        List<ProcessManagementRingNumVO> processManagementRingNumVOList = processManagementMapper.listProcessManagementRingNum(section);
        if (CollectionUtils.isEmpty(processManagementRingNumVOList)) {
            return new ArrayList<>();
        }

        //数据去重
//        List<ProcessManagementRingNumVO> distinctList = ListUtil.distinct(processManagementRingNumVOList, ProcessManagementRingNumVO::getRingNum);
        //按照时间排序最新的数据
        ProcessManagementRingNumVO latestProcessManagementRingNumVO = processManagementRingNumVOList.stream().sorted(Comparator.comparing(ProcessManagementRingNumVO::getUpdateTime).reversed()).findFirst().get();

        //是否是最新一条数据的环号，0：否；1：是
        for (ProcessManagementRingNumVO processManagementRingNumVO : processManagementRingNumVOList) {
            if (Objects.equals(processManagementRingNumVO.getRingNum(), latestProcessManagementRingNumVO.getRingNum())) {
                processManagementRingNumVO.setDataLastRingNum(1);
            } else {
                processManagementRingNumVO.setDataLastRingNum(0);
            }
        }

        List<ProcessManagementRingNumVO> distinctList = ListUtil.distinct(processManagementRingNumVOList, ProcessManagementRingNumVO::getRingNum);
        return distinctList;
    }

    @Override
    public ProcessTotalDistributionVO statisticsProcessDistribution(String section, String ringNum) {
        LambdaQueryWrapper<ProcessManagement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProcessManagement::getSection, section);
        queryWrapper.eq(ProcessManagement::getRingNum, ringNum);
        queryWrapper.eq(ProcessManagement::getDeleteFlag, 0);
        queryWrapper.orderByAsc(ProcessManagement::getUpdateDate);

        List<ProcessManagement> processManagementList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(processManagementList)) {
            return new ProcessTotalDistributionVO();
        }

        ProcessTotalDistributionVO processTotalDistributionVO = new ProcessTotalDistributionVO();
        //当前区段、环号下的每一个工序的所有的开始时间、结束时间
        Map<String, List<ProcessDistributionTimeVO>> processDistributionTimeVOMap = new HashMap<>();
        Set<String> processNameSet = new HashSet<>();
        List<Timestamp> startTimeStampList = new ArrayList<>();
        List<Timestamp> endTimeStampList = new ArrayList<>();
        for (ProcessManagement processManagement : processManagementList) {
            //工序名称 逗号分隔
            String processName = processManagement.getProcessName();
            if (StringUtils.isNotBlank(processName)) {
                String[] processNameArr = processName.split(",");
                for (String processNameStr : processNameArr) {
                    boolean add = processNameSet.add(processNameStr);
                    Timestamp startTimeStamp = getTimeStamp(processManagement, 1, processNameStr);
                    Timestamp endTimeStamp = getTimeStamp(processManagement, 2, processNameStr);
                    //新的工序名称
                    if (add) {
                        List<ProcessDistributionTimeVO> processDistributionTimeVOList = new ArrayList<>();
                        ProcessDistributionTimeVO processDistributionTimeVO = new ProcessDistributionTimeVO();
                        processDistributionTimeVO.setStartTime(startTimeStamp);
                        processDistributionTimeVO.setEndTime(endTimeStamp);
                        processDistributionTimeVOList.add(processDistributionTimeVO);
                        processDistributionTimeVOMap.put(processNameStr, processDistributionTimeVOList);
                    } else {
                        //老的工序名称
                        List<ProcessDistributionTimeVO> processDistributionTimeVOList = processDistributionTimeVOMap.get(processNameStr);
                        ProcessDistributionTimeVO processDistributionTimeVO = new ProcessDistributionTimeVO();
                        processDistributionTimeVO.setStartTime(startTimeStamp);
                        processDistributionTimeVO.setEndTime(endTimeStamp);
                        processDistributionTimeVOList.add(processDistributionTimeVO);
                        processDistributionTimeVOMap.put(processNameStr, processDistributionTimeVOList);
                    }
                    startTimeStampList.add(startTimeStamp);
                    endTimeStampList.add(endTimeStamp);
                }
            }
        }

        //设置工序总分布最早开始时间和最晚结束时间
        processTotalDistributionVO.setStartTime(CollectionUtils.isEmpty(startTimeStampList) ? null : startTimeStampList.stream().min(Timestamp::compareTo).get());
        processTotalDistributionVO.setEndTime(CollectionUtils.isEmpty(endTimeStampList) ? null :endTimeStampList.stream().max(Timestamp::compareTo).get());

        processTotalDistributionVO.setProcessDistributionVOList(toProcessDistributionVOList(processDistributionTimeVOMap));
        return processTotalDistributionVO;
    }

    @Override
    public List<ProcessRatioVO> statisticsProcessRatio(String section, String ringNum) {
        List<ProcessRatioVO> processRatioVOList = new ArrayList<>();
        LambdaQueryWrapper<ProcessManagement> totalQueryWrapper = new LambdaQueryWrapper<>();
        totalQueryWrapper.eq(ProcessManagement::getSection, section);
        totalQueryWrapper.eq(ProcessManagement::getDeleteFlag, 0);
        totalQueryWrapper.orderByAsc(ProcessManagement::getUpdateDate);
        List<ProcessManagement> totalProcessManagementList = this.list(totalQueryWrapper);

        //查询该区段下所有工序的总时长
        Long totalProcessCumulativeTime = getProcessCumulativeTime(totalProcessManagementList);
        //如果环号不为空，分子中的工序要查询在该环号下的数据
        if (StringUtils.isNotBlank(ringNum)) {
            LambdaQueryWrapper<ProcessManagement> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProcessManagement::getSection, section);
            queryWrapper.eq(ProcessManagement::getRingNum, ringNum);
            queryWrapper.eq(ProcessManagement::getDeleteFlag, 0);
            queryWrapper.orderByAsc(ProcessManagement::getUpdateDate);

            List<ProcessManagement> processManagementList = this.list(queryWrapper);
            if (CollectionUtils.isEmpty(processManagementList)) {
                return processRatioVOList;
            }

            //每个工序的总时长
            Map<String, Long> processCumulativeTimeMap = getProcessCumulativeTimeMap(processManagementList);
            Long processCumulativeTime = getProcessCumulativeTime(processCumulativeTimeMap);
            List<ProcessRatioVO> processRatioVOS = toProcessRatioVOList(processCumulativeTimeMap, processCumulativeTime);
            fillIn(processRatioVOS);
            return processRatioVOS;
        }

        Map<String, Long> processCumulativeTimeMap = getProcessCumulativeTimeMap(totalProcessManagementList);
        List<ProcessRatioVO> processRatioVOS = toProcessRatioVOList(processCumulativeTimeMap, totalProcessCumulativeTime);
        fillIn(processRatioVOS);
        return processRatioVOS;
    }

    /**
     * 获取累计时长
     *
     * @param processCumulativeTimeMap
     * @return
     */
    private Long getProcessCumulativeTime(Map<String, Long> processCumulativeTimeMap) {
        Long processCumulativeTime = 0L;
        if(processCumulativeTimeMap != null && !processCumulativeTimeMap.isEmpty()){
            Set<Map.Entry<String, Long>> entrySet = processCumulativeTimeMap.entrySet();
            for (Map.Entry<String, Long> entry : entrySet) {
                Long processTime = entry.getValue();
                processCumulativeTime = processCumulativeTime + processTime;
            }
        }

        return processCumulativeTime;
    }

    @Override
    public ProcessConstructionSituationDescriptionVO getConstructionSituationDescription(String section) {
        LambdaQueryWrapper<ProcessManagement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProcessManagement::getSection, section);
        queryWrapper.eq(ProcessManagement::getDeleteFlag, 0);
//        queryWrapper.isNotNull(ProcessManagement::getDescriptionOfConstructionSituation);
        queryWrapper.orderByDesc(ProcessManagement::getUpdateDate);
        queryWrapper.last("limit 1");
        ProcessManagement processManagement = this.getOne(queryWrapper);
        if (processManagement != null) {
            String descriptionOfConstructionSituation = processManagement.getDescriptionOfConstructionSituation();
            ConstructionSituationDescriptionParam constructionSituationDescriptionParam = JSONObject.parseObject(descriptionOfConstructionSituation, ConstructionSituationDescriptionParam.class);
            ProcessConstructionSituationDescriptionVO processConstructionSituationDescriptionVO = new ProcessConstructionSituationDescriptionVO();
            processConstructionSituationDescriptionVO.setGreaseUsage(constructionSituationDescriptionParam == null ? "" : constructionSituationDescriptionParam.getGreaseUsage());
            processConstructionSituationDescriptionVO.setId(processManagement.getId());
            processConstructionSituationDescriptionVO.setAssemblyPoint(processManagement.getAssemblyPoint());
            processConstructionSituationDescriptionVO.setResidueDischargeVolume(constructionSituationDescriptionParam == null ? "" : constructionSituationDescriptionParam.getResidueDischargeVolume());
            processConstructionSituationDescriptionVO.setUseOfFoamingAgent(constructionSituationDescriptionParam == null ? "" : constructionSituationDescriptionParam.getUseOfFoamingAgent());
            return processConstructionSituationDescriptionVO;
        }
        return new ProcessConstructionSituationDescriptionVO();
    }

    @Override
    public List<RingAbnormalSituationsDescriptionVO> listAllRingAbnormalSituationsDescription(String section) {
        List<RingAbnormalSituationsDescriptionVO> ringAbnormalSituationsDescriptionVOS = processManagementMapper.listAllRingAbnormalSituationsDescription(section);
        return ringAbnormalSituationsDescriptionVOS;
    }

    @Override
    public List<EquipmentUseRateVO> statisticsEquipmentUseRate(String section) {
        List<ProcessManagementVO> processManagementVOList = processManagementMapper.listProcessManagementGroupByRingNum(section);
        if (CollectionUtils.isEmpty(processManagementVOList)) {
            return new ArrayList<>();
        }

        List<EquipmentUseRateVO> equipmentUseRateVOS = new ArrayList<>();
        //对processManagementVOList 按照环数进行排序，从小到大进行排序
        processManagementVOList.sort(Comparator.comparing(ProcessManagementVO::getRingNum));
        int size = processManagementVOList.size();
        if (size == 1) {
            List<EquipmentUseRateVO> equipmentUseRateVOList = getEquipmentUseRateVOS(processManagementVOList, equipmentUseRateVOS);
            return equipmentUseRateVOList;
        }

        int loop = 0;
        for (ProcessManagementVO processManagementVO : processManagementVOList) {
            //最后一个环数不处理
            if (loop != size - 1) {
                EquipmentUseRateVO equipmentUseRateVO = new EquipmentUseRateVO();
                equipmentUseRateVO.setRingNum(processManagementVO.getRingNum() + "环");
                Timestamp tunnelingStartTime = processManagementVO.getTunnelingStartTime();
                Timestamp tunnelingEndTime = processManagementVO.getTunnelingEndTime();
                Long processTime = getProcessCumulativeTime(tunnelingStartTime, tunnelingEndTime);

                //取下一环数据
                ProcessManagementVO nextProcessManagementVO = processManagementVOList.get(loop + 1);
                Timestamp nextTunnelingStartTime = nextProcessManagementVO.getTunnelingStartTime();
                //分母
                Long totalProcessCumulativeTime = getProcessCumulativeTime(nextTunnelingStartTime, tunnelingStartTime);
                totalProcessCumulativeTime = totalProcessCumulativeTime < 0 ? -totalProcessCumulativeTime : totalProcessCumulativeTime;
                double v = ((double) processTime / totalProcessCumulativeTime) * 100;
                long roundedNumber = Math.round(v);
                equipmentUseRateVO.setUseRate(roundedNumber + "%");
                equipmentUseRateVOS.add(equipmentUseRateVO);
            }
            loop++;
        }
        return equipmentUseRateVOS;
    }

    @Override
    public PageInfo<ProcessManagementAppVO> pageApp(String section, String searchValue, Integer pageNo, Integer pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        LambdaQueryWrapper<ProcessManagement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProcessManagement::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag());
        if (StringUtils.isNotEmpty(searchValue)) {
            queryWrapper.like(ProcessManagement::getProcessName, searchValue).or().like(ProcessManagement::getRingNum, searchValue);
        }

        queryWrapper.orderByDesc(ProcessManagement::getUpdateDate);

        List<ProcessManagement> processManagementList = this.list(queryWrapper);

        if (CollectionUtils.isEmpty(processManagementList)) {
            return new PageInfo<>();
        }

        PageInfo<ProcessManagement> info = new PageInfo<>(processManagementList);
        List<ProcessManagement> managementList = info.getList();
        List<ProcessManagementAppVO> processManagementAppVOS = new ArrayList<>();

        for (ProcessManagement processManagement : managementList) {
            ProcessManagementAppVO processManagementAppVO = BeanUtil.toBean(processManagement, ProcessManagementAppVO.class);
            processManagementAppVO.setUpdateName(processManagement.getUpdateName());
            //区段名称，需要根据设备code查询
            LambdaQueryWrapper<DeviceManage> manageLambdaQueryWrapper = new LambdaQueryWrapper<>();
            manageLambdaQueryWrapper.eq(DeviceManage::getCode, processManagement.getSection());
            manageLambdaQueryWrapper.eq(DeviceManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag());
            DeviceManage deviceManage = deviceManageMapper.selectOne(manageLambdaQueryWrapper);
            processManagementAppVO.setSection(deviceManage == null ? "" : deviceManage.getSection());
            processManagementAppVOS.add(processManagementAppVO);
        }

        PageInfo<ProcessManagementAppVO> managementVOPageInfo = BeanUtil.toBean(info, PageInfo.class);
        managementVOPageInfo.setList(processManagementAppVOS);
        return managementVOPageInfo;
    }

    /**
     * 处理环数等于1时的逻辑
     *
     * @param processManagementVOList
     * @param equipmentUseRateVOS
     * @return
     */
    private List<EquipmentUseRateVO> getEquipmentUseRateVOS(List<ProcessManagementVO> processManagementVOList, List<EquipmentUseRateVO> equipmentUseRateVOS) {
        //查询该区段下所有工序的总时长
        Long totalProcessCumulativeTime = getProcessCumulativeTimeV2(processManagementVOList);
        //只有第1环时
        ProcessManagementVO processManagementVO = processManagementVOList.get(0);
        Timestamp tunnelingStartTime = processManagementVO.getTunnelingStartTime();
        Timestamp tunnelingEndTime = processManagementVO.getTunnelingEndTime();
        if (tunnelingStartTime != null && tunnelingEndTime != null) {
            Long processTime = getProcessCumulativeTime(tunnelingStartTime, tunnelingEndTime);
            double v = ((double) processTime / totalProcessCumulativeTime) * 100;
            long roundedNumber = Math.round(v);
            EquipmentUseRateVO equipmentUseRateVO = new EquipmentUseRateVO();
            equipmentUseRateVO.setRingNum(processManagementVO.getRingNum() + "环");
            equipmentUseRateVO.setUseRate(roundedNumber + "%");
            equipmentUseRateVOS.add(equipmentUseRateVO);
            return equipmentUseRateVOS;
        }
        return null;
    }

    /**
     * 获取当前区段下的所有环号下的所有工序的累计时长
     *
     * @param processManagementVOList
     * @return
     */
    private Long getProcessCumulativeTimeV2(List<ProcessManagementVO> processManagementVOList) {
        Long initProcessCumulativeTime = 0L;
        if (CollectionUtils.isEmpty(processManagementVOList)) {
            return initProcessCumulativeTime;
        }

        for (ProcessManagementVO processManagementVO : processManagementVOList) {
            //工序名称 逗号分隔
            String processName = processManagementVO.getProcessName();
            String[] processNameArr = processName.split(",");
            for (String processNameStr : processNameArr) {
                Timestamp startTimeStamp = getTimeStampV2(processManagementVO, 1, processNameStr);
                Timestamp endTimeStamp = getTimeStampV2(processManagementVO, 2, processNameStr);
                Long processCumulativeTime = getProcessCumulativeTime(startTimeStamp, endTimeStamp);
                initProcessCumulativeTime = initProcessCumulativeTime + processCumulativeTime;
            }
        }

        return initProcessCumulativeTime;
    }

    /**
     * 获取时间戳
     *
     * @param processManagementVO
     * @param startOrEnd          1：开始时间；2：结束时间
     * @return
     */
    private Timestamp getTimeStampV2(ProcessManagementVO processManagementVO, Integer startOrEnd, String processNameStr) {
        if (processManagementVO == null || startOrEnd == null || processNameStr == null) {
            return null;
        }

        //掘进
        if (StringUtils.equals(processNameStr, ProcessCodeNameEnums.TUNNELING.getName())) {
            if (startOrEnd == 1) {
                return processManagementVO.getTunnelingStartTime();
            }

            if (startOrEnd == 2) {
                return processManagementVO.getTunnelingEndTime();
            }
        }

        //换步
        if (StringUtils.equals(processNameStr, ProcessCodeNameEnums.CHANGE_STEPS.getName())) {
            if (startOrEnd == 1) {
                return processManagementVO.getChangeStepsStartTime();
            }

            if (startOrEnd == 2) {
                return processManagementVO.getChangeStepsEndTime();
            }
        }

        //管片拼装
        if (StringUtils.equals(processNameStr, ProcessCodeNameEnums.SEGMENT_ASSEMBLY.getName())) {
            if (startOrEnd == 1) {
                return processManagementVO.getSegmentAssemblyStartTime();
            }

            if (startOrEnd == 2) {
                return processManagementVO.getSegmentAssemblyEndTime();
            }
        }

        //开仓换刀
        if (StringUtils.equals(processNameStr, ProcessCodeNameEnums.OPEN_WAREHOUSE_CHANGE_TOOL.getName())) {
            if (startOrEnd == 1) {
                return processManagementVO.getOpenWarehouseChangeToolStartTime();
            }

            if (startOrEnd == 2) {
                return processManagementVO.getOpenWarehouseChangeToolEndTime();
            }
        }

        //常规保养
        if (StringUtils.equals(processNameStr, ProcessCodeNameEnums.COMMON_MAINTENANCE_AND_REPAIR.getName())) {
            if (startOrEnd == 1) {
                return processManagementVO.getCommonMaintenanceAndRepairStartTime();
            }

            if (startOrEnd == 2) {
                return processManagementVO.getCommonMaintenanceAndRepairEndTime();
            }
        }

        //故障停机
        if (StringUtils.equals(processNameStr, ProcessCodeNameEnums.SHUTDOWN_MAINTENANCE_AND_REPAIR.getName())) {
            if (startOrEnd == 1) {
                return processManagementVO.getShutdownMaintenanceAndRepairStartTime();
            }

            if (startOrEnd == 2) {
                return processManagementVO.getShutdownMaintenanceAndRepairEndTime();
            }
        }

        //其他工作
        if (StringUtils.equals(processNameStr, ProcessCodeNameEnums.OTHERS_JOB.getName())) {
            if (startOrEnd == 1) {
                return processManagementVO.getOthersJobStartTime();
            }

            if (startOrEnd == 2) {
                return processManagementVO.getOthersJobEndTime();
            }
        }

        return null;
    }

    /**
     * 补位
     *
     * @param processRatioVOS
     */
    private void fillIn(List<ProcessRatioVO> processRatioVOS) {
        if (CollectionUtils.isEmpty(processRatioVOS)) {
            return;
        }

        List<String> processNameList = processRatioVOS.stream().map(ProcessRatioVO::getProcessName).collect(Collectors.toList());
        ProcessCodeNameEnums[] processCodeNameEnumsArr = ProcessCodeNameEnums.values();
        for (ProcessCodeNameEnums processCodeNameEnums : processCodeNameEnumsArr) {
            String name = processCodeNameEnums.getName();
            if (!processNameList.contains(name)) {
                ProcessRatioVO processRatioVO = new ProcessRatioVO();
                processRatioVO.setProcessName(name);
                processRatioVO.setRatio("0%");
                processRatioVOS.add(processRatioVO);
            }
        }
    }

    /**
     * 构造工序比例列表
     *
     * @param processCumulativeTimeMap
     * @param totalProcessCumulativeTime
     * @return
     */
    private List<ProcessRatioVO> toProcessRatioVOList(Map<String, Long> processCumulativeTimeMap, Long totalProcessCumulativeTime) {
        List<ProcessRatioVO> processRatioVOList = new ArrayList<>();
        Set<Map.Entry<String, Long>> entrySet = processCumulativeTimeMap.entrySet();
        int size = entrySet.size();
        int len = 0;
        Long totalProcessRatio = 0L;
        for (Map.Entry<String, Long> entry : entrySet) {
            String processName = entry.getKey();
            Long processTime = entry.getValue();
            ProcessRatioVO processRatioVO = new ProcessRatioVO();
            processRatioVO.setProcessName(processName);
            if (len != size - 1) {
                double v = ((double) processTime / totalProcessCumulativeTime) * 100;
                long roundedNumber = Math.round(v);
                totalProcessRatio = totalProcessRatio + roundedNumber;
                processRatioVO.setRatio(roundedNumber + "%");
            } else {
                Long processRatio = 100 - totalProcessRatio;
                processRatioVO.setRatio(processRatio + "%");
            }

            processRatioVOList.add(processRatioVO);
            len++;
        }

        return processRatioVOList;
    }

    /**
     * 获取当前区段下的所有环号下的所有工序的累计时长
     *
     * @param processManagementList
     * @return
     */
    private Long getProcessCumulativeTime(List<ProcessManagement> processManagementList) {
        Long initProcessCumulativeTime = 0L;
        if (CollectionUtils.isEmpty(processManagementList)) {
            return initProcessCumulativeTime;
        }

        for (ProcessManagement processManagement : processManagementList) {
            //工序名称 逗号分隔
            String processName = processManagement.getProcessName();
            if(StringUtils.isNotBlank(processName)){
                String[] processNameArr = processName.split(",");
                for (String processNameStr : processNameArr) {
                    Timestamp startTimeStamp = getTimeStamp(processManagement, 1, processNameStr);
                    Timestamp endTimeStamp = getTimeStamp(processManagement, 2, processNameStr);
                    Long processCumulativeTime = getProcessCumulativeTime(startTimeStamp, endTimeStamp);
                    initProcessCumulativeTime = initProcessCumulativeTime + processCumulativeTime;
                }
            }
        }

        return initProcessCumulativeTime;
    }

    /**
     * 获取工序累计时间
     *
     * @param processManagementList
     * @return
     */
    private Map<String, Long> getProcessCumulativeTimeMap(List<ProcessManagement> processManagementList) {
        Map<String, Long> processCumulativeTimeMap = new HashMap<>();
        if (CollectionUtils.isEmpty(processManagementList)) {
            return processCumulativeTimeMap;
        }
        Set<String> processNameSet = new HashSet<>();
        for (ProcessManagement processManagement : processManagementList) {
            //工序名称 逗号分隔
            String processName = processManagement.getProcessName();
            if(StringUtils.isNotBlank(processName)){
                String[] processNameArr = processName.split(",");
                for (String processNameStr : processNameArr) {
                    boolean add = processNameSet.add(processNameStr);
                    Timestamp startTimeStamp = getTimeStamp(processManagement, 1, processNameStr);
                    Timestamp endTimeStamp = getTimeStamp(processManagement, 2, processNameStr);
                    Long processCumulativeTime = getProcessCumulativeTime(startTimeStamp, endTimeStamp);
                    //新的工序名称
                    if (add) {
                        processCumulativeTimeMap.put(processNameStr, processCumulativeTime);
                    } else {
                        //老的工序名称
                        Long cumulativeTime = processCumulativeTimeMap.get(processNameStr);
                        processCumulativeTimeMap.put(processNameStr, cumulativeTime + processCumulativeTime);
                    }
                }
            }
        }

        return processCumulativeTimeMap;
    }

    /**
     * timestamp 转换为 LocalDateTime
     *
     * @param timestamp
     * @return
     */
    private LocalDateTime getLocalDateTime(Timestamp timestamp) {
        if (timestamp == null) {
            return null;
        }
        String startTimeStr = getTimeStr(timestamp);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        LocalDateTime startTimeLocalDateTime = LocalDateTime.parse(startTimeStr, formatter);
        return startTimeLocalDateTime;
    }

    /**
     * 计算工序累计时间
     *
     * @param startTimeStamp
     * @param endTimeStamp
     * @return
     */
    private Long getProcessCumulativeTime(Timestamp startTimeStamp, Timestamp endTimeStamp) {
        if (startTimeStamp == null || endTimeStamp == null) {
            return 0L;
        }
        String startTimeStr = getTimeStr(startTimeStamp);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        LocalDateTime startTimeLocalDateTime = LocalDateTime.parse(startTimeStr, formatter);

        String endTimeStr = getTimeStr(endTimeStamp);
        LocalDateTime endTimeLocalDateTime = LocalDateTime.parse(endTimeStr, formatter);

        Duration timeDifference = Duration.between(startTimeLocalDateTime, endTimeLocalDateTime);
        long minutes = timeDifference.toMinutes();
        return minutes;
    }

    /**
     * map 转 list
     *
     * @param processDistributionTimeVOMap
     * @return
     */
    private List<ProcessDistributionVO> toProcessDistributionVOList(Map<String, List<ProcessDistributionTimeVO>> processDistributionTimeVOMap) {
        List<ProcessDistributionVO> processDistributionVOList = new ArrayList<>();
        if (processDistributionTimeVOMap == null || processDistributionTimeVOMap.isEmpty()) {
            return processDistributionVOList;
        }
        for (Map.Entry<String, List<ProcessDistributionTimeVO>> entry : processDistributionTimeVOMap.entrySet()) {
            ProcessDistributionVO processDistributionVO = new ProcessDistributionVO();
            processDistributionVO.setProcessName(entry.getKey());
            List<ProcessDistributionTimeVO> processDistributionTimeVOList = entry.getValue();
            processDistributionTimeVOList = processDistributionTimeVOList.stream().sorted(Comparator.comparing(ProcessDistributionTimeVO::getStartTime)).collect(Collectors.toList());
            processDistributionVO.setProcessDistributionTimeVOList(processDistributionTimeVOList);
            processDistributionVOList.add(processDistributionVO);
        }

        return processDistributionVOList;
    }

    /**
     * 获取时间戳
     *
     * @param processManagement
     * @param startOrEnd        1：开始时间；2：结束时间
     * @return
     */
    private Timestamp getTimeStamp(ProcessManagement processManagement, Integer startOrEnd, String processNameStr) {
        if (processManagement == null || startOrEnd == null || processNameStr == null) {
            return null;
        }

        //掘进
        if (StringUtils.equals(processNameStr, ProcessCodeNameEnums.TUNNELING.getName())) {
            if (startOrEnd == 1) {
                return processManagement.getTunnelingStartTime();
            }

            if (startOrEnd == 2) {
                return processManagement.getTunnelingEndTime();
            }
        }

        //换步
        if (StringUtils.equals(processNameStr, ProcessCodeNameEnums.CHANGE_STEPS.getName())) {
            if (startOrEnd == 1) {
                return processManagement.getChangeStepsStartTime();
            }

            if (startOrEnd == 2) {
                return processManagement.getChangeStepsEndTime();
            }
        }

        //管片拼装
        if (StringUtils.equals(processNameStr, ProcessCodeNameEnums.SEGMENT_ASSEMBLY.getName())) {
            if (startOrEnd == 1) {
                return processManagement.getSegmentAssemblyStartTime();
            }

            if (startOrEnd == 2) {
                return processManagement.getSegmentAssemblyEndTime();
            }
        }

        //开仓换刀
        if (StringUtils.equals(processNameStr, ProcessCodeNameEnums.OPEN_WAREHOUSE_CHANGE_TOOL.getName())) {
            if (startOrEnd == 1) {
                return processManagement.getOpenWarehouseChangeToolStartTime();
            }

            if (startOrEnd == 2) {
                return processManagement.getOpenWarehouseChangeToolEndTime();
            }
        }

        //常规保养
        if (StringUtils.equals(processNameStr, ProcessCodeNameEnums.COMMON_MAINTENANCE_AND_REPAIR.getName())) {
            if (startOrEnd == 1) {
                return processManagement.getCommonMaintenanceAndRepairStartTime();
            }

            if (startOrEnd == 2) {
                return processManagement.getCommonMaintenanceAndRepairEndTime();
            }
        }

        //故障停机
        if (StringUtils.equals(processNameStr, ProcessCodeNameEnums.SHUTDOWN_MAINTENANCE_AND_REPAIR.getName())) {
            if (startOrEnd == 1) {
                return processManagement.getShutdownMaintenanceAndRepairStartTime();
            }

            if (startOrEnd == 2) {
                return processManagement.getShutdownMaintenanceAndRepairEndTime();
            }
        }

        //其他工作
        if (StringUtils.equals(processNameStr, ProcessCodeNameEnums.OTHERS_JOB.getName())) {
            if (startOrEnd == 1) {
                return processManagement.getOthersJobStartTime();
            }

            if (startOrEnd == 2) {
                return processManagement.getOthersJobEndTime();
            }
        }

        return null;
    }

    /**
     * 导出数据
     *
     * @param response
     * @param processManagementExportVOS
     * @throws IOException
     */
    private void exportData(HttpServletResponse response, List<ProcessManagementExportVO> processManagementExportVOS, String fileName) throws IOException {
        ServletOutputStream outputStream = response.getOutputStream();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(outputStream)
                .head(ProcessManagementExportVO.class)
                .excelType(ExcelTypeEnum.XLSX)
                .sheet("sheet")
                .doWrite(processManagementExportVOS);

        outputStream.flush();
        outputStream.close();
    }

    /**
     * 导出错误数据
     *
     * @param response
     * @param processManagementExportVOS
     * @throws IOException
     */
    private void exportErrorData(HttpServletResponse response, List<ProcessManagementExportVO> processManagementExportVOS, String fileName) throws IOException {
        ServletOutputStream outputStream = response.getOutputStream();
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(outputStream)
                .head(ProcessManagementExportVO.class)
                .excelType(ExcelTypeEnum.XLSX)
                .sheet("sheet")
                .doWrite(processManagementExportVOS);

        outputStream.flush();
        outputStream.close();
    }

    /**
     * 判断两个输入文本是否是时间格式
     * 校验结束必须时间大于开始时间，即只能选择开始时间之后的时间且不能录入(超过当前时间24小时【含24小时】)未来时间
     *
     * @param input1
     * @param input2
     * @return
     */
    private Boolean getTimeResult(Timestamp input1, Timestamp input2) {
        if ((input1 == null && input2 != null) || (input1 != null && input2 == null)) {
            return false;
        }

        if (input1 == null && input2 == null) {
            return true;
        }

        //剩下的两者都不为空，进行参数判断
        Date date1 = getTimeFormatResult(input1);
        Date date2 = getTimeFormatResult(input2);
        if (date1 == null || date2 == null) {
            return false;
        }

        //结束必须时间大于开始时间
        if (date2.before(date1)) {
            return false;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        //结束时间都是开始时间+24之内的时间
        String timeStr1 = getTimeStr(input1);
        LocalDateTime localDateTime = LocalDateTime.parse(timeStr1, formatter);
        LocalDateTime afterLocalDateTime = localDateTime.plusHours(24);
        //结束时间
        String timeStr = getTimeStr(input2);
        LocalDateTime input2LocalDateTime = LocalDateTime.parse(timeStr, formatter);

        if (input2LocalDateTime.isAfter(afterLocalDateTime)) {
            return false;
        }

        return true;
    }

    /**
     * 判断时间不能为空，都为空，或者都有值
     *
     * @param input1
     * @param input2
     * @return
     */
    private Boolean getTimeBlankResult(String input1, String input2) {
        if ((StringUtils.isBlank(input1) && StringUtils.isNotBlank(input2)) || (StringUtils.isNotBlank(input1) && StringUtils.isBlank(input2))) {
            return false;
        }

        if (StringUtils.isBlank(input1) && StringUtils.isBlank(input2)) {
            return true;
        }

        return true;
    }

    /**
     * 判断两个输入文本是否是时间格式
     * 校验结束必须时间大于开始时间，即只能选择开始时间之后的时间且不能录入(超过当前时间24小时【含24小时】)未来时间
     *
     * @param input1
     * @param input2
     * @return
     */
    private String getTimeResult(String input1, String input2, String errorMsgPrefix) {
        if(StringUtils.isEmpty(input1) && StringUtils.isEmpty(input2)){
            return null;
        }
        //剩下的两者都不为空，进行参数判断
        Date date1 = getTimeFormatResult(input1);
        Date date2 = getTimeFormatResult(input2);
        if (date1 == null || date2 == null) {
            return errorMsgPrefix + "开始时间或结束时间不正确，时间格式为yyyy-MM-dd HH:mm";
        }

        //结束必须时间大于开始时间
        if (date2.before(date1)) {
            return errorMsgPrefix + "结束必须时间大于开始时间";
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        //结束时间都是开始时间+24之内的时间
        LocalDateTime localDateTime = LocalDateTime.parse(input1, formatter);
        LocalDateTime afterLocalDateTime = localDateTime.plusHours(24);
        //结束时间
        LocalDateTime input2LocalDateTime = LocalDateTime.parse(input2, formatter);

        if (input2LocalDateTime.isAfter(afterLocalDateTime)) {
            return errorMsgPrefix + "结束时间必须在开始时间+24小时之内";
        }

        return null;
    }

    /**
     * 判断输入文本是否是日期格式的数据
     *
     * @param input
     * @return
     */
    private Date getTimeFormatResult(Timestamp input) {
        if (input == null) {
            return null;
        }

        try {
            String timeStr = getTimeStr(input);
            return DateUtils.dateTime("yyyy-MM-dd HH:mm", timeStr);
        } catch (Exception e) {
            log.error("getTimeFormatResult 时间格式转换异常：{}", e);
            return null;
        }
    }

    /**
     * timestamp 转 string
     *
     * @param input
     * @return
     */
    private String getTimeStr(Timestamp input) {
        if(input == null){
            return null;
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");//定义格式，不显示毫秒
        String str = df.format(input);
        return str;
    }

    /**
     * 判断输入文本是否是日期格式的数据
     *
     * @param input
     * @return
     */
    private Date getTimeFormatResult(String input) {
        if (StringUtils.isBlank(input)) {
            return null;
        }

        try {
            Date date = DateUtils.dateTime("yyyy-MM-dd HH:mm", input);
            return date;
        } catch (Exception e) {
            log.error("getTimeFormatResult 时间格式转换异常：{}", e);
            return null;
        }
    }

    /**
     * 获取整数、小数 匹配结果
     *
     * @param input         匹配内容
     * @param integerDigits 整数位数
     * @param decimalDigits 小数位数
     * @return
     */
    private Boolean getRegexpIntegerDecimalMatchResult(String input, int integerDigits, int decimalDigits) {
        if (StringUtils.isBlank(input)) {
            return false;
        }

        String regex = "[+]?\\d{1," + integerDigits + "}(\\.\\d{1," + decimalDigits + "})?";
        return input.matches(regex);
    }
}