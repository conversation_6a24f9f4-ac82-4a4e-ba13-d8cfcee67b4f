package com.fawkes.project.tbm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.core.exception.BusinessException;
import com.fawkes.project.tbm.common.mapper.CameraManageMapper;
import com.fawkes.project.tbm.common.model.CameraManage;
import com.fawkes.project.tbm.common.model.DeviceManage;
import com.fawkes.project.tbm.common.param.CameraManageParam;
import com.fawkes.project.tbm.common.vo.CameraManageTreeVO;
import com.fawkes.project.tbm.common.vo.CameraManageVO;
import com.fawkes.project.tbm.service.CameraManageService;
import com.fawkes.project.tbm.service.DeviceManageService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 摄像头配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Service
public class CameraManageServiceImpl extends ServiceImpl<CameraManageMapper, CameraManage> implements CameraManageService {
    @Resource
    private DeviceManageService deviceManageService;

    private static final String m38uUrlTest = "https://open.ys7.com/v3/openlive/FS7562710_1_1.m3u8?expire=1770600123&id=810440714545528832&t=c4c1dc7cf39b1ea44b39f0078e7e8948e0caae0dbdabd85eb31487539987fe08&ev=100";
    @Override
    public List<CameraManageTreeVO> list(String name) {
        List<CameraManageTreeVO> treeVOList = new ArrayList<>();
        List<CameraManageTreeVO> treeVOListReturn = new ArrayList<>();
        //拿取所有盾构机数据
        List<DeviceManage> list = deviceManageService.list();
        //拿取符合条件的所有摄像头数据
        List<CameraManage> cameraManages = this.lambdaQuery().like(StringUtils.isNotBlank(name), CameraManage::getCameraName, name)
                .eq(CameraManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .list();
        list.forEach(e -> {
            CameraManageTreeVO treeVO = new CameraManageTreeVO();
            List<CameraManageVO> collect = cameraManages.stream().filter(cameraManage -> cameraManage.getDeviceCode().equals(e.getCode()))
                    .map(cameraManage -> {
                        CameraManageVO cameraManageVO = BeanUtil.copyProperties(cameraManage, CameraManageVO.class);
                        cameraManageVO.setCameraUrl(m38uUrlTest);
                        return cameraManageVO;
                    }).collect(Collectors.toList());
            treeVO.setDeviceCode(e.getCode());
            treeVO.setDeviceName(e.getName());
            treeVO.setCameraList(collect);
            treeVOList.add(treeVO);
        });
        treeVOList.forEach(e -> {
            if (CollectionUtils.isNotEmpty(e.getCameraList()) || e.getDeviceName().contains(name)) {
                treeVOListReturn.add(e);
            }


        });

        return treeVOListReturn;
    }

    @Override
    public void add(CameraManageParam param) {
        //需要判断新摄像头名称是否已经被使用
        Integer count = this.lambdaQuery().eq(CameraManage::getCameraName, param.getCameraName())
                .eq(CameraManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .count();
        if (count > 0) {
            throw new BusinessException("该摄像头名称已存在，请勿重复");
        }
        //需要判断新摄像头Ip是否已经被使用
        Integer countIp = this.lambdaQuery().eq(CameraManage::getCameraIp, param.getCameraIp())
                .eq(CameraManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .count();
        if (countIp > 0) {
            throw new BusinessException("该设备Ip已存在，请勿重复");
        }
//判断一个盾构机的摄像头最大数量不能超过20个
        Integer listCount = this.lambdaQuery().eq(CameraManage::getDeviceCode, param.getDeviceCode())
                .eq(CameraManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .count();
        if (listCount >= 20) {
            throw new BusinessException("该设备下摄像头数量不能超过20个");
        }

        CameraManage cameraManage = BeanUtil.copyProperties(param, CameraManage.class);
        this.save(cameraManage);

    }

    @Override
    public void update(CameraManageParam param) {
        //需要判断新摄像头名称是否已经被使用
        Integer count = this.lambdaQuery().eq(CameraManage::getCameraName, param.getCameraName())
                .eq(CameraManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .notIn(CameraManage::getId, param.getId())
                .count();
        if (count > 0) {
            throw new BusinessException("该摄像头名称已存在，请勿重复");
        }
        //需要判断新摄像头Ip是否已经被使用
        Integer countIp = this.lambdaQuery().eq(CameraManage::getCameraIp, param.getCameraIp())
                .eq(CameraManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .notIn(CameraManage::getId, param.getId())
                .count();
        if (countIp > 0) {
            throw new BusinessException("该设备Ip已存在，请勿重复");
        }
        CameraManage cameraManage = BeanUtil.copyProperties(param, CameraManage.class);
        this.updateById(cameraManage);
    }

    @Override
    public boolean delete(String id) {
        return removeById(id);
    }

    @Override
    public CameraManageVO get(String id) {
        CameraManage byId = this.getById(id);
        CameraManageVO cameraManageVO = BeanUtil.copyProperties(byId, CameraManageVO.class);
        cameraManageVO.setCameraUrl(m38uUrlTest);
        return cameraManageVO;
    }

    @Override
    public List<CameraManageVO> listProcess(String name, String section) {
        //拿取符合条件的所有摄像头数据
        List<CameraManage> cameraManages = this.lambdaQuery().like(StringUtils.isNotBlank(name), CameraManage::getCameraName, name)
                .eq(CameraManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag()).eq(CameraManage::getProcessCamera, 1)
                .eq(CameraManage::getDeviceCode, section)
                .list();

        if(CollectionUtils.isEmpty(cameraManages)){
            return new ArrayList<>();
        }

        List<CameraManageVO> cameraManageVOS = cameraManages.stream().map(cameraManage -> {
            CameraManageVO cameraManageVO = BeanUtil.copyProperties(cameraManage, CameraManageVO.class);
            cameraManageVO.setCameraUrl(m38uUrlTest);
            return cameraManageVO;
        }).collect(Collectors.toList());
        return cameraManageVOS;
    }
}
