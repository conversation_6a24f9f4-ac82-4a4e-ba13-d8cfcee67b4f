package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmEarthSupport;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.common.vo.TbmXYVO;

import java.util.List;

/**
 * <p>
 * TBM土仓压力参数表(此表为原始表，数据按月分表) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
public interface TbmEarthSupportService extends IService<TbmEarthSupport> {
    // 根据设备编码获取最近的数据
    TbmEarthSupport getLastTbmEarthSupport(String deviceCode);

    // 根据设备编码和环号获取最后十个环号对应的数据
    List<TbmEarthSupport> getLastTenRingNumTbmEarthSupport(String deviceCode, Integer ringNum);

    /**
     * 获取最后十个环号对应的数据
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     * @param position 位置
     * @return
     */
    List<TbmXYVO> getRingNumTbmEarthSupport(String deviceCode, List<Integer> ringNumList, String position);

    /**
     * 同步IOT数据
     *
     * @param iotResponseList
     * @return
     */
    Boolean syncIotDeviceData(List<IOTResponse> iotResponseList, String deviceCode);
}
