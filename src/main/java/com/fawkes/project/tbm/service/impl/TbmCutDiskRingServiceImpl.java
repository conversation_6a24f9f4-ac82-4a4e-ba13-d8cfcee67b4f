package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.mapper.TbmCutDiskRingMapper;
import com.fawkes.project.tbm.common.model.TbmCutDiskRing;
import com.fawkes.project.tbm.service.TbmCutDiskRingService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version TbmCutDiskRingServiceImpl 2025/6/11 9:32
 */
@Service
public class TbmCutDiskRingServiceImpl
        extends ServiceImpl<TbmCutDiskRingMapper, TbmCutDiskRing>
        implements TbmCutDiskRingService {
    /**
     * 多条件查询
     */
    @Override
    public List<TbmCutDiskRing> queryList(LambdaQueryWrapper<TbmCutDiskRing> wrapper) {
        return this.list(wrapper);
    }

    /**
     * 批量新增
     */
    @Override
    public Boolean addBatch(List<TbmCutDiskRing> list) {
        return this.saveBatch(list);
    }

    /**
     * 条件修改
     */
    @Override
    public Boolean edit(LambdaUpdateWrapper<TbmCutDiskRing> wrapper) {
        return this.update(wrapper);
    }
}
