package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmDriveSystem;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.common.vo.TbmDriveSystemXYVO;

import java.util.List;

/**
 * <p>
 * TBM驱动系统参数(此表为原始表，数据按月分表) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
public interface TbmDriveSystemService extends IService<TbmDriveSystem> {
    // 根据设备编码获取最近的驱动系统数据
    TbmDriveSystem getLastTbmDriveSystem(String deviceCode);

    // 根据设备编码和环号获取最后十个环号对应的TbmDriveSystem对象
    List<TbmDriveSystem> getLastTenRingNumTbmDriveSystem(String deviceCode, Integer ringNum);

    /**
     * 获取最后十个环号对应的TbmDriveSystem对象
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     * @param motorTemperature 电机温度
     * @param electricCurrent 电机电流
     * @return
     */
    List<TbmDriveSystemXYVO> getRingNumTbmDriveSystem(String deviceCode, List<Integer> ringNumList, String motorTemperature, String electricCurrent);


    /**
     * 同步驱动系统
     *
     * @param iotResponseList
     */
    Boolean syncTbmDriveSystem(List<IOTResponse> iotResponseList, String deviceCode);
}
