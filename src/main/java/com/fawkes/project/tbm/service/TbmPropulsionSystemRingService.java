package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmEarthSupportRing;
import com.fawkes.project.tbm.common.model.TbmPropulsionSystemRing;

import java.util.List;

/**
 * <AUTHOR>
 * @version TbmPropulsionSystemRingService 2025/6/11 9:35
 */
public interface TbmPropulsionSystemRingService extends IService<TbmPropulsionSystemRing> {
    /**
     * 多条件查询
     * @param wrapper
     * @return
     */
    List<TbmPropulsionSystemRing> queryList(LambdaQueryWrapper<TbmPropulsionSystemRing> wrapper);

    /**
     * 批量新增
     * @param list
     * @return
     */
    Boolean addBatch(List<TbmPropulsionSystemRing> list);

    /**
     * 条件修改
     * @param wrapper
     * @return
     */
    Boolean edit(LambdaUpdateWrapper<TbmPropulsionSystemRing> wrapper);

}
