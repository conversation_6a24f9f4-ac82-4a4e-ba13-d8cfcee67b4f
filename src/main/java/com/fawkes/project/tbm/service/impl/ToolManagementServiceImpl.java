package com.fawkes.project.tbm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.core.exception.BusinessException;
import com.fawkes.project.tbm.client.StorageClient;
import com.fawkes.project.tbm.common.enums.DataSourceEnums;
import com.fawkes.project.tbm.common.mapper.DeviceManageMapper;
import com.fawkes.project.tbm.common.mapper.ToolManagementMapper;
import com.fawkes.project.tbm.common.mapper.ToolNameManageMapper;
import com.fawkes.project.tbm.common.mapper.ToolPositionManageMapper;
import com.fawkes.project.tbm.common.model.DeviceManage;
import com.fawkes.project.tbm.common.model.ToolManagement;
import com.fawkes.project.tbm.common.model.ToolNameManage;
import com.fawkes.project.tbm.common.model.ToolPositionManage;
import com.fawkes.project.tbm.common.param.*;
import com.fawkes.project.tbm.common.query.ToolManagementExportQuery;
import com.fawkes.project.tbm.common.tools.verify.ObjectValidator;
import com.fawkes.project.tbm.common.tools.verify.config.VerifyConfig;
import com.fawkes.project.tbm.common.tools.verify.domain.RowVerifyError;
import com.fawkes.project.tbm.common.utils.*;
import com.fawkes.project.tbm.common.utils.excel.ExcelUtil;
import com.fawkes.project.tbm.common.vo.*;
import com.fawkes.project.tbm.common.vo.app.ToolManagementAppVO;
import com.fawkes.project.tbm.service.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/6/3 18:02
 */
@Service
@Slf4j
public class ToolManagementServiceImpl extends ServiceImpl<ToolManagementMapper, ToolManagement> implements IToolManagementService {

    /**设备管理Mapper*/
    @Resource
    private DeviceManageMapper deviceManageMapper;

    /**刀具名称Mapper*/
    @Resource
    private ToolNameManageMapper toolNameManageMapper;

    @Resource
    private ToolPositionManageMapper toolPositionManageMapper;

    @Resource
    private ToolNameManageService toolNameManageService;

    @Resource
    private ToolPositionManageService toolPositionManageService;

    @Resource
    private DeviceManageService deviceManageService;

    @Resource
    private ToolManagementMapper toolManagementMapper;


    /**对象验证器*/
    @Resource
    private ObjectValidator objectValidator;

    /**方位只能字母、符号，不超过3位字符*/
    private static final String Orientation_REGEXP = "^[a-zA-Z~!@#$%^&*()_\\-=+<>?:'\",.{}\\[\\]|\\\\/;·~！@#￥%……&*（）——\\-+=|《》？：“”【】、；‘’，。、]{1,3}$";

    /**整数正则*/
    private static final String REGEXP_INTEGER = "^[0-9]*$";

    @Resource
    private StorageClient storageClient;

    @Resource
    private IOperationLogService operationLogService;

    private static final BigDecimal ZERO_DEFAULT = new BigDecimal("0.00");

    /**
     * @param sectionList     区段code集合
     * @param toolName        刀具名称
     * @param toolLocation    刀具位置
     * @param wearValue       磨损值
     * @param createName      填报人
     * @param createStartDate 填报开始时间
     * @param createEndDate   填报结束时间
     * @param pageNo          分页
     * @param pageSize        页大小
     * @return 列表
     */
    @Override
    public PageInfo<ToolManagementVO> page(List<String> sectionList, String toolName, String toolLocation, String wearValue, String createName,
                                           String createStartDate, String createEndDate, Integer pageNo, Integer pageSize) {
        LambdaQueryWrapper<ToolManagement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(ToolManagement::getUpdateDate)
                .eq(ToolManagement::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag());
        //通过道具名称模糊查询刀具id
        if (StringUtils.isNotEmpty(toolName)) {
            List<ToolNameManage> toolNameManages = toolNameManageMapper.selectList(
                    new LambdaQueryWrapper<>(ToolNameManage.class)
                            .like(ToolNameManage::getTooName, toolName)
                            .select(ToolNameManage::getId));
            if (ListUtil.isEmpty(toolNameManages)) {
                return new PageInfo<>();
            }
            List<Long> toolNameManageIdList = toolNameManages.stream().map(ToolNameManage::getId).collect(Collectors.toList());
            queryWrapper.in(ToolManagement::getToolNameManageId, toolNameManageIdList);
        }



        //通过刀具位置查询刀具位置id
        if (StringUtils.isNotBlank(toolLocation)) {
            List<ToolPositionManage> toolPositionManages = toolPositionManageMapper.selectList(
                    new LambdaQueryWrapper<>(ToolPositionManage.class)
                            .like(ToolPositionManage::getToolPosition, toolLocation)
                            .select(ToolPositionManage::getId));
            if (ListUtil.isEmpty(toolPositionManages)) {
                return new PageInfo<>();
            }
            List<Long> toolPositionManageIdList = toolPositionManages.stream().map(ToolPositionManage::getId).collect(Collectors.toList());
            queryWrapper.in(ToolManagement::getToolPositionManageId, toolPositionManageIdList);
        }

        if (CollectionUtils.isNotEmpty(sectionList)) {
            queryWrapper.in(ToolManagement::getSection, sectionList);
        }

        if (StringUtils.isNotEmpty(wearValue)) {
            try {
                BigDecimal wearValueDecimal = new BigDecimal(wearValue);
                queryWrapper.like(ToolManagement::getWearValue, wearValueDecimal);
            } catch (NumberFormatException e) {
                return ListUtil.emptyPage();
            }
        }

        if (StringUtils.isNotEmpty(createName)) {
            queryWrapper.like(ToolManagement::getUpdateName, createName);
        }

        if (StringUtils.isNotEmpty(createStartDate)) {
            queryWrapper.gt(ToolManagement::getUpdateDate, createStartDate);
        }

        if (StringUtils.isNotEmpty(createEndDate)) {
            queryWrapper.lt(ToolManagement::getUpdateDate, createEndDate);
        }
        PageHelper.startPage(pageNo, pageSize);
        log.info("最终 SQL 条件: {}", queryWrapper.getCustomSqlSegment());
        List<ToolManagement> toolManagementList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(toolManagementList)) {
            return new PageInfo<>();
        }
        PageInfo<ToolManagement> info = new PageInfo<>(toolManagementList);
        //(刀具id,刀具名称)
        List<Long> toolNameManageId = toolManagementList.stream().map(ToolManagement::getToolNameManageId).collect(Collectors.toList());
        Map<Long, String> toolNameMap = toolNameManageService.getToolNameMap(toolNameManageId);
        //(刀具位置id,刀具位置)
        List<Long> toolPositionManageId = toolManagementList.stream().map(ToolManagement::getToolPositionManageId).collect(Collectors.toList());
        Map<Long, String> toolPositionMap = toolPositionManageService.getToolPositionMap(toolPositionManageId);

        //(设备code,区段)
        List<String> deviceCodeList = toolManagementList.stream().map(ToolManagement::getSection).collect(Collectors.toList());
        Map<String, String> codeSectionMap = deviceManageService.getCodeSectionMap(deviceCodeList);

        List<ToolManagement> managementList = info.getList();
        List<ToolManagementVO> managementVOS = new ArrayList<>();

        for (ToolManagement toolManagement : managementList) {
            ToolManagementVO toolManagementVO = BeanUtil.toBean(toolManagement, ToolManagementVO.class);
            //根据id回显名称
            toolManagementVO.setToolName(toolNameMap.get(toolManagement.getToolNameManageId()));
            toolManagementVO.setToolLocation(toolPositionMap.get(toolManagement.getToolPositionManageId()));
            toolManagementVO.setSectionName(codeSectionMap.get(toolManagement.getSection()));
            if (StringUtils.isNotBlank(toolManagementVO.getChangeToolPicture())) {
                FileInfoParam fileInfoParam = new FileInfoParam();
                ArrayList<String> g9s = new ArrayList<>();
                g9s.add(toolManagementVO.getChangeToolPicture());
                fileInfoParam.setG9s(g9s);
                // 服务调用
                ApiResponseBody fileInfo = storageClient.file(fileInfoParam);
                if (fileInfo != null) {
                    List data = (List) fileInfo.getData();
                    if (data.size() > 0) {
                        Object object = data.get(0);
                        HashMap<String, Object> returnData = (HashMap<String, Object>) object;
                        String fileUrl = (String) returnData.get("fileToken");
                        toolManagementVO.setChangeToolPictureF8s(fileUrl);
                    }
                }
            }
            managementVOS.add(toolManagementVO);
        }

        PageInfo<ToolManagementVO> managementVOPageInfo = BeanUtil.toBean(info, PageInfo.class);
        managementVOPageInfo.setList(managementVOS);
        return managementVOPageInfo;
    }

    /**
     * 分页列表查询
     *
     * @param section 区段
     * @param searchValue 模糊查询（刀具名称、刀具位置、磨损值）值
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return
     */
    @Override
    public PageInfo<ToolManagementAppVO> pageApp(String section, String searchValue, Integer pageNo, Integer pageSize) {

        LambdaQueryWrapper<ToolManagement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(ToolManagement::getUpdateDate)
                .eq(ToolManagement::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag());

        List<Long> toolNameManageIdList = null;
        List<Long> toolPositionManageIdList = null;
        if (StringUtils.isNotEmpty(searchValue)) {
            //通过刀具名称模糊查询刀具id
            List<ToolNameManage> toolNameManages = toolNameManageMapper.selectList(Wrappers.lambdaQuery(ToolNameManage.class)
                    .select(ToolNameManage::getId)
                    .eq(ToolNameManage::getSection, section)
                    .like(ToolNameManage::getTooName, searchValue));

            Boolean orFlag = false;
            if (!ListUtil.isEmpty(toolNameManages)) {
                toolNameManageIdList = ListUtil.map(toolNameManages, ToolNameManage::getId);
                queryWrapper.in(ToolManagement::getToolNameManageId, toolNameManageIdList);
                orFlag = true;
            }

            //通过刀具位置查询刀具位置id
            List<ToolPositionManage> toolPositionManages = toolPositionManageMapper.selectList(
                    new LambdaQueryWrapper<>(ToolPositionManage.class)
                            .select(ToolPositionManage::getId)
                            .eq(ToolPositionManage::getSection, section)
                            .like(ToolPositionManage::getToolPosition, searchValue));
            if (!ListUtil.isEmpty(toolPositionManages)) {
                toolPositionManageIdList = toolPositionManages.stream().map(ToolPositionManage::getId).collect(Collectors.toList());
                if (orFlag){
                    queryWrapper.or().in(ToolManagement::getToolPositionManageId, toolPositionManageIdList);
                }else {
                    queryWrapper.in(ToolManagement::getToolPositionManageId, toolPositionManageIdList);
                }

                orFlag = true;
            }

            if (orFlag){
                queryWrapper.or().like(ToolManagement::getWearValue, searchValue);
             }else {
                queryWrapper.like(ToolManagement::getWearValue, searchValue);
            }
        }

        if (StringUtils.isNotBlank(section)) {
            queryWrapper.eq(ToolManagement::getSection, section);
        }

        PageHelper.startPage(pageNo, pageSize);
        log.info("最终 SQL1 条件: {}", queryWrapper.getCustomSqlSegment());
        List<ToolManagement> toolManagementList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(toolManagementList)) {
            return new PageInfo<>();
        }
        PageInfo<ToolManagement> info = new PageInfo<>(toolManagementList);
        //(刀具id,刀具名称)
        List<Long> toolNameManageId = toolManagementList.stream().map(ToolManagement::getToolNameManageId).collect(Collectors.toList());
        Map<Long, String> toolNameMap = toolNameManageService.getToolNameMap(toolNameManageId);
        //(刀具位置id,刀具位置)
        List<Long> toolPositionManageId = toolManagementList.stream().map(ToolManagement::getToolPositionManageId).collect(Collectors.toList());
        Map<Long, String> toolPositionMap = toolPositionManageService.getToolPositionMap(toolPositionManageId);

        //(设备code,区段)
        List<String> deviceCodeList = toolManagementList.stream().map(ToolManagement::getSection).collect(Collectors.toList());
        Map<String, String> codeSectionMap = deviceManageService.getCodeSectionMap(deviceCodeList);

        List<ToolManagement> managementList = info.getList();
        List<ToolManagementAppVO> managementVOS = new ArrayList<>();

        for (ToolManagement toolManagement : managementList) {
            ToolManagementAppVO toolManagementAppVO = BeanUtil.toBean(toolManagement, ToolManagementAppVO.class);
            //根据id回显名称
            toolManagementAppVO.setToolName(toolNameMap.get(toolManagement.getToolNameManageId()));
            toolManagementAppVO.setToolLocation(toolPositionMap.get(toolManagement.getToolPositionManageId()));
            toolManagementAppVO.setSectionName(codeSectionMap.get(toolManagement.getSection()));
            managementVOS.add(toolManagementAppVO);
        }

        PageInfo<ToolManagementAppVO> managementAppVOPageInfo = BeanUtil.toBean(info, PageInfo.class);
        managementAppVOPageInfo.setList(managementVOS);
        return managementAppVOPageInfo;
    }

    /**
     * 新增
     *
     * @param param 新增刀具
     * @return true成功/false失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(ToolManagementParam param) throws IllegalAccessException {
        if (param.getId() != null) {
            ToolManagement byId = super.getById(param.getId());
            if (byId == null) {
                throw new BusinessException("该数据已被删除");
            }
        }
        String section = param.getSection();
        //判断区段在不在数据库中
        DeviceManage deviceManage = deviceManageMapper.selectOne(new LambdaQueryWrapper<>(DeviceManage.class)
                .eq(DeviceManage::getCode, section)
                .eq(DeviceManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .last("limit 1"));
        if (Objects.isNull(deviceManage)) {
            throw new BusinessException("区段不存在");
        }

        //刀具是否存在
        Long toolNameManageId = param.getToolNameManageId();
        ToolNameManage toolNameManage = toolNameManageMapper.selectOne(new LambdaQueryWrapper<>(ToolNameManage.class)
                .eq(ToolNameManage::getId, toolNameManageId)
                .eq(ToolNameManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .last("limit 1"));
        if (Objects.isNull(toolNameManage)) {
            throw new BusinessException("刀具不存在");
        }
        if (!section.equals(toolNameManage.getSection())) {
            throw new BusinessException("刀具与区段不匹配");
        }

        Long toolPositionManageId = param.getToolPositionManageId();
        ToolPositionManage toolPositionManage = toolPositionManageMapper.selectOne(new LambdaQueryWrapper<>(ToolPositionManage.class)
                .eq(ToolPositionManage::getId, toolPositionManageId)
                .eq(ToolPositionManage::getDeleteFlag, DeleteFlagEnum.DATA_OK.getFlag())
                .last("limit 1"));
        if (Objects.isNull(toolPositionManage)) {
            throw new BusinessException("刀具位置不存在");
        }
        if (!section.equals(toolPositionManage.getSection()) || !toolNameManageId.equals(toolPositionManage.getToolNameManageId())) {
            throw new BusinessException("刀具位置与刀具不匹配");
        }

        //里程格式校验
        String startOrientation = param.getStartOrientation();
        if (StringUtils.isBlank(startOrientation) || !startOrientation.matches(Orientation_REGEXP)) {
            throw new BusinessException("开始方位格式错误");
        }
        String endOrientation = param.getEndOrientation();
        if (StringUtils.isBlank(endOrientation) || !endOrientation.matches(Orientation_REGEXP)) {
            throw new BusinessException("结束方位格式错误");
        }


        String newlyInstalledCuttingToolsStatus = param.getNewlyInstalledCuttingToolsStatus();
        if (StringUtils.isNotBlank(newlyInstalledCuttingToolsStatus) && newlyInstalledCuttingToolsStatus.length() > 200) {
            throw new BusinessException("新状刀具状态参数错误");
        }

        //换刀原因
        String changeToolReason = param.getChangeToolReason();
        if (StringUtils.isNotBlank(changeToolReason)) {
            if (changeToolReason.length() > 200) {
                throw new BusinessException("换刀原因参数错误");
            }
            param.setHasToolChanged(Boolean.TRUE);
        } else {
            param.setHasToolChanged(Boolean.FALSE);
        }
        ToolManagement toolManagement = BeanUtil.toBean(param, ToolManagement.class);
        Long id = toolManagement.getId();
        //新增
        if (id == null) {
            MyEntityTool.insertEntityWithNameSilent(toolManagement);
            boolean result = this.save(toolManagement);
            if (result) {
                // 记录新增操作
                operationLogService.recordToolManagementAddOperation(toolManagement, param.getDataSource());
            }
            return result;
        }
        //编辑
        ToolManagement oldToolManagement = super.getById(id);
        MyEntityTool.updateEntityWithNameSilent(toolManagement);
        boolean result = this.updateById(toolManagement);
        if (result) {
            // 记录修改操作
            operationLogService.recordToolManagementUpdateOperation(oldToolManagement, toolManagement, param.getDataSource());
        }
        return result;
    }

    /**
     * 详情
     *
     * @param id 刀具id
     * @return 刀具详情
     */
    @Override
    public ToolManagementVO getById(String id) {
        ToolManagement toolManagement = super.getById(Long.valueOf(id));
        if (toolManagement == null) {
            return null;
        }
        //(刀具id,刀具名称)
        Map<Long, String> toolNameMap = toolNameManageService.getToolNameMap(Collections.singletonList(toolManagement.getToolNameManageId()));
        //(刀具位置id,刀具位置)
        Map<Long, String> toolPositionMap = toolPositionManageService.getToolPositionMap(Collections.singletonList(toolManagement.getToolPositionManageId()));
        //(设备code,区段)
        Map<String, String> codeSectionMap = deviceManageService.getCodeSectionMap(Collections.singletonList(toolManagement.getSection()));
        ToolManagementVO toolManagementVO = BeanUtil.copyProperties(toolManagement, ToolManagementVO.class);
        toolManagementVO.setToolName(toolNameMap.get(toolManagement.getToolNameManageId()));
        toolManagementVO.setToolLocation(toolPositionMap.get(toolManagement.getToolPositionManageId()));
        toolManagementVO.setSectionName(codeSectionMap.get(toolManagement.getSection()));
        return toolManagementVO;
    }


    /**
     * 删除
     *
     * @param id 刀具id
     * @return true成功/false失败
     * @throws IllegalAccessException 填充异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(String id, Integer dataSource) throws IllegalAccessException {
        List<ToolManagement> toolManagementList = this.listByIds(Collections.singletonList(Long.valueOf(id)));
        if (ListUtil.isEmpty(toolManagementList)) {
            return Boolean.TRUE;
        }
        for (ToolManagement toolManagement : toolManagementList) {
            toolManagement.setDeleteFlag(DeleteFlagEnum.DATA_WARN.getFlag());
            MyEntityTool.updateEntityWithNameSilent(toolManagement);
        }
        boolean result = this.updateBatchById(toolManagementList);
        if (result) {
            // 记录删除操作
            for (ToolManagement toolManagement : toolManagementList) {
                operationLogService.recordToolManagementDeleteOperation(toolManagement, dataSource);
            }
        }
        return result;
    }

    /**
     * 导入
     *
     * @return 导入结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ToolManagementImportResult importExcel(MultipartFile file, HttpServletResponse response) throws IOException {
        InputStream inputStream = file.getInputStream();
        List<ToolManagementAddImportVO> excelList = EasyExcel.read(inputStream).head(ToolManagementAddImportVO.class).sheet(0).doReadSync();
        if (CollectionUtils.isEmpty(excelList)) {
            throw new BusinessException("excel文件读取错误！请检查文件");
        }

        ListUtil.forI(excelList, (index, vo) -> vo.setSerialNumber(index + 1));

        VerifyConfig<ToolManagementAddImportVO> verifyConfig = ToolManagementAddImportVO.getVerifyConfig();
        List<RowVerifyError<ToolManagementAddImportVO>> verifyErrorList = this.objectValidator.verifyImportList(excelList, verifyConfig);
        excelList = RowVerifyError.filterErrorData(excelList, verifyErrorList);
        if (ListUtil.isEmpty(excelList)) {
            return new ToolManagementImportResult(verifyErrorList);
        }

        for (ToolManagementAddImportVO toolManagementAddImportVO : excelList) {
            List<String> errorMsgList = getErrorMsgList(toolManagementAddImportVO);

            if (CollectionUtils.isNotEmpty(errorMsgList)) {
                RowVerifyError<ToolManagementAddImportVO> verifyError = new RowVerifyError<>();
                verifyError.setRowNo(toolManagementAddImportVO.getSerialNumber());
                verifyError.setErrorObject(toolManagementAddImportVO);
                verifyError.setErrorMsgList(errorMsgList);
                verifyErrorList.add(verifyError);
            }
        }

        excelList = RowVerifyError.filterErrorData(excelList, verifyErrorList);
        if (ListUtil.isEmpty(excelList)) {
            return new ToolManagementImportResult(verifyErrorList);
        }

        ToolManagementImportResult result = new ToolManagementImportResult();
        result.setErrorList(verifyErrorList);
        List<ToolManagementVO> processManagementVOList = BeanUtil.copyToList(excelList, ToolManagementVO.class);
        result.setRight(processManagementVOList);

        if (CollectionUtils.isNotEmpty(result.getError())) {
            return result;
        }

        //数据入库
        List<ToolManagement> toolManagementList = BeanUtil.copyToList(excelList, ToolManagement.class);
        //公共字段填充
        toolManagementList.forEach(MyEntityTool::insertEntityWithNameSilent);
        boolean saveOrUpdateBatch = this.saveOrUpdateBatch(toolManagementList);
        log.info("function=insertEntity, saveOrUpdateBatch:{}", saveOrUpdateBatch);

        if (saveOrUpdateBatch) {
            for (ToolManagement toolManagement : toolManagementList) {
                // 记录新增操作
                operationLogService.recordToolManagementAddOperation(toolManagement, DataSourceEnums.PC.getCode());
            }
        }
        return result;
    }

    /**
     * 导入校验
     *
     * @param toolManagementAddImportVO 导入数据
     * @return 错误数据
     */
    private List<String> getErrorMsgList(ToolManagementAddImportVO toolManagementAddImportVO) {
        List<String> errorMsgList = new ArrayList<>();
        String sectionName = toolManagementAddImportVO.getSectionName();
        if (StringUtils.isBlank(sectionName)) {
            errorMsgList.add("区段不能为空");
        } else {
            DeviceManage deviceManage = deviceManageMapper.selectOne(
                    new LambdaQueryWrapper<>(DeviceManage.class)
                            .eq(DeviceManage::getSection, sectionName)
                            .last("limit 1"));
            if (deviceManage == null) {
                errorMsgList.add("区段不存在");
            } else {
                toolManagementAddImportVO.setSection(deviceManage.getCode());
            }
        }

        String ringNum = toolManagementAddImportVO.getRingNum();
        if (StringUtils.isBlank(ringNum)) {
            errorMsgList.add("环号不能为空");
        } else {
            if (!ringNum.matches(REGEXP_INTEGER) || ringNum.length() > 8) {
                errorMsgList.add("环号数据格式不正确");
            }
        }

        //里程格式校验
        String startOrientation = toolManagementAddImportVO.getStartOrientation();
        if (StringUtils.isBlank(startOrientation)) {
            errorMsgList.add("开始方位不能为空");
        } else {
            if (!startOrientation.matches(Orientation_REGEXP)) {
                errorMsgList.add("开始方位格式错误");
            }
        }

        String startMileage = toolManagementAddImportVO.getStartMileage();
        if (StringUtils.isBlank(startMileage)) {
            errorMsgList.add("开始里程不能为空");
        } else {
            if (!getRegexpIntegerDecimalMatchResult(startMileage, 12, 3)) {
                errorMsgList.add("开始里程数据格式不正确");
            }
        }

        String endOrientation = toolManagementAddImportVO.getEndOrientation();
        if (StringUtils.isBlank(endOrientation)) {
            errorMsgList.add("结束方位不能为空");
        } else {
            if (!endOrientation.matches(Orientation_REGEXP)) {
                errorMsgList.add("结束方位格式错误");
            }
        }

        String endMileage = toolManagementAddImportVO.getEndMileage();
        if (StringUtils.isBlank(endMileage)) {
            errorMsgList.add("结束里程不能为空");
        } else {
            if (!getRegexpIntegerDecimalMatchResult(endMileage, 12, 3)) {
                errorMsgList.add("结束里程数据格式不正确");
            }
        }

        String openWarehouseStartTime = toolManagementAddImportVO.getOpenWarehouseStartTime();
        Date openWarehouseStart = null;
        if (StringUtils.isBlank(openWarehouseStartTime)) {
            errorMsgList.add("开仓开始时间不能为空");
        } else {
            openWarehouseStart = getTimeFormatResult(openWarehouseStartTime);
            if (openWarehouseStart == null) {
                errorMsgList.add("开仓开始时间格式不正确,应为yyyy-MM-dd HH:mm");
            }
        }


        String openWarehouseEndTime = toolManagementAddImportVO.getOpenWarehouseEndTime();
        Date openWarehouseEnd = null;
        if (StringUtils.isBlank(openWarehouseEndTime)) {
            errorMsgList.add("开仓结束时间不能为空");
        } else {
            openWarehouseEnd = getTimeFormatResult(openWarehouseEndTime);
            if (openWarehouseEnd == null) {
                errorMsgList.add("开仓结束时间格式不正确,应为yyyy-MM-dd HH:mm");
            }
        }

        if (openWarehouseStart != null && openWarehouseEnd != null && openWarehouseStart.after(openWarehouseEnd)) {
            errorMsgList.add("开仓开始时间不能大于结束时间");
        }

        String toolName = toolManagementAddImportVO.getToolName();
        if (StringUtils.isBlank(toolName)) {
            errorMsgList.add("刀具名称不能为空");
        } else {
            ToolNameManage toolNameManage = toolNameManageMapper.selectOne(
                    new LambdaQueryWrapper<>(ToolNameManage.class)
                            .eq(ToolNameManage::getTooName, toolName)
                            .last("limit 1"));
            if (toolNameManage == null) {
                errorMsgList.add("刀具名称不存在");
            } else {
                if (!toolNameManage.getSection().equals(toolManagementAddImportVO.getSection())) {
                    errorMsgList.add("刀具名称与区段不匹配");
                } else {
                    toolManagementAddImportVO.setToolNameManageId(toolNameManage.getId());
                }
            }
        }

        String toolLocation = toolManagementAddImportVO.getToolLocation();
        if (StringUtils.isBlank(toolLocation)) {
            errorMsgList.add("刀具位置不能为空");
        } else {
            ToolPositionManage toolPositionManage = toolPositionManageMapper.selectOne(
                    new LambdaQueryWrapper<>(ToolPositionManage.class)
                            .eq(ToolPositionManage::getToolPosition, toolLocation)
                            .last("limit 1"));
            if (toolPositionManage == null) {
                errorMsgList.add("刀具位置不存在");
            } else {
                if (!toolPositionManage.getSection().equals(toolManagementAddImportVO.getSection()) ||
                        !toolPositionManage.getToolNameManageId().equals(toolManagementAddImportVO.getToolNameManageId())) {
                    errorMsgList.add("刀具位置与区段或刀具不匹配");
                } else {
                    toolManagementAddImportVO.setToolPositionManageId(toolPositionManage.getId());
                }
            }
        }

        String changeThreshold = toolManagementAddImportVO.getChangeThreshold();
        if (StringUtils.isBlank(changeThreshold)) {
            errorMsgList.add("更换阈值不能为空");
        } else {
            if (!getRegexpIntegerDecimalMatchResult(changeThreshold, 8, 3)) {
                errorMsgList.add("更换阈值数据格式不正确");
            }
        }

        String wearValue = toolManagementAddImportVO.getWearValue();
        if (StringUtils.isBlank(wearValue)) {
            errorMsgList.add("磨损值不能为空");
        } else {
            if (!getRegexpIntegerDecimalMatchResult(wearValue, 5, 3)) {
                errorMsgList.add("磨损值数据格式不正确");
            }
        }
        String changeToolReason = toolManagementAddImportVO.getChangeToolReason();
        if (StringUtils.isNotBlank(changeToolReason) && changeToolReason.length() > 200) {
            errorMsgList.add("换刀原因不能超过两百字");
        }
        if (StringUtils.isNotBlank(changeToolReason) && changeToolReason.length() <= 200) {
            toolManagementAddImportVO.setHasToolChanged(Boolean.TRUE);
        } else {
            toolManagementAddImportVO.setHasToolChanged(Boolean.FALSE);
        }
        String newlyInstalledCuttingToolsStatus = toolManagementAddImportVO.getNewlyInstalledCuttingToolsStatus();
        if (StringUtils.isNotBlank(newlyInstalledCuttingToolsStatus) && newlyInstalledCuttingToolsStatus.length() > 200) {
            errorMsgList.add("新装刀具状态不能超过两百字");
        }
        return errorMsgList;
    }

    /**
     * 判断输入文本是否是日期格式的数据
     *
     * @param input 校验文本
     * @return 转换结果
     */
    private Date getTimeFormatResult(String input) {
        if (StringUtils.isBlank(input)) {
            return null;
        }

        try {
            return DateUtils.dateTime("yyyy-MM-dd HH:mm", input);
        } catch (Exception e) {
            log.error("getTimeFormatResult 时间格式转换异常：{}", e);
            return null;
        }
    }

    /**
     * 获取整数、小数 匹配结果
     *
     * @param input         匹配内容
     * @param integerDigits 整数位数
     * @param decimalDigits 小数位数
     * @return true成功/false失败
     */
    private Boolean getRegexpIntegerDecimalMatchResult(String input, int integerDigits, int decimalDigits) {
        if (StringUtils.isBlank(input)) {
            return false;
        }

        String regex = "[+]?\\d{1," + integerDigits + "}(\\.\\d{1," + decimalDigits + "})?";
        return input.matches(regex);
    }


    /**
     * 导出
     *
     * @param query    刀具管理id
     * @param response 响应流
     */
    @Override
    public void export(ToolManagementExportQuery query, HttpServletResponse response) throws IOException {
        List<Long> exportIdList = query.getExportIdList();
        List<ToolManagement> toolManagementList = super.list(new LambdaQueryWrapper<>(ToolManagement.class)
                .in(ToolManagement::getId, exportIdList)
                .orderByDesc(ToolManagement::getUpdateDate)
                .orderByDesc(ToolManagement::getId));
        if (ListUtil.isEmpty(toolManagementList)) {
            throw new BusinessException("请选择导出数据");
        }


        List<ToolManagementTempExportVO> toolManagementTempExportVOS = BeanUtil.copyToList(toolManagementList, ToolManagementTempExportVO.class);

        //(刀具id,刀具名称)
        List<Long> toolNameManageId = toolManagementList.stream().map(ToolManagement::getToolNameManageId).collect(Collectors.toList());
        Map<Long, String> toolNameMap = toolNameManageService.getToolNameMap(toolNameManageId);
        //(刀具位置id,刀具位置)
        List<Long> toolPositionManageId = toolManagementList.stream().map(ToolManagement::getToolPositionManageId).collect(Collectors.toList());
        Map<Long, String> toolPositionMap = toolPositionManageService.getToolPositionMap(toolPositionManageId);

        //(设备code,区段)
        List<String> deviceCodeList = toolManagementList.stream().map(ToolManagement::getSection).collect(Collectors.toList());
        Map<String, String> codeSectionMap = deviceManageService.getCodeSectionMap(deviceCodeList);
        toolManagementTempExportVOS.forEach(exportVO -> {
            exportVO.setToolName(toolNameMap.get(exportVO.getToolNameManageId()));
            exportVO.setToolLocation(toolPositionMap.get(exportVO.getToolPositionManageId()));
            exportVO.setSectionName(codeSectionMap.get(exportVO.getSection()));
            exportVO.setMileage(formatMileage(exportVO.getStartOrientation(), exportVO.getStartMileage()) + "~" + formatMileage(exportVO.getEndOrientation(), exportVO.getEndMileage()));
            exportVO.setHasToolChangedValue(exportVO.isHasToolChanged() ? "是" : "否");
            LocalDateTime openWarehouseStartTime = exportVO.getOpenWarehouseStartTime();
            exportVO.setOpenWarehouseStartTimeDesc(DateUtils.format(openWarehouseStartTime, "yyyy-MM-dd HH:mm"));
            LocalDateTime openWarehouseEndTime = exportVO.getOpenWarehouseEndTime();
            exportVO.setOpenWarehouseEndTimeDesc(DateUtils.format(openWarehouseEndTime, "yyyy-MM-dd HH:mm"));
        });

        List<ToolManagementExportVO> toolManagementExportVOList = BeanUtil.copyToList(toolManagementTempExportVOS, ToolManagementExportVO.class);
        exportData(response, toolManagementExportVOList, "刀具管理");
    }

    /**
     * 格式化里程金额（保持原始小数位数）
     * @param prefix
     * @param mileage 里程值（BigDecimal）
     * @return 格式化后的字符串（如 "S1+023.500"）
     */
    private String formatMileage(String prefix, BigDecimal mileage) {
        // 1. 提取整数和小数部分
        String plainStr = mileage.toPlainString();

        // 2. 计算千位数和剩余部分
        BigDecimal thousand = new BigDecimal("1000");
        int thousandPart = mileage.divideToIntegralValue(thousand).intValue();
        BigDecimal remaining = mileage.remainder(thousand).abs();

        // 3. 格式化剩余部分（整数3位补零 + 原始小数）
        String remainingStr = String.format("%03d", remaining.intValue()) +
                (remaining.scale() > 0 ? plainStr.substring(plainStr.indexOf('.')) : "");

        return prefix + thousandPart + "+" + remainingStr;
    }

    /**
     * 导出数据
     *
     * @param response
     * @param toolManagementExportVOS
     * @throws IOException
     */
    private void exportData(HttpServletResponse response,
                            List<ToolManagementExportVO> toolManagementExportVOS,
                            String fileName) throws IOException {
        ExcelUtil<ToolManagementExportVO> util = new ExcelUtil<>(ToolManagementExportVO. class);
        util.exportExcel(response, toolManagementExportVOS, "刀具数据" );
//        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
//        response.setCharacterEncoding("utf-8");
//        response.setHeader("Content-disposition",
//                "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");
//
//        // 使用优化后的图片写入处理器
//        try (ServletOutputStream out = response.getOutputStream()) {
//            EasyExcel.write(out, ToolManagementExportVO.class)
//                    .excelType(ExcelTypeEnum.XLSX)
//                    .sheet("刀具数据")
//                    .doWrite(toolManagementExportVOS);
//        }
    }


    /**
     * 统计磨损值
     *
     * @param section              区域code
     * @param toolPositionManageId 刀具位置id
     * @return 磨损统计列表
     */
    @Override
    public List<ToolWearStatisticsVO> statisticsToolWear(String section, Long toolPositionManageId) {
        if (toolPositionManageId == null) {
            return Collections.emptyList();
        }
        List<ToolManagement> toolManagementList = super.list(new LambdaQueryWrapper<>(ToolManagement.class)
                .eq(ToolManagement::getSection, section)
                .eq(ToolManagement::getToolPositionManageId, toolPositionManageId)
                .orderByAsc(ToolManagement::getCreateDate)
                .orderByAsc(ToolManagement::getId));
        if (ListUtil.isEmpty(toolManagementList)) {
            return Collections.emptyList();
        }
        List<ToolWearStatisticsVO> voList = new ArrayList<>(toolManagementList.size());
        int currentRingNum = 0;
        for (ToolManagement toolManagement : toolManagementList) {
            ToolWearStatisticsVO vo = new ToolWearStatisticsVO();
            vo.setChangeToolNum(++currentRingNum);
            vo.setWearValue(toolManagement.getWearValue().toString());
            voList.add(vo);
        }
        return voList;
    }

    /**
     * 刀具全局展示
     *
     * @param section 区域code
     */
    @Override
    public ToolDisplayVO toolDisplay(String section) {
        ToolDisplayVO toolDisplay = toolManagementMapper.toolDisplay(section);
        if (StringUtils.isNotBlank(toolDisplay.getChangeToolTime())) {
            BigDecimal bigDecimal = new BigDecimal(toolDisplay.getChangeToolTime());
            toolDisplay.setChangeToolTime(NullMergeUtils.nullMerge(MathUtils.divideHalfUp(bigDecimal, new BigDecimal(3600)), BigDecimal.ZERO).toString());
        }
        List<ToolInfoParam> toolInfoParamList = toolManagementMapper.getPositionNum(section);
        List<ToolInfoParam> toolInfoList = toolManagementMapper.getToolInfo(section);
        Map<Long, ToolInfoParam> toolLocationMap = ListUtil.toMap(toolInfoParamList, ToolInfoParam::getId);
        toolInfoList.forEach(toolInfoParam -> {
            ToolInfoParam param = toolLocationMap.get(toolInfoParam.getId());
            if (param != null) {
                toolInfoParam.setChangeToolNum(param.getChangeToolNum());
                toolInfoParam.setToolLocation(param.getToolLocation());
                if (StringUtils.isNotBlank(toolInfoParam.getChangeToolPicture())) {
                    FileInfoParam fileInfoParam = new FileInfoParam();
                    ArrayList<String> g9s = new ArrayList<>();
                    g9s.add(toolInfoParam.getChangeToolPicture());
                    fileInfoParam.setG9s(g9s);
                    // 服务调用
                    ApiResponseBody fileInfo = storageClient.file(fileInfoParam);
                    if (fileInfo != null) {
                        List data = (List) fileInfo.getData();
                        if (data.size() > 0) {
                            Object object = data.get(0);
                            HashMap<String, Object> returnData = (HashMap<String, Object>) object;
                            String fileUrl = (String) returnData.get("fileToken");
                            toolInfoParam.setChangeToolPictureF8s(fileUrl);
                        }
                    }
                }
            }
        });
        toolDisplay.setToolInfo(toolInfoList);
        return toolDisplay;
    }

    /**
     * 刀具磨损比较
     *
     * @param section 区域code
     * @param groupId 组id
     * @return 磨损比较列表
     */
    @Override
    public List<ToolWearCompareVO> toolWearCompare(String section, Long groupId) {
        return baseMapper.selectToolWearCompareData(section, groupId);
    }

    /**
     * 刀具磨损预警
     */
    @Override
    public PageInfo<ToolWearEarlyWarningVO> toolWearEarlyWarning(String section, Integer pageNo, Integer pageSize) {
        if (pageNo != null) {
            PageHelper.startPage(pageNo, pageSize);
        }
        List<ToolWearEarlyParam> voList = toolManagementMapper.toolWearEarlyWarning(section);
        List<ToolWearEarlyWarningVO> list = new ArrayList<>();
        if (ListUtil.isNotEmpty(voList)) {
            List<Long> positionIdList = ListUtil.distinctMap(voList, ToolWearEarlyParam::getToolPositionManageId);
            List<ToolWearEarlyWarningVO> wearEarlyWarningList = toolManagementMapper.getCumulativeWearAndTear(section, positionIdList);
            Map<Long, ToolWearEarlyWarningVO> positionIdMap = ListUtil.toMap(wearEarlyWarningList, ToolWearEarlyWarningVO::getToolPositionManageId);
            for (ToolWearEarlyParam toolWearEarlyParam : voList) {
                ToolWearEarlyWarningVO toolWearEarlyWarningVO = positionIdMap.get(toolWearEarlyParam.getToolPositionManageId());
                if (toolWearEarlyWarningVO == null) {
                    toolWearEarlyWarningVO = new ToolWearEarlyWarningVO();
                }
                // 取true状态的磨损值和最近一次的磨损值
                if (!toolWearEarlyParam.getHasToolChanged()) {
                    BigDecimal cumulativeWearAndTear = MathUtils.add(toolWearEarlyWarningVO.getCumulativeWearAndTear(), toolWearEarlyParam.getWearValue());
                    toolWearEarlyWarningVO.setCumulativeWearAndTear(cumulativeWearAndTear);
                }
                // 公里磨损量
                BigDecimal kmOfWear = MathUtils.divideHalfUp(toolWearEarlyWarningVO.getCumulativeWearAndTear(), toolWearEarlyParam.getEndMileage());
                if (toolWearEarlyParam.getHasToolChanged()) {
                    toolWearEarlyParam.setWearValue(BigDecimal.ZERO);
                }
                BigDecimal subtract = MathUtils.subtract(toolWearEarlyParam.getChangeThreshold(), toolWearEarlyParam.getWearValue());
                BigDecimal divide = MathUtils.divideHalfUp(subtract, kmOfWear);
                ToolWearEarlyWarningVO vo = new ToolWearEarlyWarningVO();
                vo.setToolLocation(toolWearEarlyParam.getToolPosition());
                vo.setChangeToolPredictedMileage(divide == null ? ZERO_DEFAULT.toString() : divide.toString());
                list.add(vo);
            }
        }
        return ListUtil.pageConvert(voList, list);
    }

    /**
     * 刀具磨损规律
     *
     * @param section 区域code
     * @param groupId 刀具位置id
     * @return 磨损规律列表
     */
    @Override
    public List<ToolWearRuleVO> toolWearRule(String section, Long groupId) {
        //查询所有的刀具 最近一次磨损值,+最近一次的结束里程
        List<ToolWearRuleVO> voList = this.toolManagementMapper.toolWearRule(section, groupId);
        if (ListUtil.isEmpty(voList)) {
            return Collections.emptyList();
        }
        List<Long> positionIdList = ListUtil.distinctMap(voList, ToolWearRuleVO::getToolPositionManageId);
        List<ToolWearEarlyWarningVO> wearEarlyWarningList = toolManagementMapper.getCumulativeWearAndTear(section, positionIdList);
        //获取刀具位置id对应的 cumulativeWearAndTear
        Map<Long, ToolWearEarlyWarningVO> positionIdMap = ListUtil.toMap(wearEarlyWarningList, ToolWearEarlyWarningVO::getToolPositionManageId);
        for (ToolWearRuleVO toolWearRuleVO : voList) {
            ToolWearEarlyWarningVO toolWearEarlyWarningVO = positionIdMap.get(toolWearRuleVO.getToolPositionManageId());
            //位置的累计磨损量=每次换刀时的磨损值之和(不含最后一次的所有换刀为是的磨损统计之和+最后一次换刀磨损)
            BigDecimal cumulativeWearAndTear;
            if (toolWearEarlyWarningVO == null) {
                //没有累计换刀给个0,有的话取过来
                cumulativeWearAndTear = BigDecimal.ZERO;
            } else {
                cumulativeWearAndTear = toolWearEarlyWarningVO.getCumulativeWearAndTear();
            }

            Boolean lastHasToolChanged = toolWearRuleVO.getLastHasToolChanged();
            //如果最后一次不是换刀的话,总数加上最后一次的磨损值
            if (BooleanUtils.isFalse(lastHasToolChanged)) {
                cumulativeWearAndTear = MathUtils.addIfNull(cumulativeWearAndTear, toolWearRuleVO.getLastWearValue());
            }

            cumulativeWearAndTear = cumulativeWearAndTear == null ? BigDecimal.ZERO : cumulativeWearAndTear;
            //每公里磨损量 = 累计磨损量/最后一次填报的里程
            BigDecimal everyKilometerWearValue = MathUtils.divideHalfUp(cumulativeWearAndTear, toolWearRuleVO.getLastEndMileage());
            toolWearRuleVO.setEveryKilometerWearValue(everyKilometerWearValue == null ? ZERO_DEFAULT : everyKilometerWearValue);
            toolWearRuleVO.setCumulativeWearValue(cumulativeWearAndTear);
        }
        return voList;
    }



}
