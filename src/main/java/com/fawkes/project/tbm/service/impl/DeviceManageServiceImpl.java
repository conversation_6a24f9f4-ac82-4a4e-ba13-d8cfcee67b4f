package com.fawkes.project.tbm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.core.exception.BusinessException;
import com.fawkes.project.tbm.common.mapper.DeviceManageMapper;
import com.fawkes.project.tbm.common.model.DeviceManage;
import com.fawkes.project.tbm.common.model.RiskWarning;
import com.fawkes.project.tbm.common.param.DeviceManageParam;
import com.fawkes.project.tbm.common.vo.DeviceManageVO;
import com.fawkes.project.tbm.service.DeviceManageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 设备管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Service
public class DeviceManageServiceImpl extends ServiceImpl<DeviceManageMapper, DeviceManage> implements DeviceManageService {

    @Override
    public List<DeviceManageVO> list(String code, String name) {
        List<DeviceManage> deviceManages = this.lambdaQuery().like(StringUtils.isNotBlank(code), DeviceManage::getCode, code)
                .like(StringUtils.isNotBlank(name), DeviceManage::getName, name)
                .orderByDesc(DeviceManage::getUpdateDate).list();
        List<DeviceManageVO> deviceManageVOS = BeanUtil.copyToList(deviceManages, DeviceManageVO.class);
        return deviceManageVOS;
    }

    @Override
    public void add(DeviceManageParam param) {
        //需要判断新编号是否已经被使用
        Integer count = this.lambdaQuery().eq(DeviceManage::getCode, param.getCode())
                .count();
        if (count > 0) {
            throw new BusinessException("该设备编号已存在，请勿重复");
        }
        String regex = "^[1-9]\\d{0,7}(\\.\\d{1,3})?$";
        if (!param.getTotalMileage().matches(regex)) {
            throw new BusinessException("掘进总里程输入正数，整数最大8位，小数最大2位");
        }
        DeviceManage deviceManage = BeanUtil.copyProperties(param, DeviceManage.class);
        this.save(deviceManage);
    }

    @Override
    public void update(DeviceManageParam param) {
        String regex = "^[1-9]\\d{0,7}(\\.\\d{1,3})?$";
        if (!param.getTotalMileage().matches(regex)) {
            throw new BusinessException("掘进总里程输入正数，整数最大8位，小数最大2位");
        }
        //需要判断新编号是否已经被使用
        DeviceManage byId = this.getById(param.getId());
        if (!byId.getCode().equals(param.getCode())) {
            Integer count = this.lambdaQuery().eq(DeviceManage::getCode, param.getCode())
                    .count();
            if (count > 0) {
                throw new BusinessException("该设备编号已存在，请勿重复");
            }
        }
        DeviceManage deviceManage = BeanUtil.copyProperties(param, DeviceManage.class);
        this.updateById(deviceManage);
    }

    @Override
    public boolean delete(String id) {
        return removeById(id);
    }

    @Override
    public DeviceManageVO get(String id) {
        DeviceManage deviceManage = this.getById(id);
        return BeanUtil.copyProperties(deviceManage, DeviceManageVO.class);
    }

    /**
     * 设备code查询code-区段
     * @param codeList 区段
     * @return (code,区段)
     */
    @Override
    public Map<String,String> getCodeSectionMap(List<String> codeList){
        List<DeviceManage> list1 = super.list(
                new LambdaQueryWrapper<>(DeviceManage.class)
                        .in(DeviceManage::getCode, codeList)
                        .select(DeviceManage::getCode, DeviceManage::getSection));
        return list1.stream().collect(Collectors.toMap(DeviceManage::getCode, DeviceManage::getSection));
    }
}
