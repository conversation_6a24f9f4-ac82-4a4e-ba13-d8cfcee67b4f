package com.fawkes.project.tbm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fawkes.project.tbm.common.constants.IOTDeviceConstants;
import com.fawkes.project.tbm.common.handler.MonthTableNameHandler;
import com.fawkes.project.tbm.common.handler.MonthTableNameHandler;
import com.fawkes.project.tbm.common.mapper.TbmDriveSystemMapper;
import com.fawkes.project.tbm.common.model.TbmDriveSystem;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.common.vo.IOTResponse;
import com.fawkes.project.tbm.common.utils.DateUtils;
import com.fawkes.project.tbm.common.vo.TbmDriveSystemXYVO;
import com.fawkes.project.tbm.service.TbmDriveSystemService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * TBM驱动系统参数(此表为原始表，数据按月分表) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Service
public class TbmDriveSystemServiceImpl extends ServiceImpl<TbmDriveSystemMapper, TbmDriveSystem> implements TbmDriveSystemService {

    @Resource
    private TbmDriveSystemMapper tbmDriveSystemMapper;


    @Override
    public TbmDriveSystem getLastTbmDriveSystem(String deviceCode) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        List<TbmDriveSystem> tbmDriveSystems = this.lambdaQuery()
                .eq(TbmDriveSystem::getCode, deviceCode)
                .orderByDesc(TbmDriveSystem::getCreateTime)
                .last("LIMIT  1").list();
        MonthTableNameHandler.removeMonthData();
        if (CollectionUtils.isNotEmpty(tbmDriveSystems)) {
            return tbmDriveSystems.get(0);
        }
        return null;

    }

    @Override
    public List<TbmDriveSystem> getLastTenRingNumTbmDriveSystem(String deviceCode, Integer ringNum) {
        Integer minRingNum = 0;
        if (ringNum - 9 > 0) {
            minRingNum = ringNum - 9;
        }
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());
        List<TbmDriveSystem> tbmDriveSystems = this.lambdaQuery().eq(TbmDriveSystem::getCode, deviceCode)
                .between(TbmDriveSystem::getRingNum, minRingNum, ringNum).list();
        MonthTableNameHandler.removeMonthData();
        return tbmDriveSystems;
    }

    /**
     * 获取最后十个环号对应的TbmDriveSystem对象
     * @param deviceCode 设备编码
     * @param ringNumList 环号
     * @param motorTemperature 电机温度
     * @param electricCurrent 电机电流
     */
    @Override
    public List<TbmDriveSystemXYVO> getRingNumTbmDriveSystem(String deviceCode, List<Integer> ringNumList, String motorTemperature, String electricCurrent) {
        String tableName = "tbm_drive_system_" + DateUtils.getYyyyMM();
        return tbmDriveSystemMapper.getRingNumTbmDriveSystem(deviceCode, ringNumList, motorTemperature, electricCurrent, tableName);
    }

    /**
     * 同步驱动系统
     * @param iotResponseList
     */
    @Override
    public Boolean syncTbmDriveSystem(List<IOTResponse> iotResponseList, String deviceCode) {
        MonthTableNameHandler.setMonthData(DateUtils.getYyyyMM());

        //电机总功率
        String power = getPropertyValue(iotResponseList, IOTDeviceConstants.POWER);

        //振动频率
        String frequency = getPropertyValue(iotResponseList, IOTDeviceConstants.FREQUENCY);

        //电机电流
        String electricCurrentOne = getPropertyValue(iotResponseList, IOTDeviceConstants.ELECTRIC_CURRENT_ONE);
        String electricCurrentTwo = getPropertyValue(iotResponseList, IOTDeviceConstants.ELECTRIC_CURRENT_TWO);
        String electricCurrentThree = getPropertyValue(iotResponseList, IOTDeviceConstants.ELECTRIC_CURRENT_THREE);
        String electricCurrentFour = getPropertyValue(iotResponseList, IOTDeviceConstants.ELECTRIC_CURRENT_FOUR);
        String electricCurrentFive = getPropertyValue(iotResponseList, IOTDeviceConstants.ELECTRIC_CURRENT_FIVE);
        String electricCurrentSix = getPropertyValue(iotResponseList, IOTDeviceConstants.ELECTRIC_CURRENT_SIX);
        String electricCurrentSeven = getPropertyValue(iotResponseList, IOTDeviceConstants.ELECTRIC_CURRENT_SEVEN);
        String electricCurrentEight = getPropertyValue(iotResponseList, IOTDeviceConstants.ELECTRIC_CURRENT_EIGHT);
        String electricCurrentNine = getPropertyValue(iotResponseList, IOTDeviceConstants.ELECTRIC_CURRENT_NINE);
        String electricCurrentTen = getPropertyValue(iotResponseList, IOTDeviceConstants.ELECTRIC_CURRENT_TEN);
        String electricCurrentEleven = getPropertyValue(iotResponseList, IOTDeviceConstants.ELECTRIC_CURRENT_ELEVEN);
        String electricCurrentTwelve = getPropertyValue(iotResponseList, IOTDeviceConstants.ELECTRIC_CURRENT_TWELVE);
        String electricCurrentThirteen = getPropertyValue(iotResponseList, IOTDeviceConstants.ELECTRIC_CURRENT_THIRTEEN);
        String electricCurrentFourteen = getPropertyValue(iotResponseList, IOTDeviceConstants.ELECTRIC_CURRENT_FOURTEEN);

        Integer ringNum = null;
        Optional<String> optionalR = iotResponseList.stream().filter(iotResponse -> iotResponse.getProperty()
                .equals(IOTDeviceConstants.RING_NUM)).map(IOTResponse::getFormatValue).findFirst();
        if (optionalR.isPresent()) {
            ringNum = Integer.valueOf(optionalR.get());
        }

        //点击温度
        String temperatureOne = getPropertyValue(iotResponseList, IOTDeviceConstants.TEMPERATURE_ONE);
        String temperatureTwo = getPropertyValue(iotResponseList, IOTDeviceConstants.TEMPERATURE_TWO);
        String temperatureThree = getPropertyValue(iotResponseList, IOTDeviceConstants.TEMPERATURE_THREE);
        String temperatureFour = getPropertyValue(iotResponseList, IOTDeviceConstants.TEMPERATURE_FOUR);
        String temperatureFive = getPropertyValue(iotResponseList, IOTDeviceConstants.TEMPERATURE_FIVE);
        String temperatureSix = getPropertyValue(iotResponseList, IOTDeviceConstants.TEMPERATURE_SIX);
        String temperatureSeven = getPropertyValue(iotResponseList, IOTDeviceConstants.TEMPERATURE_SEVEN);
        String temperatureEight = getPropertyValue(iotResponseList, IOTDeviceConstants.TEMPERATURE_EIGHT);
        String temperatureNine = getPropertyValue(iotResponseList, IOTDeviceConstants.TEMPERATURE_NINE);
        String temperatureTen = getPropertyValue(iotResponseList, IOTDeviceConstants.TEMPERATURE_TEN);
        String temperatureEleven = getPropertyValue(iotResponseList, IOTDeviceConstants.TEMPERATURE_ELEVEN);
        String temperatureTwelve = getPropertyValue(iotResponseList, IOTDeviceConstants.TEMPERATURE_TWELVE);
        String temperatureThirteen = getPropertyValue(iotResponseList, IOTDeviceConstants.TEMPERATURE_THIRTEEN);
        String temperatureFourteen = getPropertyValue(iotResponseList, IOTDeviceConstants.TEMPERATURE_FOURTEEN);

        TbmDriveSystem tbmDriveSystem = new TbmDriveSystem();
        tbmDriveSystem.setCode(deviceCode);
        tbmDriveSystem.setRingNum(ringNum);
        tbmDriveSystem.setPower(buildStringToBigDecimal(power));
        tbmDriveSystem.setFrequency(buildStringToBigDecimal(frequency));

        tbmDriveSystem.setElectricCurrentOne(buildStringToBigDecimal(electricCurrentOne));
        tbmDriveSystem.setElectricCurrentTwo(buildStringToBigDecimal(electricCurrentTwo));
        tbmDriveSystem.setElectricCurrentThree(buildStringToBigDecimal(electricCurrentThree));
        tbmDriveSystem.setElectricCurrentFour(buildStringToBigDecimal(electricCurrentFour));
        tbmDriveSystem.setElectricCurrentFive(buildStringToBigDecimal(electricCurrentFive));
        tbmDriveSystem.setElectricCurrentSix(buildStringToBigDecimal(electricCurrentSix));
        tbmDriveSystem.setElectricCurrentSeven(buildStringToBigDecimal(electricCurrentSeven));
        tbmDriveSystem.setElectricCurrentEight(buildStringToBigDecimal(electricCurrentEight));
        tbmDriveSystem.setElectricCurrentNine(buildStringToBigDecimal(electricCurrentNine));
        tbmDriveSystem.setElectricCurrentTen(buildStringToBigDecimal(electricCurrentTen));
        tbmDriveSystem.setElectricCurrentEleven(buildStringToBigDecimal(electricCurrentEleven));
        tbmDriveSystem.setElectricCurrentTwelve(buildStringToBigDecimal(electricCurrentTwelve));
        tbmDriveSystem.setElectricCurrentThirteen(buildStringToBigDecimal(electricCurrentThirteen));
        tbmDriveSystem.setElectricCurrentFourteen(buildStringToBigDecimal(electricCurrentFourteen));

        tbmDriveSystem.setTemperatureOne(buildStringToBigDecimal(temperatureOne));
        tbmDriveSystem.setTemperatureTwo(buildStringToBigDecimal(temperatureTwo));
        tbmDriveSystem.setTemperatureThree(buildStringToBigDecimal(temperatureThree));
        tbmDriveSystem.setTemperatureFour(buildStringToBigDecimal(temperatureFour));
        tbmDriveSystem.setTemperatureFive(buildStringToBigDecimal(temperatureFive));
        tbmDriveSystem.setTemperatureSix(buildStringToBigDecimal(temperatureSix));
        tbmDriveSystem.setTemperatureSeven(buildStringToBigDecimal(temperatureSeven));
        tbmDriveSystem.setTemperatureEight(buildStringToBigDecimal(temperatureEight));
        tbmDriveSystem.setTemperatureNine(buildStringToBigDecimal(temperatureNine));
        tbmDriveSystem.setTemperatureTen(buildStringToBigDecimal(temperatureTen));
        tbmDriveSystem.setTemperatureEleven(buildStringToBigDecimal(temperatureEleven));
        tbmDriveSystem.setTemperatureTwelve(buildStringToBigDecimal(temperatureTwelve));
        tbmDriveSystem.setTemperatureThirteen(buildStringToBigDecimal(temperatureThirteen));
        tbmDriveSystem.setTemperatureFourteen(buildStringToBigDecimal(temperatureFourteen));
        tbmDriveSystem.setCreateTime(new Timestamp(System.currentTimeMillis()));
        tbmDriveSystem.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        int insert = tbmDriveSystemMapper.insert(tbmDriveSystem);
        MonthTableNameHandler.removeMonthData();
        return insert > 0;
    }

    private String getPropertyValue(List<IOTResponse> iotResponseList, String propertyName) {
        return iotResponseList.stream()
                .filter(iotResponse -> iotResponse.getProperty().equals(propertyName))
                .map(IOTResponse::getFormatValue)
                .findFirst()
                .orElse(null);
    }

    private BigDecimal buildStringToBigDecimal(String value) {
        if (value == null) {
            return null;
        }
        return new BigDecimal(value);
    }
}
