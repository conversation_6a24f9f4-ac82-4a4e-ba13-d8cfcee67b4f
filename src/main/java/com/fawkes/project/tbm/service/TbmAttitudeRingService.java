package com.fawkes.project.tbm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fawkes.project.tbm.common.model.TbmAttitudeRing;
import com.fawkes.project.tbm.common.model.TbmCutDiskRing;

import java.util.List;

/**
 * <AUTHOR>
 * @version TbmAttitudeRingService 2025/6/12 9:58
 */
public interface TbmAttitudeRingService extends IService<TbmAttitudeRing> {

    /**
     * 多条件查询
     * @param wrapper
     * @return
     */
    List<TbmAttitudeRing> queryList(LambdaQueryWrapper<TbmAttitudeRing> wrapper);

    /**
     * 批量新增
     * @param list
     * @return
     */
    Boolean addBatch(List<TbmAttitudeRing> list);

    /**
     * 条件修改
     * @param wrapper
     * @return
     */
    Boolean edit(LambdaUpdateWrapper<TbmAttitudeRing> wrapper);

}
