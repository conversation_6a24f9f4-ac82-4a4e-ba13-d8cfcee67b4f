package com.fawkes.project.tbm.controllerapp;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.enums.DataSourceEnums;
import com.fawkes.project.tbm.common.param.ProcessManagementParam;
import com.fawkes.project.tbm.common.vo.ProcessManagementVO;
import com.fawkes.project.tbm.common.vo.app.ProcessManagementAppVO;
import com.fawkes.project.tbm.service.IProcessManagementService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 工序管理-app端
 * <AUTHOR>
 * @Description
 * @Date 2025/6/3 18:04
 */
@Api(tags = {"APP工序管理"})
@RestController
@RequestMapping("/app/processManagement")
public class AppProcessManagementController {

    @Resource
    private IProcessManagementService iProcessManagementService;

    /**
     * 分页列表查询
     *
     * @param section 区段
     * @param searchValue 模糊查询（刀具名称、刀具位置、磨损值）值
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return
     */
    @ApiOperation(value = "分页列表查询", notes = "分页列表查询")
    @GetMapping(value = "/page")
    public ApiResponseBody<PageInfo<ProcessManagementAppVO>> page(@RequestParam(name = "section") String section,
                                                                  @RequestParam(name = "searchValue", required = false) String searchValue,
                                                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        return ApiResponseBody.defaultSuccess(iProcessManagementService.pageApp(section, searchValue, pageNo, pageSize));
    }

    /**
     * 新增数据
     */
    @ApiOperation("新增")
    @PostMapping("/add")
    public ApiResponseBody<Boolean> add(@RequestBody @Valid ProcessManagementParam param) throws IllegalAccessException {
        param.setDataSource(DataSourceEnums.APP.getCode());
        return ApiResponseBody.defaultSuccess(iProcessManagementService.add(param));
    }


    /**
     * 详情
     */
    @ApiOperation("详情")
    @GetMapping("/get")
    public ApiResponseBody<ProcessManagementVO> get(@RequestParam(name = "id") String id) {
        return ApiResponseBody.defaultSuccess(iProcessManagementService.get(id));
    }


    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping(path = "/delete", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<Boolean> delete(@RequestParam String id) throws IllegalAccessException {
        return ApiResponseBody.defaultSuccess(iProcessManagementService.delete(id, DataSourceEnums.APP.getCode()));
    }

}
