package com.fawkes.project.tbm.controllerapp;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.common.enums.DataSourceEnums;
import com.fawkes.project.tbm.common.param.ToolManagementParam;
import com.fawkes.project.tbm.common.vo.*;
import com.fawkes.project.tbm.common.vo.app.ToolManagementAppVO;
import com.fawkes.project.tbm.service.IToolManagementService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 刀具管理-app端
 * <AUTHOR>
 * @Description
 * @Date 2025/6/3 18:04
 */
@Api(tags = {"APP刀具管理"})
@RestController
@RequestMapping("/app/toolManagement")
public class AppToolManagementController {

    @Resource
    private IToolManagementService iToolManagementService;

    /**
     * 分页列表查询
     *
     * @param section 区段
     * @param searchValue 模糊查询（刀具名称、刀具位置、磨损值）值
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return
     */
    @ApiOperation(value = "分页列表查询", notes = "分页列表查询")
    @GetMapping(value = "/page")
    public ApiResponseBody<PageInfo<ToolManagementAppVO>> page(@RequestParam(name = "section") String section,
                                                               @RequestParam(name = "searchValue", required = false) String searchValue,
                                                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        return ApiResponseBody.defaultSuccess(iToolManagementService.pageApp(section, searchValue, pageNo, pageSize));
    }

    /**
     * 新增数据
     */
    @ApiOperation("新增")
    @PostMapping("/add")
    public ApiResponseBody<Boolean> add(@RequestBody @Valid ToolManagementParam param) throws IllegalAccessException {
        param.setDataSource(DataSourceEnums.APP.getCode());
        return ApiResponseBody.defaultSuccess(iToolManagementService.add(param));
    }


    /**
     * 详情
     */
    @ApiOperation("详情")
    @GetMapping("/get")
    public ApiResponseBody<ToolManagementVO> get(@RequestParam(name = "id") String id) {
        return ApiResponseBody.defaultSuccess(iToolManagementService.getById(id));
    }


    /**
     * 删除
     */
    @ApiOperation("删除")
    @DeleteMapping(path = "/delete", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<Boolean> delete(@RequestParam String id) throws IllegalAccessException {
        return ApiResponseBody.defaultSuccess(iToolManagementService.delete(id, DataSourceEnums.APP.getCode()));
    }

}
