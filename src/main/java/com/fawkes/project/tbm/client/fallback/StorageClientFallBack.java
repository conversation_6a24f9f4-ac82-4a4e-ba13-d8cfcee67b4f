package com.fawkes.project.tbm.client.fallback;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.enums.BizCodeMsgEnum;
import com.fawkes.core.pojo.query.GetFileInfoParam;
import com.fawkes.project.tbm.client.StorageClient;
import com.fawkes.project.tbm.common.param.FileInfoParam;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * @author: wuyunchao
 * @date : 2020-11-26
 **/
@Component
public class StorageClientFallBack implements StorageClient {
    @Override
    public ApiResponseBody upload(MultipartFile file, String g9s) {
        return ApiResponseBody.error(BizCodeMsgEnum.SYS_ERROR);
    }

    @Override
    public ResponseEntity<Resource> download(String f8s) throws IOException {

        return null;
    }

    @Override
    public ApiResponseBody file(FileInfoParam f8s) {
        return null;
    }

    @Override
    public ApiResponseBody getFileByToken(GetFileInfoParam getFileInfoParam) {
        return ApiResponseBody.error(BizCodeMsgEnum.SYS_ERROR);
    }
}