package com.fawkes.project.tbm.client.fallback;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.enums.BizCodeMsgEnum;
import com.fawkes.project.tbm.client.IUserClient;
import com.fawkes.project.tbm.common.model.SysUser;
import com.fawkes.project.tbm.common.param.CreateUserParam;
import com.fawkes.project.tbm.common.param.UpdatePortalParam;
import com.fawkes.project.tbm.common.param.UpdateUserParam;
import com.fawkes.project.tbm.common.vo.UserRolesVO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

/**
 * <AUTHOR>
 */
@Component
public class UserClientFallBack implements IUserClient {

    @Override
    public ApiResponseBody<CreateUserParam> createUser(CreateUserParam createUserParam) {
        return ApiResponseBody.error(BizCodeMsgEnum.SYS_ERROR);
    }

    @Override
    public ApiResponseBody updatePortal(UpdatePortalParam updatePortalParam) {
        return ApiResponseBody.error(BizCodeMsgEnum.SYS_ERROR);
    }

    @Override
    public ApiResponseBody<UpdateUserParam> updateUser(UpdateUserParam updateUserParam) {
        return ApiResponseBody.error(BizCodeMsgEnum.SYS_ERROR);
    }

    @Override
    public ApiResponseBody<ArrayList<UserRolesVO>> listUserRoles(String id, String portalId) {
        return ApiResponseBody.error(BizCodeMsgEnum.SYS_ERROR);
    }

    @Override
    public ApiResponseBody<SysUser> getUser(String clientId, String userName) {
        return ApiResponseBody.error(BizCodeMsgEnum.SYS_ERROR);
    }
}
