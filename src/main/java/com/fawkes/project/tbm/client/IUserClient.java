package com.fawkes.project.tbm.client;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.tbm.client.fallback.UserClientFallBack;
import com.fawkes.project.tbm.common.model.SysUser;
import com.fawkes.project.tbm.common.param.CreateUserParam;
import com.fawkes.project.tbm.common.param.UpdatePortalParam;
import com.fawkes.project.tbm.common.param.UpdateUserParam;
import com.fawkes.project.tbm.common.vo.UserRolesVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;

/**
 * <AUTHOR>
 */
@FeignClient(
        name = "sys-user",
        fallback = UserClientFallBack.class
//        ,url = "http://10.95.101.23:18200/api/sys-user/"
)
public interface IUserClient {

    @ApiOperation("新增用户")
    @PostMapping(
            path = {"/user"},
            produces = {"application/json;charset=UTF-8"}
    )
    ApiResponseBody<CreateUserParam> createUser(@RequestBody CreateUserParam createUserParam);

    @ApiOperation("更新门户")
    @PutMapping(
            path = {"/portal"},
            produces = {"application/json;charset=UTF-8"}
    )
    ApiResponseBody updatePortal(@RequestBody UpdatePortalParam updatePortalParam);

    @ApiOperation("更新用户")
    @PutMapping(
            path = {"/user"},
            produces = {"application/json;charset=UTF-8"}
    )
    ApiResponseBody<UpdateUserParam> updateUser(@RequestBody UpdateUserParam updateUserParam);

    @ApiOperation("获取用户拥有的角色列表")
    @GetMapping(
            path = {"/user/roles"},
            produces = {"application/json;charset=UTF-8"}
    )
    ApiResponseBody<ArrayList<UserRolesVO>> listUserRoles(@RequestParam("id")String id,
                                                          @RequestParam(name = "portalId", required = false)String portalId);

    @ApiOperation("根据应用id和用户名查询用户信息")
    @GetMapping(
            path = {"/user"},
            produces = {"application/json;charset=UTF-8"}
    )
    ApiResponseBody<SysUser> getUser(@RequestParam(name = "clientId", required = false)String clientId,
                                     @RequestParam(name = "userName", required = false)String userName);
}
