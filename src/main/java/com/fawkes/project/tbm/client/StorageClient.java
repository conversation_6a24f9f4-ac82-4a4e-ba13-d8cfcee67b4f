package com.fawkes.project.tbm.client;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.constants.AppConstants;
import com.fawkes.core.pojo.query.GetFileInfoParam;
import com.fawkes.project.tbm.client.fallback.StorageClientFallBack;
import com.fawkes.project.tbm.common.param.FileInfoParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * @author: wuyunchao
 * @date : 2020-11-26
 **/
@FeignClient(
        value = AppConstants.SYS_APPLICATION_STORAGE_NAME,// "sys-storage-wyc"
        // 开发 环境
//        url = "http://************:18200/api/sys-storage/",
        fallback = StorageClientFallBack.class
)
public interface StorageClient {
    /**
     * 上传接口
     *
     * @param file file
     * @param g9s  g9s
     * @return ApiResponseBody
     */
    @PostMapping(path = "upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ApiResponseBody upload(@RequestPart("file") MultipartFile file,
                                  @RequestParam(value = "g9s", required = false) String g9s);

    /**
     * 下载接口
     *
     * @param f8s f8s
     * @return Resource
     * @throws IOException IOException
     */
    @GetMapping(path = "download", produces = {"application/json;charset=UTF-8"})
    public ResponseEntity<Resource> download(@RequestParam(value = "f8s") String f8s) throws IOException;

    @ApiOperation("获取文件信息")
    @PostMapping(path = "file", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody getFileByToken(@RequestBody GetFileInfoParam getFileInfoParam);


    @ApiOperation("根据k8s获取图片组")
    @PostMapping(path = "/file", consumes = "application/json")
    ApiResponseBody file(FileInfoParam f8s);
}
