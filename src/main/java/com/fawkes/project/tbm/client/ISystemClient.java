package com.fawkes.project.tbm.client;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.constants.AppConstants;
import com.fawkes.project.tbm.client.fallback.SysSystemClientFallBack;
import com.fawkes.project.tbm.common.param.RoleMiddleUserParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@FeignClient(
        value = AppConstants.SYS_APPLICATION_SYSTEM_NAME,
        fallback = SysSystemClientFallBack.class
//        ,url = "http://10.95.101.23:18200/api/sys-system/"
)
public interface ISystemClient {
    /**
     * 获取项目类型枚举
     * @param code 字典键
     * @return ApiResponseBody
     */
    @GetMapping(path = "/dictionary/detail/list", produces = {"application/json;charset=UTF-8"})
    ApiResponseBody dictionaryDetailList(@RequestParam String code,
                                     @RequestParam String langCode,
                                     @RequestParam String serviceType) ;

    /**
     * 获取项目类型枚举
     * @param code 字典键
     * @return ApiResponseBody
     */
    @GetMapping(path = "/dictionary/detail/code", produces = {"application/json;charset=UTF-8"})
    ApiResponseBody dictionaryDetailCode(@RequestParam String code, @RequestParam String langCode) ;

    /**
     * 给用户分配角色
     *
     * @param roleMiddleUserParam
     * @return
     */
    @PostMapping(value = "/role/users")
    ApiResponseBody createRoleMiddleUser(@RequestBody RoleMiddleUserParam roleMiddleUserParam);

    /**
     * 角色删除用户
     *
     * @param roleMiddleUserParam
     * @return
     */
    @DeleteMapping(value = "/role/users")
    ApiResponseBody deleteRoleMiddleUser(@RequestBody RoleMiddleUserParam roleMiddleUserParam);
}