package com.fawkes.project.tbm.client.fallback;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.enums.BizCodeMsgEnum;
import com.fawkes.project.tbm.client.ISystemClient;
import com.fawkes.project.tbm.common.param.AssignRoleParam;
import com.fawkes.project.tbm.common.param.RoleMiddleUserParam;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class SysSystemClientFallBack implements ISystemClient {

    @Override
    public ApiResponseBody dictionaryDetailList(String code, String langCode, String serviceType) {
        return ApiResponseBody.error(BizCodeMsgEnum.SYS_ERROR);
    }

    @Override
    public ApiResponseBody dictionaryDetailCode(String code, String langCode) {
        return ApiResponseBody.error(BizCodeMsgEnum.SYS_ERROR);
    }

    @Override
    public ApiResponseBody createRoleMiddleUser(RoleMiddleUserParam roleMiddleUserParam) {
        return ApiResponseBody.error(BizCodeMsgEnum.SYS_ERROR);
    }

    @Override
    public ApiResponseBody deleteRoleMiddleUser(RoleMiddleUserParam roleMiddleUserParam) {
        return ApiResponseBody.error(BizCodeMsgEnum.SYS_ERROR);
    }
}
