package com.fawkes.project.tbm.client;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.constants.AppConstants;
import com.fawkes.project.tbm.client.fallback.BpmClientFallBack;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 流程接口调用
 * <AUTHOR>
 * @date 2021-4-14
 */
@FeignClient(
        name = AppConstants.SYS_APPLICATION_BPM_NAME,
        fallback = BpmClientFallBack.class
)
public interface IBpmClient {
    @ApiImplicitParams({
            @ApiImplicitParam(name = "modelKey", value = "流程模型Key", required = true),
            @ApiImplicitParam(name = "formKey", value = "表单Key"),
            @ApiImplicitParam(name = "bizId", value = "表单业务数据ID"),
            @ApiImplicitParam(name = "variable", value = "流程变量/审批人"),
            @ApiImplicitParam(name = "comment", value = "审批意见"),
            @ApiImplicitParam(name = "stageFlag", value = "暂存标识 0（不暂存）/1（暂存）", defaultValue = "0"),
            @ApiImplicitParam(name = "customProcessFlag", value = "1 自由流程"),
            @ApiImplicitParam(name = "fileToken", value = "审批附件文件token")
    })
    @PostMapping(value = "/process/start", produces = {"application/json;charset=UTF-8"})
    ApiResponseBody startProcess(@RequestParam(value = "modelKey")String modelKey, @RequestParam(value = "formKey", required = false)String formKey,
                                 @RequestParam(value = "bizId", required = false)String bizId, @RequestBody(required = false) Map<String,Object> variable,
                                 @RequestParam(value = "comment", required = false)String comment, @RequestParam(value = "stageFlag", defaultValue = "0")String stageFlag,
                                 @RequestParam(value = "customProcessFlag", required = false)String customProcessFlag, @RequestParam(value = "fileToken", required = false)String fileToken);

    @ApiOperation("审批通过")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskId", value = "任务ID", required = true),
            @ApiImplicitParam(name = "comment", value = "审批意见"),
            @ApiImplicitParam(name = "approval", value = "审批类型", allowableValues = "todo,commit,agree,reject,delegate,skip,stage", defaultValue = "agree"),
            @ApiImplicitParam(name = "variable", value = "流程变量/审批人"),
            @ApiImplicitParam(name = "stageFlag", value = "暂存标识 0（不暂存）/1（暂存）", defaultValue = "0")
    })
    @PutMapping(value = "/process/complete", produces = {"application/json;charset=UTF-8"})
    ApiResponseBody completeProcess(@RequestParam(value = "taskId")String taskId, @RequestParam(value = "comment", required = false)String comment,
                                    @RequestParam(value = "approval", defaultValue = "agree")String approval, @RequestBody(required = false) Map<String,Object> variable,
                                    @RequestParam(value = "stageFlag", defaultValue = "0")String stageFlag);
}
