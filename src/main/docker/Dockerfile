FROM adoptopenjdk/openjdk8-openj9:alpine-slim

# 环境变量
ENV WORK_PATH /home/<USER>/fawkes
ENV APP_NAME @project.build.finalName@.@project.packaging@
ENV APP_VERSION @project.version@

EXPOSE 11313
#USER
#USER user:group
#VOLUME
VOLUME ["/home/<USER>", "/tmp/data"]
#ADD
#COPY
COPY $APP_NAME $WORK_PATH/
#LABEL
#STOPSIGNAL
#ARG
#ONBUILD
# WORKDIR
WORKDIR $WORK_PATH
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone

# ENTRYPOINT
ENTRYPOINT ["java","-Djava.security.egd=file:/dev/./urandom"]

CMD ["-jar", "@project.build.finalName@.@project.packaging@"]