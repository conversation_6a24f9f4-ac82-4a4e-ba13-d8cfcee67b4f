spring:
  profiles:
    active: '@profiles.active@'
  application:
    name: tbm
  cloud:
    nacos:
      discovery:
        server-addr: 10.95.101.23:18848
        username: nacos
        password: tbm@123456
      config:
        server-addr: 10.95.101.23:18848
        username: nacos
        password: tbm@123456
        file-extension: yml
        group: TBM
        prefix: tbm
        extension-configs:
          - data-id: common-core.yml
            group: FAWKES_SYS_GROUP
            refresh: true
          - data-id: common-jasypt.yml
            group: FAWKES_SYS_GROUP
            refresh: true
          - data-id: common-log.yml
            group: FAWKES_SYS_GROUP
            refresh: true
          - data-id: common-datasource.yml
            group: FAWKES_SYS_GROUP
            refresh: true
          - data-id: common-rabbitmq.yml
            group: FAWKES_SYS_GROUP
            refresh: true
          - data-id: common-redis.yml
            group: FAWKES_SYS_GROUP
            refresh: true