<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.tbm.common.mapper.TbmTappingSystemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fawkes.project.tbm.common.model.TbmTappingSystem">
        <result column="id" property="id" />
        <result column="code" property="code" />
        <result column="snail_rotational_speed" property="snailRotationalSpeed" />
        <result column="snail_torchque" property="snailTorchque" />
        <result column="belt_rotational_speed" property="beltRotationalSpeed" />
        <result column="ring_num" property="ringNum" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        code, snail_rotational_speed, snail_torchque, belt_rotational_speed,ring_num, create_time, update_time
    </sql>
    <select id="getRingNumTbmTappingSystem" resultType="com.fawkes.project.tbm.common.vo.TbmTappingSystemXYVO">
        SELECT
        MAX( DISTINCT snail_rotational_speed ) as snailRotationalSpeedMax,
        MAX( DISTINCT snail_torchque ) as  snailTorchqueMax,
        MAX( DISTINCT belt_rotational_speed ) as  beltRotationalSpeedMax,
        AVG( snail_rotational_speed ) as snailRotationalSpeedAverage,
        AVG( snail_torchque ) as snailTorchqueAverage,
        AVG( belt_rotational_speed) as beltRotationalSpeedAverage,
        ring_num as ring
        FROM
        ${tableName}
        WHERE
        `code` = #{deviceCode}
        <if test="ringNumList != null and ringNumList.size() > 0">
            AND ring_num in (<foreach collection="ringNumList" separator="," item="ringNum">
            #{ringNum}
        </foreach>)
        </if>
        GROUP BY
        ring_num
    </select>

    <!--    查看指定的表是否存在-->
    <select id="existTable" parameterType="String" resultType="Integer">
        select count(*)
        from information_schema.TABLES
        where table_name = #{tableName}
    </select>

    <!-- 删除指定的表-->
    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

    <!-- 创建新的月份表-->
    <update id="createTable" parameterType="String">
        CREATE TABLE ${tableName}
        (
            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '出渣系统参数ID',
            `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备编码',
            `snail_rotational_speed` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '螺机转速/Double/kw',
            `snail_torchque` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '螺机扭矩/Double/hz',
            `belt_rotational_speed` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '皮带机转速/Double',
            `ring_num` int NOT NULL DEFAULT '0' COMMENT '环数',
            `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
            PRIMARY KEY (`id`) USING BTREE,
            KEY `idx_code` (`code`),
            KEY `idx_ring_num` (`ring_num`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='TBM出渣系统参数(此表为原始表，数据按月分表)';
    </update>
</mapper>
