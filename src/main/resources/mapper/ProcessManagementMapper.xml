<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fawkes.project.tbm.common.mapper.ProcessManagementMapper" >
  <resultMap id="BaseResultMap" type="com.fawkes.project.tbm.common.model.ProcessManagement" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="section" property="section" jdbcType="VARCHAR" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_name" property="createName" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_name" property="updateName" jdbcType="VARCHAR" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="delete_flag" property="deleteFlag" jdbcType="INTEGER" />
    <result column="ring_num" property="ringNum" jdbcType="INTEGER" />
    <result column="assembly_point" property="assemblyPoint" jdbcType="DECIMAL" />
    <result column="tunneling_start_time" property="tunnelingStartTime" jdbcType="TIMESTAMP" />
    <result column="tunneling_end_time" property="tunnelingEndTime" jdbcType="TIMESTAMP" />
    <result column="change_steps_start_time" property="changeStepsStartTime" jdbcType="TIMESTAMP" />
    <result column="change_steps_end_time" property="changeStepsEndTime" jdbcType="TIMESTAMP" />
    <result column="segment_assembly_start_time" property="segmentAssemblyStartTime" jdbcType="TIMESTAMP" />
    <result column="segment_assembly_end_time" property="segmentAssemblyEndTime" jdbcType="TIMESTAMP" />
    <result column="others_job_start_time" property="othersJobStartTime" jdbcType="TIMESTAMP" />
    <result column="others_job_end_time" property="othersJobEndTime" jdbcType="TIMESTAMP" />
    <result column="common_maintenance_and_repair_start_time" property="commonMaintenanceAndRepairStartTime" jdbcType="TIMESTAMP" />
    <result column="shutdown_maintenance_and_repair_end_time" property="shutdownMaintenanceAndRepairEndTime" jdbcType="TIMESTAMP" />
    <result column="open_warehouse_change_tool_start_time" property="openWarehouseChangeToolStartTime" jdbcType="TIMESTAMP" />
    <result column="open_warehouse_change_tool_end_time" property="openWarehouseChangeToolEndTime" jdbcType="TIMESTAMP" />
    <result column="open_warehouse_change_tool_num" property="openWarehouseChangeToolNum" jdbcType="INTEGER" />
    <result column="description_of_abnormal_situations" property="descriptionOfAbnormalSituations" jdbcType="VARCHAR" />
    <result column="attachment" property="attachment" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="process_name" property="processName" jdbcType="VARCHAR" />
    <result column="common_maintenance_and_repair_end_time" property="commonMaintenanceAndRepairEndTime" jdbcType="TIMESTAMP" />
    <result column="shutdown_maintenance_and_repair_start_time" property="shutdownMaintenanceAndRepairStartTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.fawkes.project.tbm.common.model.ProcessManagement" extends="BaseResultMap" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="description_of_construction_situation" property="descriptionOfConstructionSituation" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, section, create_by, create_name, create_date, update_by, update_name, update_date,
    delete_flag, ring_num, assembly_point, tunneling_start_time, tunneling_end_time,
    change_steps_start_time, change_steps_end_time, segment_assembly_start_time, segment_assembly_end_time,
    others_job_start_time, others_job_end_time, common_maintenance_and_repair_start_time,
    shutdown_maintenance_and_repair_end_time, open_warehouse_change_tool_start_time,
    open_warehouse_change_tool_end_time, open_warehouse_change_tool_num, description_of_abnormal_situations,
    attachment, remark, process_name, common_maintenance_and_repair_end_time, shutdown_maintenance_and_repair_start_time
  </sql>
  <sql id="Blob_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    description_of_construction_situation
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.fawkes.project.tbm.common.model.ProcessManagementExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from process_management
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.fawkes.project.tbm.common.model.ProcessManagementExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from process_management
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.fawkes.project.tbm.common.model.ProcessManagementExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from process_management
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.fawkes.project.tbm.common.model.ProcessManagement" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into process_management (id, section, create_by,
    create_name, create_date, update_by,
    update_name, update_date, delete_flag,
    ring_num, assembly_point, tunneling_start_time,
    tunneling_end_time, change_steps_start_time,
    change_steps_end_time, segment_assembly_start_time,
    segment_assembly_end_time, others_job_start_time,
    others_job_end_time, common_maintenance_and_repair_start_time,
    shutdown_maintenance_and_repair_end_time, open_warehouse_change_tool_start_time,
    open_warehouse_change_tool_end_time, open_warehouse_change_tool_num,
    description_of_abnormal_situations, attachment,
    remark, process_name, common_maintenance_and_repair_end_time,
    shutdown_maintenance_and_repair_start_time, description_of_construction_situation
    )
    values (#{id,jdbcType=BIGINT}, #{section,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR},
    #{createName,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR},
    #{updateName,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=INTEGER},
    #{ringNum,jdbcType=INTEGER}, #{assemblyPoint,jdbcType=DECIMAL}, #{tunnelingStartTime,jdbcType=TIMESTAMP},
    #{tunnelingEndTime,jdbcType=TIMESTAMP}, #{changeStepsStartTime,jdbcType=TIMESTAMP},
    #{changeStepsEndTime,jdbcType=TIMESTAMP}, #{segmentAssemblyStartTime,jdbcType=TIMESTAMP},
    #{segmentAssemblyEndTime,jdbcType=TIMESTAMP}, #{othersJobStartTime,jdbcType=TIMESTAMP},
    #{othersJobEndTime,jdbcType=TIMESTAMP}, #{commonMaintenanceAndRepairStartTime,jdbcType=TIMESTAMP},
    #{shutdownMaintenanceAndRepairEndTime,jdbcType=TIMESTAMP}, #{openWarehouseChangeToolStartTime,jdbcType=TIMESTAMP},
    #{openWarehouseChangeToolEndTime,jdbcType=TIMESTAMP}, #{openWarehouseChangeToolNum,jdbcType=INTEGER},
    #{descriptionOfAbnormalSituations,jdbcType=VARCHAR}, #{attachment,jdbcType=VARCHAR},
    #{remark,jdbcType=VARCHAR}, #{processName,jdbcType=VARCHAR}, #{commonMaintenanceAndRepairEndTime,jdbcType=TIMESTAMP},
    #{shutdownMaintenanceAndRepairStartTime,jdbcType=TIMESTAMP}, #{descriptionOfConstructionSituation,jdbcType=LONGVARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.fawkes.project.tbm.common.model.ProcessManagement" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into process_management
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="section != null" >
        section,
      </if>
      <if test="createBy != null" >
        create_by,
      </if>
      <if test="createName != null" >
        create_name,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="updateBy != null" >
        update_by,
      </if>
      <if test="updateName != null" >
        update_name,
      </if>
      <if test="updateDate != null" >
        update_date,
      </if>
      <if test="deleteFlag != null" >
        delete_flag,
      </if>
      <if test="ringNum != null" >
        ring_num,
      </if>
      <if test="assemblyPoint != null" >
        assembly_point,
      </if>
      <if test="tunnelingStartTime != null" >
        tunneling_start_time,
      </if>
      <if test="tunnelingEndTime != null" >
        tunneling_end_time,
      </if>
      <if test="changeStepsStartTime != null" >
        change_steps_start_time,
      </if>
      <if test="changeStepsEndTime != null" >
        change_steps_end_time,
      </if>
      <if test="segmentAssemblyStartTime != null" >
        segment_assembly_start_time,
      </if>
      <if test="segmentAssemblyEndTime != null" >
        segment_assembly_end_time,
      </if>
      <if test="othersJobStartTime != null" >
        others_job_start_time,
      </if>
      <if test="othersJobEndTime != null" >
        others_job_end_time,
      </if>
      <if test="commonMaintenanceAndRepairStartTime != null" >
        common_maintenance_and_repair_start_time,
      </if>
      <if test="shutdownMaintenanceAndRepairEndTime != null" >
        shutdown_maintenance_and_repair_end_time,
      </if>
      <if test="openWarehouseChangeToolStartTime != null" >
        open_warehouse_change_tool_start_time,
      </if>
      <if test="openWarehouseChangeToolEndTime != null" >
        open_warehouse_change_tool_end_time,
      </if>
      <if test="openWarehouseChangeToolNum != null" >
        open_warehouse_change_tool_num,
      </if>
      <if test="descriptionOfAbnormalSituations != null" >
        description_of_abnormal_situations,
      </if>
      <if test="attachment != null" >
        attachment,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="processName != null" >
        process_name,
      </if>
      <if test="commonMaintenanceAndRepairEndTime != null" >
        common_maintenance_and_repair_end_time,
      </if>
      <if test="shutdownMaintenanceAndRepairStartTime != null" >
        shutdown_maintenance_and_repair_start_time,
      </if>
      <if test="descriptionOfConstructionSituation != null" >
        description_of_construction_situation,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="section != null" >
        #{section,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null" >
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createName != null" >
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null" >
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteFlag != null" >
        #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="ringNum != null" >
        #{ringNum,jdbcType=INTEGER},
      </if>
      <if test="assemblyPoint != null" >
        #{assemblyPoint,jdbcType=DECIMAL},
      </if>
      <if test="tunnelingStartTime != null" >
        #{tunnelingStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tunnelingEndTime != null" >
        #{tunnelingEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="changeStepsStartTime != null" >
        #{changeStepsStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="changeStepsEndTime != null" >
        #{changeStepsEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="segmentAssemblyStartTime != null" >
        #{segmentAssemblyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="segmentAssemblyEndTime != null" >
        #{segmentAssemblyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="othersJobStartTime != null" >
        #{othersJobStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="othersJobEndTime != null" >
        #{othersJobEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commonMaintenanceAndRepairStartTime != null" >
        #{commonMaintenanceAndRepairStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shutdownMaintenanceAndRepairEndTime != null" >
        #{shutdownMaintenanceAndRepairEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="openWarehouseChangeToolStartTime != null" >
        #{openWarehouseChangeToolStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="openWarehouseChangeToolEndTime != null" >
        #{openWarehouseChangeToolEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="openWarehouseChangeToolNum != null" >
        #{openWarehouseChangeToolNum,jdbcType=INTEGER},
      </if>
      <if test="descriptionOfAbnormalSituations != null" >
        #{descriptionOfAbnormalSituations,jdbcType=VARCHAR},
      </if>
      <if test="attachment != null" >
        #{attachment,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="processName != null" >
        #{processName,jdbcType=VARCHAR},
      </if>
      <if test="commonMaintenanceAndRepairEndTime != null" >
        #{commonMaintenanceAndRepairEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shutdownMaintenanceAndRepairStartTime != null" >
        #{shutdownMaintenanceAndRepairStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="descriptionOfConstructionSituation != null" >
        #{descriptionOfConstructionSituation,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.fawkes.project.tbm.common.model.ProcessManagementExample" resultType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from process_management
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update process_management
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.section != null" >
        section = #{record.section,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createName != null" >
        create_name = #{record.createName,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateName != null" >
        update_name = #{record.updateName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleteFlag != null" >
        delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="record.ringNum != null" >
        ring_num = #{record.ringNum,jdbcType=INTEGER},
      </if>
      <if test="record.assemblyPoint != null" >
        assembly_point = #{record.assemblyPoint,jdbcType=DECIMAL},
      </if>
      <if test="record.tunnelingStartTime != null" >
        tunneling_start_time = #{record.tunnelingStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tunnelingEndTime != null" >
        tunneling_end_time = #{record.tunnelingEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.changeStepsStartTime != null" >
        change_steps_start_time = #{record.changeStepsStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.changeStepsEndTime != null" >
        change_steps_end_time = #{record.changeStepsEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.segmentAssemblyStartTime != null" >
        segment_assembly_start_time = #{record.segmentAssemblyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.segmentAssemblyEndTime != null" >
        segment_assembly_end_time = #{record.segmentAssemblyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.othersJobStartTime != null" >
        others_job_start_time = #{record.othersJobStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.othersJobEndTime != null" >
        others_job_end_time = #{record.othersJobEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.commonMaintenanceAndRepairStartTime != null" >
        common_maintenance_and_repair_start_time = #{record.commonMaintenanceAndRepairStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.shutdownMaintenanceAndRepairEndTime != null" >
        shutdown_maintenance_and_repair_end_time = #{record.shutdownMaintenanceAndRepairEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.openWarehouseChangeToolStartTime != null" >
        open_warehouse_change_tool_start_time = #{record.openWarehouseChangeToolStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.openWarehouseChangeToolEndTime != null" >
        open_warehouse_change_tool_end_time = #{record.openWarehouseChangeToolEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.openWarehouseChangeToolNum != null" >
        open_warehouse_change_tool_num = #{record.openWarehouseChangeToolNum,jdbcType=INTEGER},
      </if>
      <if test="record.descriptionOfAbnormalSituations != null" >
        description_of_abnormal_situations = #{record.descriptionOfAbnormalSituations,jdbcType=VARCHAR},
      </if>
      <if test="record.attachment != null" >
        attachment = #{record.attachment,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.processName != null" >
        process_name = #{record.processName,jdbcType=VARCHAR},
      </if>
      <if test="record.commonMaintenanceAndRepairEndTime != null" >
        common_maintenance_and_repair_end_time = #{record.commonMaintenanceAndRepairEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.shutdownMaintenanceAndRepairStartTime != null" >
        shutdown_maintenance_and_repair_start_time = #{record.shutdownMaintenanceAndRepairStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.descriptionOfConstructionSituation != null" >
        description_of_construction_situation = #{record.descriptionOfConstructionSituation,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update process_management
    set id = #{record.id,jdbcType=BIGINT},
    section = #{record.section,jdbcType=VARCHAR},
    create_by = #{record.createBy,jdbcType=VARCHAR},
    create_name = #{record.createName,jdbcType=VARCHAR},
    create_date = #{record.createDate,jdbcType=TIMESTAMP},
    update_by = #{record.updateBy,jdbcType=VARCHAR},
    update_name = #{record.updateName,jdbcType=VARCHAR},
    update_date = #{record.updateDate,jdbcType=TIMESTAMP},
    delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
    ring_num = #{record.ringNum,jdbcType=INTEGER},
    assembly_point = #{record.assemblyPoint,jdbcType=DECIMAL},
    tunneling_start_time = #{record.tunnelingStartTime,jdbcType=TIMESTAMP},
    tunneling_end_time = #{record.tunnelingEndTime,jdbcType=TIMESTAMP},
    change_steps_start_time = #{record.changeStepsStartTime,jdbcType=TIMESTAMP},
    change_steps_end_time = #{record.changeStepsEndTime,jdbcType=TIMESTAMP},
    segment_assembly_start_time = #{record.segmentAssemblyStartTime,jdbcType=TIMESTAMP},
    segment_assembly_end_time = #{record.segmentAssemblyEndTime,jdbcType=TIMESTAMP},
    others_job_start_time = #{record.othersJobStartTime,jdbcType=TIMESTAMP},
    others_job_end_time = #{record.othersJobEndTime,jdbcType=TIMESTAMP},
    common_maintenance_and_repair_start_time = #{record.commonMaintenanceAndRepairStartTime,jdbcType=TIMESTAMP},
    shutdown_maintenance_and_repair_end_time = #{record.shutdownMaintenanceAndRepairEndTime,jdbcType=TIMESTAMP},
    open_warehouse_change_tool_start_time = #{record.openWarehouseChangeToolStartTime,jdbcType=TIMESTAMP},
    open_warehouse_change_tool_end_time = #{record.openWarehouseChangeToolEndTime,jdbcType=TIMESTAMP},
    open_warehouse_change_tool_num = #{record.openWarehouseChangeToolNum,jdbcType=INTEGER},
    description_of_abnormal_situations = #{record.descriptionOfAbnormalSituations,jdbcType=VARCHAR},
    attachment = #{record.attachment,jdbcType=VARCHAR},
    remark = #{record.remark,jdbcType=VARCHAR},
    process_name = #{record.processName,jdbcType=VARCHAR},
    common_maintenance_and_repair_end_time = #{record.commonMaintenanceAndRepairEndTime,jdbcType=TIMESTAMP},
    shutdown_maintenance_and_repair_start_time = #{record.shutdownMaintenanceAndRepairStartTime,jdbcType=TIMESTAMP},
    description_of_construction_situation = #{record.descriptionOfConstructionSituation,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update process_management
    set id = #{record.id,jdbcType=BIGINT},
    section = #{record.section,jdbcType=VARCHAR},
    create_by = #{record.createBy,jdbcType=VARCHAR},
    create_name = #{record.createName,jdbcType=VARCHAR},
    create_date = #{record.createDate,jdbcType=TIMESTAMP},
    update_by = #{record.updateBy,jdbcType=VARCHAR},
    update_name = #{record.updateName,jdbcType=VARCHAR},
    update_date = #{record.updateDate,jdbcType=TIMESTAMP},
    delete_flag = #{record.deleteFlag,jdbcType=INTEGER},
    ring_num = #{record.ringNum,jdbcType=INTEGER},
    assembly_point = #{record.assemblyPoint,jdbcType=DECIMAL},
    tunneling_start_time = #{record.tunnelingStartTime,jdbcType=TIMESTAMP},
    tunneling_end_time = #{record.tunnelingEndTime,jdbcType=TIMESTAMP},
    change_steps_start_time = #{record.changeStepsStartTime,jdbcType=TIMESTAMP},
    change_steps_end_time = #{record.changeStepsEndTime,jdbcType=TIMESTAMP},
    segment_assembly_start_time = #{record.segmentAssemblyStartTime,jdbcType=TIMESTAMP},
    segment_assembly_end_time = #{record.segmentAssemblyEndTime,jdbcType=TIMESTAMP},
    others_job_start_time = #{record.othersJobStartTime,jdbcType=TIMESTAMP},
    others_job_end_time = #{record.othersJobEndTime,jdbcType=TIMESTAMP},
    common_maintenance_and_repair_start_time = #{record.commonMaintenanceAndRepairStartTime,jdbcType=TIMESTAMP},
    shutdown_maintenance_and_repair_end_time = #{record.shutdownMaintenanceAndRepairEndTime,jdbcType=TIMESTAMP},
    open_warehouse_change_tool_start_time = #{record.openWarehouseChangeToolStartTime,jdbcType=TIMESTAMP},
    open_warehouse_change_tool_end_time = #{record.openWarehouseChangeToolEndTime,jdbcType=TIMESTAMP},
    open_warehouse_change_tool_num = #{record.openWarehouseChangeToolNum,jdbcType=INTEGER},
    description_of_abnormal_situations = #{record.descriptionOfAbnormalSituations,jdbcType=VARCHAR},
    attachment = #{record.attachment,jdbcType=VARCHAR},
    remark = #{record.remark,jdbcType=VARCHAR},
    process_name = #{record.processName,jdbcType=VARCHAR},
    common_maintenance_and_repair_end_time = #{record.commonMaintenanceAndRepairEndTime,jdbcType=TIMESTAMP},
    shutdown_maintenance_and_repair_start_time = #{record.shutdownMaintenanceAndRepairStartTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <select id="listProcessManagementRingNum" resultType="com.fawkes.project.tbm.common.vo.ProcessManagementRingNumVO">
    select
      section as section,ring_num as ring_num, update_date as update_time
    from process_management
    where section = #{section}
      and delete_flag = 0
    order by ring_num asc
  </select>

  <select id="listAllRingAbnormalSituationsDescription"
          resultType="com.fawkes.project.tbm.common.vo.RingAbnormalSituationsDescriptionVO">
    SELECT
      *
    FROM
      (
        SELECT
          id,
          ring_num,
          description_of_abnormal_situations,
          ROW_NUMBER() OVER(PARTITION BY ring_num ORDER BY update_date DESC) AS rowNumber
        FROM
          process_management
        where delete_flag = 0
          and section = #{section}
          and description_of_abnormal_situations IS NOT NULL
          and description_of_abnormal_situations != ''
        order by update_date desc
      )t
    WHERE
      rowNumber = 1;
  </select>

  <select id="listProcessManagementGroupByRingNum" resultType="com.fawkes.project.tbm.common.vo.ProcessManagementVO">
    SELECT
      *
    FROM
      (
        SELECT
          id,
          section,
          ring_num,
          process_name,
          tunneling_start_time,
          tunneling_end_time,
          change_steps_start_time,
          change_steps_end_time,
          segment_assembly_start_time,
          segment_assembly_end_time,
          others_job_start_time,
          others_job_end_time,
          common_maintenance_and_repair_start_time,
          common_maintenance_and_repair_end_time,
          shutdown_maintenance_and_repair_end_time,
          shutdown_maintenance_and_repair_start_time,
          open_warehouse_change_tool_start_time,
          open_warehouse_change_tool_end_time,
          ROW_NUMBER() OVER(PARTITION BY ring_num ORDER BY update_date DESC) AS rowNumber
        FROM
          process_management
        where delete_flag = 0
          and section = #{section}
          and tunneling_start_time IS NOT NULL
          and tunneling_end_time IS NOT NULL
      )t
    WHERE
      rowNumber = 1;
  </select>
</mapper>