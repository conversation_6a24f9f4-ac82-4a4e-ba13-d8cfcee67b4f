<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.tbm.common.mapper.HarmfulGasDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fawkes.project.tbm.common.model.HarmfulGasDevice">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="device_code" property="deviceCode" />
        <result column="device_name" property="deviceName" />
        <result column="ch4_level" property="ch4Level" />
        <result column="h2s_level" property="h2sLevel" />
        <result column="o2_level" property="o2Level" />
        <result column="co_level" property="coLevel" />
        <result column="update_by_fullname" property="updateByFullname" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        delete_flag,
        code, name, device_code, device_name, ch4_level, h2s_level, o2_level, co_level, update_by_fullname
    </sql>

</mapper>
