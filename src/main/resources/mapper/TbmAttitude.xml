<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.tbm.common.mapper.TbmAttitudeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fawkes.project.tbm.common.model.TbmAttitude">
        <result column="id" property="id" />
        <result column="code" property="code" />
        <result column="horizontal_end" property="horizontalEnd" />
        <result column="horizontal_front" property="horizontalFront" />
        <result column="vertical_end" property="verticalEnd" />
        <result column="vertical_front" property="verticalFront" />
        <result column="mileage" property="mileage" />
        <result column="tunneling" property="tunneling" />
        <result column="rolling_angle" property="rollingAngle" />
        <result column="pitch_angle" property="pitchAngle" />
        <result column="ring_num" property="ringNum" />
        <result column="runing_state" property="runingState" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        code, horizontal_end, horizontal_front, vertical_end, vertical_front, mileage, tunneling, rolling_angle, pitch_angle, ring_num, runing_state, longitude, latitude, create_time, update_time
    </sql>
    <select id="getRingNumTbmAttitude" resultType="com.fawkes.project.tbm.common.vo.TbmAttitudeXYVO">
        SELECT
            MAX( DISTINCT horizontal_end ) as horizontalEndMax,
            MAX( DISTINCT horizontal_front ) as horizontalFrontMax,
            MAX( DISTINCT vertical_end ) as verticalEndMax,
            MAX( DISTINCT vertical_front ) as verticalFrontMax,
            AVG( horizontal_end ) as horizontalEndAverage,
            AVG( horizontal_front ) as horizontalFrontAverage,
            AVG( vertical_end ) as verticalEndAverage,
            AVG( vertical_front ) as verticalFrontAverage,
            ring_num AS ring
        FROM
            ${tableName}
        WHERE
        `code` = #{deviceCode}
        <if test="ringNumList != null and ringNumList.size() > 0">
            AND ring_num in (<foreach collection="ringNumList" separator="," item="ringNum">
            #{ringNum}
        </foreach>)
        </if>
        GROUP BY
            ring_num
    </select>

    <!--    查看指定的表是否存在-->
    <select id="existTable" parameterType="String" resultType="Integer">
        select count(*)
        from information_schema.TABLES
        where table_name = #{tableName}
    </select>

    <!-- 删除指定的表-->
    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

    <!-- 创建新的月份表-->
    <update id="createTable" parameterType="String">
        CREATE TABLE ${tableName}
        (
            `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'TBM姿态参数ID',
            `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备编码',
            `horizontal_end` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '水平-后/Double/mm',
            `horizontal_front` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '水平-前/Double/mm',
            `vertical_end` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '垂直-后/Double/mm',
            `vertical_front` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '垂直-前/Double/mm',
            `mileage` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '里程/Double/m',
            `tunneling` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '掘进/Double/m',
            `rolling_angle` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '滚动角/Double/mm/m',
            `pitch_angle` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '俯仰角/Double/mm/m',
            `ring_num` int NOT NULL DEFAULT '0' COMMENT '环数',
            `runing_state` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '运行状态： 掘进中|已停机',
            `longitude` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '经度',
            `latitude` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '纬度',
            `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
            `dish_coordinate_x` varchar(100) DEFAULT NULL COMMENT '盾首坐标X',
            `dish_coordinate_y` varchar(100) DEFAULT NULL COMMENT '盾首坐标Y',
            `dish_coordinate_z` varchar(100) DEFAULT NULL COMMENT '盾首坐标Z',
            `longitude_gc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '经度-高德',
            `latitude_gc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '纬度-高德',
            PRIMARY KEY (`id`) USING BTREE,
            KEY `idx_code` (`code`),
            KEY `idx_ring_num` (`ring_num`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='TBM姿态参数表(此表为原始表，数据按月分表)';
    </update>
</mapper>
