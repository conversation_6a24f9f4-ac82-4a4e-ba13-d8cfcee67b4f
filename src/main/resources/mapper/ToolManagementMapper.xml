<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fawkes.project.tbm.common.mapper.ToolManagementMapper" >

    <select id="toolDisplay" resultType="com.fawkes.project.tbm.common.vo.ToolDisplayVO">
        SELECT
            COUNT( IF ( tm.has_tool_changed = 0, NULL, tm.id )) AS changeToolNum,
            SUM( tmp.seconds_difference ) AS changeToolTime
        FROM
            tool_management AS tm
                LEFT JOIN
                ( SELECT id, TIMESTAMPDIFF( SECOND, open_warehouse_start_time, open_warehouse_end_time ) AS seconds_difference
                    FROM tool_management
                    WHERE delete_flag = 0 and section = #{section} ) AS tmp
                ON tmp.id = tm.id
        WHERE
            tm.delete_flag = 0
          AND tm.section = #{section}
    </select>
    <select id="getPositionNum" resultType="com.fawkes.project.tbm.common.param.ToolInfoParam">
        SELECT
            COUNT( IF ( tm.has_tool_changed = 0, NULL, tm.id ) ) AS changeToolNum,
            tpm.tool_position as toolLocation,
            tpm.id as id
        FROM
            tool_management tm
                LEFT JOIN tool_position_manage tpm ON tm.tool_position_manage_id = tpm.id
        WHERE
            tm.delete_flag = 0
          AND tm.section = #{section}
          AND tpm.delete_flag = 0
          AND tpm.section = #{section}
        GROUP BY
            tpm.tool_position,
            tpm.id
    </select>
    <select id="getToolInfo" resultType="com.fawkes.project.tbm.common.param.ToolInfoParam">
        SELECT
            tmp.tool_position_manage_id as id,
            tmp.change_tool_picture as changeToolPicture,
            tmp.change_tool_reason as changeToolReason,
            tmp.section as section,
            tnm.too_name as toolName
        FROM
            ( SELECT *, ROW_NUMBER() OVER ( PARTITION BY tool_position_manage_id ORDER BY update_date DESC ) AS rn FROM tool_management ) AS tmp
                LEFT JOIN tool_name_manage tnm ON tmp.tool_name_manage_id = tnm.id
        WHERE
            tmp.rn = 1
          AND tmp.delete_flag = 0
          AND tnm.delete_flag = 0
          AND tmp.section = #{section}
          AND tnm.section = #{section}
    </select>
    <select id="toolWearEarlyWarning" resultType="com.fawkes.project.tbm.common.param.ToolWearEarlyParam">
        SELECT
            tmp.wear_value as wearValue,
            tmp.end_mileage as endMileage,
            tmp.section as section,
            tmp.change_threshold as changeThreshold,
            tmp.has_tool_changed as hasToolChanged,
            tmp.tool_position_manage_id as toolPositionManageId,
            tpm.tool_position as toolPosition
        FROM
            (
            SELECT *, ROW_NUMBER() OVER ( PARTITION BY tool_position_manage_id ORDER BY update_date DESC ) AS rn
              FROM tool_management
              where delete_flag = 0
            ) AS tmp
                LEFT JOIN tool_position_manage tpm ON tmp.tool_position_manage_id = tpm.id
        WHERE
            tmp.rn = 1
          AND tmp.section = #{section}
        ORDER BY
            tmp.tool_position_manage_id
    </select>
    <select id="getCumulativeWearAndTear" resultType="com.fawkes.project.tbm.common.vo.ToolWearEarlyWarningVO">
        SELECT
            SUM(IF ( has_tool_changed = 1, wear_value, NULL ) ) as cumulativeWearAndTear,
            tool_position_manage_id as toolPositionManageId
        FROM
            tool_management
        WHERE
            delete_flag = 0
          AND section = #{section}
            and tool_position_manage_id in (
        <foreach collection="positionIdList" item="positionId" separator=",">
            #{positionId}
        </foreach>
        )
        GROUP BY
            tool_position_manage_id
    </select>


    <select id="selectToolWearCompareData" resultType="com.fawkes.project.tbm.common.vo.ToolWearCompareVO">
        <!--  根据位置id进行分组,然后根据填报的更新时间倒序 -->
        WITH ranked_changes AS (SELECT toolPosition.id            AS tool_position_manage_id,
                                       toolPosition.tool_position AS tool_location,
                                       toolPosition.sort,
                                       toolManagement.wear_value,
                                       toolManagement.update_date,
                                       ROW_NUMBER() OVER (
                                           PARTITION BY toolPosition.id
                                           ORDER BY toolManagement.update_date DESC, toolManagement.id DESC
                                           ) AS orderIndex
        FROM tool_position_manage toolPosition
        LEFT JOIN tool_management toolManagement
        ON toolPosition.id = toolManagement.tool_position_manage_id
         <!-- 刀具填报del = 0放着,刀具位置要全展示,不然leftjoin没效-->
        AND toolManagement.delete_flag = 0
        WHERE toolPosition.section = #{section}
        AND toolPosition.group_id = #{groupId}
        AND toolPosition.delete_flag = 0
        )
        SELECT
        rc.tool_location,
        rc.sort,
        <!--  最新的一次 -->
        COALESCE(MAX(IF(rc.orderIndex = 1, CAST(rc.wear_value AS CHAR), NULL)), '0.000') as this_time_wear_value,
        <!--  最新的第二次序 -->
        COALESCE(MAX(IF(rc.orderIndex = 2, CAST(rc.wear_value AS CHAR), NULL)), '0.000') as last_time_wear_value
        FROM ranked_changes rc
        <!--  只取前两次 -->
        WHERE rc.orderIndex &lt;= 2
        GROUP BY rc.tool_location,rc.sort
        ORDER BY rc.sort;
    </select>

    <select id="toolWearRule" resultType="com.fawkes.project.tbm.common.vo.ToolWearRuleVO">
        select * from
            (select toolPosition.id as toolPositionManageId,
                    toolPosition.tool_position  as toolLocation,
                    toolManage.wear_value as LastWearValue,
                    toolManage.end_mileage as LastEndMileage,
                    toolManage.has_tool_changed as LastHasToolChanged,
                    ROW_NUMBER() OVER ( PARTITION BY toolPosition.id ORDER BY toolManage.update_date DESC, toolManage.id desc ) AS rn
             from tool_position_manage toolPosition
                      left join tool_management toolManage
                                on toolPosition.id = toolManage.tool_position_manage_id
                                    AND toolManage.delete_flag = 0
             where toolPosition.delete_flag = 0
               and toolPosition.section = #{section}
               and toolPosition.group_id = #{groupId}
            ) as temp
        where rn = 1
    </select>

    <select id="findToolPositionManageIdList" resultType="java.lang.Long">
        select  distinct  tool_position_manage_id from tool_management
        where delete_flag = 0
        and section = #{section}
    </select>
</mapper>
