<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.tbm.common.mapper.TbmHarmfulGasMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fawkes.project.tbm.common.model.TbmHarmfulGas">
        <result column="id" property="id" />
        <result column="code" property="code" />
        <result column="ch4_level" property="ch4Level" />
        <result column="h2s_level" property="h2sLevel" />
        <result column="o2_level" property="o2Level" />
        <result column="co_level" property="coLevel" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        code, ch4_level, h2s_level, o2_level, co_level, create_time, update_time
    </sql>

<!--auto generated by MybatisCodeHelper on 2025-04-03-->
    <select id="selectByCodeInLastOne" resultMap="BaseResultMap">
        SELECT    o1.id,
        o1.code, o1.ch4_level, o1.h2s_level, o1.o2_level, o1.co_level, o1.create_time, o1.update_time
        FROM ${tableName} o1
            JOIN (
        SELECT code, MAX(create_time) AS latest_create_time
        FROM ${tableName} WHERE code in
        <foreach item="item" index="index" collection="codeCollection"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY code
        ) o2 ON o1.code = o2.code AND o1.create_time = o2.latest_create_time;
    </select>

    <!--    查看指定的表是否存在-->
    <select id="existTable" parameterType="String" resultType="Integer">
        select count(*)
        from information_schema.TABLES
        where table_name = #{tableName}
    </select>

    <!-- 删除指定的表-->
    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

    <!-- 创建新的月份表-->
    <update id="createTable" parameterType="String">
        CREATE TABLE ${tableName}
        (
            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '有害气体ID',
            `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备编码',
            `ch4_level` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'CH4/Double/%LEL',
            `h2s_level` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'H2S/Double/%VOL',
            `o2_level` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'O2/Double/%VOL',
            `co_level` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'CO/Double/PPM',
            `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
            PRIMARY KEY (`id`) USING BTREE,
            KEY `idx_code` (`code`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='TBM有害气体参数表(此表为原始表，数据按月分表)';
    </update>
</mapper>
