<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.tbm.common.mapper.TbmDriveSystemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fawkes.project.tbm.common.model.TbmDriveSystem">
        <result column="id" property="id" />
        <result column="code" property="code"/>
        <result column="power" property="power"/>
        <result column="frequency" property="frequency"/>
        <result column="electric_current_one" property="electricCurrentOne"/>
        <result column="electric_current_two" property="electricCurrentTwo"/>
        <result column="electric_current_three" property="electricCurrentThree"/>
        <result column="electric_current_four" property="electricCurrentFour"/>
        <result column="electric_current_five" property="electricCurrentFive"/>
        <result column="electric_current_six" property="electricCurrentSix"/>
        <result column="electric_current_seven" property="electricCurrentSeven"/>
        <result column="electric_current_eight" property="electricCurrentEight"/>
        <result column="electric_current_nine" property="electricCurrentNine"/>
        <result column="electric_current_ten" property="electricCurrentTen"/>
        <result column="electric_current_eleven" property="electricCurrentEleven"/>
        <result column="electric_current_twelve" property="electricCurrentTwelve"/>
        <result column="electric_current_thirteen" property="electricCurrentThirteen"/>
        <result column="electric_current_fourteen" property="electricCurrentFourteen"/>
        <result column="ring_num" property="ringNum" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        code,
        `power`,
        frequency,
        electric_current_one,
        electric_current_two,
        electric_current_three,
        electric_current_four,
        electric_current_five,
        electric_current_six,
        electric_current_seven,
        electric_current_eight,
        electric_current_nine,
        electric_current_ten,
        electric_current_eleven,
        electric_current_twelve,
        electric_current_thirteen,
        electric_current_fourteen,
        ring_num,
        create_time,
        update_time
    </sql>
    <select id="getRingNumTbmDriveSystem" resultType="com.fawkes.project.tbm.common.vo.TbmDriveSystemXYVO">
        SELECT
        MAX( DISTINCT power ) AS powerMax,
        MAX( DISTINCT ${electricCurrent}) AS electricCurrentMax,
        MAX( DISTINCT ${motorTemperature}) AS temperatureMax,
        AVG( power ) AS powerAverage,
        AVG( ${electricCurrent} ) AS electricCurrentAverage,
        AVG( ${motorTemperature} ) AS temperatureAverage,
        ring_num AS ring
        FROM
         ${tableName}
        WHERE
        `code` = #{deviceCode}
          <if test="ringNumList != null and ringNumList.size() > 0">
              AND ring_num in (<foreach collection="ringNumList" separator="," item="ringNum">
              #{ringNum}
          </foreach>)
          </if>
        GROUP BY
        ring_num
    </select>

    <!--    查看指定的表是否存在-->
    <select id="existTable" parameterType="String" resultType="Integer">
        select count(*)
        from information_schema.TABLES
        where table_name = #{tableName}
    </select>

    <!-- 删除指定的表-->
    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

    <!-- 创建新的月份表-->
    <update id="createTable" parameterType="String">
        CREATE TABLE ${tableName}
        (
            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '驱动系统参数ID',
            `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备编码',
            `power` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '电机总功率/Double/kw',
            `frequency` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '振动频率/Double/hz',
            `electric_current_one` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '电机电流/Double#1',
            `electric_current_two` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '电机电流/Double#2',
            `electric_current_three` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '电机电流/Double#3',
            `electric_current_four` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '电机电流/Double#4',
            `electric_current_five` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '电机电流/Double#5',
            `electric_current_six` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '电机电流/Double#6',
            `electric_current_seven` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '电机电流/Double#7',
            `electric_current_eight` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '电机电流/Double#8',
            `electric_current_nine` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '电机电流/Double#9',
            `electric_current_ten` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '电机电流/Double#10',
            `electric_current_eleven` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '电机电流/Double#11',
            `electric_current_twelve` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '电机电流/Double#12',
            `electric_current_thirteen` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '电机电流/Double#13',
            `electric_current_fourteen` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '电机电流/Double#14',
            `ring_num` int NOT NULL DEFAULT '0' COMMENT '环数',
            `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
            `temperature_one` decimal(10,2) DEFAULT NULL COMMENT '电机温度/Double#1',
            `temperature_two` decimal(10,2) DEFAULT NULL COMMENT '电机温度/Double#2',
            `temperature_three` decimal(10,2) DEFAULT NULL COMMENT '电机温度/Double#3',
            `temperature_four` decimal(10,2) DEFAULT NULL COMMENT '电机温度/Double#4',
            `temperature_five` decimal(10,2) DEFAULT NULL COMMENT '电机温度/Double#5',
            `temperature_six` decimal(10,2) DEFAULT NULL COMMENT '电机温度/Double#6',
            `temperature_seven` decimal(10,2) DEFAULT NULL COMMENT '电机温度/Double#7',
            `temperature_eight` decimal(10,2) DEFAULT NULL COMMENT '电机温度/Double#8',
            `temperature_nine` decimal(10,2) DEFAULT NULL COMMENT '电机温度/Double#9',
            `temperature_ten` decimal(10,2) DEFAULT NULL COMMENT '电机温度/Double#10',
            `temperature_eleven` decimal(10,2) DEFAULT NULL COMMENT '电机温度/Double#11',
            `temperature_twelve` decimal(10,2) DEFAULT NULL COMMENT '电机温度/Double#12',
            `temperature_thirteen` decimal(10,2) DEFAULT NULL COMMENT '电机温度/Double#13',
            `temperature_fourteen` decimal(10,2) DEFAULT NULL COMMENT '电机温度/Double#14',
            PRIMARY KEY (`id`) USING BTREE,
            KEY `idx_code` (`code`),
            KEY `idx_ring_num` (`ring_num`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='TBM驱动系统参数(此表为原始表，数据按月分表)';
    </update>
</mapper>
