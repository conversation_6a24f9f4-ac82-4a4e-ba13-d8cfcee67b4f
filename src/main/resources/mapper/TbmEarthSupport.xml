<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.tbm.common.mapper.TbmEarthSupportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fawkes.project.tbm.common.model.TbmEarthSupport">
        <result column="id" property="id" />
        <result column="code" property="code" />
        <result column="brace_pressure" property="bracePressure" />
        <result column="ring_num" property="ringNum" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        code, brace_pressure, ring_num, create_time, update_time
    </sql>
    <select id="getRingNumTbmEarthSupport" resultType="com.fawkes.project.tbm.common.vo.TbmXYVO">
        SELECT
        <if test="position != null and position != ''">
                MAX( DISTINCT ${position} ) AS max,
                AVG( ${position} ) AS average,
        </if>
        <if test="position == null || position == ''">
            MAX( DISTINCT brace_pressure ) AS max,
            AVG( brace_pressure ) AS average,
        </if>
            ring_num AS ring
        FROM
            ${tableName}
        WHERE
        `code` = #{deviceCode}
        <if test="ringNumList != null and ringNumList.size() > 0">
            AND ring_num in (<foreach collection="ringNumList" separator="," item="ringNum">
            #{ringNum}
        </foreach>)
        </if>
        GROUP BY
            ring_num
    </select>

    <!--    查看指定的表是否存在-->
    <select id="existTable" parameterType="String" resultType="Integer">
        select count(*)
        from information_schema.TABLES
        where table_name = #{tableName}
    </select>

    <!-- 删除指定的表-->
    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

    <!-- 创建新的月份表-->
    <update id="createTable" parameterType="String">
        CREATE TABLE ${tableName}
        (
            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '土仓压力参数ID',
            `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备编码',
            `brace_pressure` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '压力/Double/bar',
            `ring_num` int NOT NULL DEFAULT '0' COMMENT '环数',
            `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
            `position_one` decimal(10,6) DEFAULT NULL COMMENT '位置1#',
            `position_two` decimal(10,6) DEFAULT NULL COMMENT '位置2#',
            `position_three` decimal(10,6) DEFAULT NULL COMMENT '位置3#',
            `position_four` decimal(10,6) DEFAULT NULL COMMENT '位置4#',
            `position_five` decimal(10,6) DEFAULT NULL COMMENT '位置5#',
            `position_six` decimal(10,6) DEFAULT NULL COMMENT '位置6#',
            `position_seven` decimal(10,6) DEFAULT NULL COMMENT '位置7#',
            `position_eight` decimal(10,6) DEFAULT NULL COMMENT '位置8#',
            PRIMARY KEY (`id`) USING BTREE,
            KEY `idx_code` (`code`),
            KEY `idx_ring_num` (`ring_num`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='TBM土仓压力参数表(此表为原始表，数据按月分表)';
    </update>
</mapper>
