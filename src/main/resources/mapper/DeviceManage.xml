<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.tbm.common.mapper.DeviceManageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fawkes.project.tbm.common.model.DeviceManage">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="mode" property="mode" />
        <result column="construction_unit" property="constructionUnit" />
        <result column="section" property="section" />
        <result column="total_mileage" property="totalMileage" />
        <result column="ring_num" property="ringNum" />
        <result column="ring_width" property="ringWidth" />
        <result column="update_by_fullname" property="updateByFullname" />
        <result column="diameter" property="diameter" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_by,
        create_date,
        update_by,
        update_date,
        delete_flag,
        code, name, type, mode, construction_unit, section, total_mileage, ring_num, ring_width, update_by_fullname,diameter
    </sql>

</mapper>
