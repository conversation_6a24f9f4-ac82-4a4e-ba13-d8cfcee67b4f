<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.tbm.common.mapper.TbmPropulsionSystemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fawkes.project.tbm.common.model.TbmPropulsionSystem">
        <result column="id" property="id" />
        <result column="code" property="code" />
        <result column="thrust" property="thrust" />
        <result column="prop_speed" property="propSpeed" />
        <result column="cylinder_pressure" property="cylinderPressure" />
        <result column="oil_temp" property="oilTemp" />
        <result column="ring_num" property="ringNum" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        code, thrust, prop_speed, cylinder_pressure, oil_temp,ring_num, create_time, update_time
    </sql>
    <select id="getRingNumTbmPropulsionSystem" resultType="com.fawkes.project.tbm.common.vo.TbmPropulsionSystemXYVO">
        SELECT
        MAX( DISTINCT thrust ) as thrustMax,
        MAX( DISTINCT prop_speed ) as propSpeedMax,
        MAX( DISTINCT ${cylinderPressureStr}) as cylinderPressureMax,
        AVG( thrust ) as thrustAverage,
        AVG( prop_speed ) as propSpeedAverage,
        AVG(${cylinderPressureStr}) as cylinderPressureAverage,
        ring_num as ring
        FROM
        ${tableName}
        WHERE
        `code` = #{deviceCode}
        <if test="ringNumList != null and ringNumList.size() > 0">
            AND ring_num in (<foreach collection="ringNumList" separator="," item="ringNum">
            #{ringNum}
        </foreach>)
        </if>
        GROUP BY
        ring_num
    </select>

    <!--    查看指定的表是否存在-->
    <select id="existTable" parameterType="String" resultType="Integer">
        select count(*)
        from information_schema.TABLES
        where table_name = #{tableName}
    </select>

    <!-- 删除指定的表-->
    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

    <!-- 创建新的月份表-->
    <update id="createTable" parameterType="String">
        CREATE TABLE ${tableName}
        (
            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '推进系统参数ID',
            `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备编码',
            `thrust` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '推力/Double/kN',
            `prop_speed` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '推进速度/Double/mm/M',
            `cylinder_pressure` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '油缸压力/bar',
            `oil_temp` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '油箱温度/Double/℃',
            `ring_num` int NOT NULL DEFAULT '0' COMMENT '环数',
            `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
            `cylinder_pressure_one` decimal(10,4) DEFAULT NULL COMMENT '油缸压力1#',
            `cylinder_pressure_two` decimal(10,4) DEFAULT NULL COMMENT '油缸压力2#',
            `cylinder_pressure_three` decimal(10,4) DEFAULT NULL COMMENT '油缸压力3#',
            `cylinder_pressure_four` decimal(10,4) DEFAULT NULL COMMENT '油缸压力4#',
            `cylinder_pressure_five` decimal(10,4) DEFAULT NULL COMMENT '油缸压力5#',
            `cylinder_pressure_six` decimal(10,4) DEFAULT NULL COMMENT '油缸压力6#',
            PRIMARY KEY (`id`) USING BTREE,
            KEY `idx_code` (`code`),
            KEY `idx_ring_num` (`ring_num`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='TBM推进系统参数(此表为原始表，数据按月分表)';
    </update>
</mapper>
