<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.tbm.common.mapper.StatisticsComprehensiveIndexMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fawkes.project.tbm.common.model.StatisticsComprehensiveIndex">
        <result column="id" property="id" />
        <result column="code" property="code" />
        <result column="mileage" property="mileage" />
        <result column="today_mileage" property="todayMileage" />
        <result column="ring_num" property="ringNum" />
        <result column="today_ring_num" property="todayRingNum" />
        <result column="runing_state" property="runingState" />
        <result column="statistics_date" property="statisticsDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        code, mileage, today_mileage, ring_num, today_ring_num, runing_state, statistics_date
    </sql>

    <select id="listMonthPropulsionFootage" resultType="com.fawkes.project.tbm.common.vo.MonthAdvanceVO">
        select
            sum(today_mileage) as monthAdvance,
            DATE_FORMAT(statistics_date , '%Y/%m') as month
        from
            statistics_comprehensive_index
        where
            DATE_FORMAT(statistics_date , '%Y-%m') in
            <foreach collection="monthList" item="month" separator="," open="(" close=")" >
                #{month}
            </foreach>
            <if test="deviceCode != null and deviceCode != ''">
                and code = #{deviceCode}
            </if>
        GROUP by
            DATE_FORMAT(statistics_date , '%Y/%m');
    </select>
</mapper>
