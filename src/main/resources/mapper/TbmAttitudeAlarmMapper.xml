<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.tbm.common.mapper.TbmAttitudeAlarmMapper">
    <!--    查看指定的表是否存在-->
    <select id="existTable" parameterType="String" resultType="Integer">
        select count(*)
        from information_schema.TABLES
        where table_name = #{tableName}
    </select>

    <!-- 删除指定的表-->
    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

    <!-- 创建新的月份表-->
    <update id="createTable" parameterType="String">
        CREATE TABLE ${tableName}
        (
            `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
            `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
            `update_time` timestamp NULL DEFAULT NULL COMMENT '修改时间',
            `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备编码',
            `ring_num` int DEFAULT NULL COMMENT '环号',
            `alarm_location` varchar(100) DEFAULT NULL COMMENT '告警位置',
            `alarm_flag` int DEFAULT NULL COMMENT '告警标志（0：正常；1：异常）',
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='TBM姿态参数-告警表(数据按月分表)';
    </update>
</mapper>
