<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.tbm.common.mapper.TbmSupportSystemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fawkes.project.tbm.common.model.TbmSupportSystem">
        <result column="id" property="id" />
        <result column="code" property="code" />
        <result column="up_brace_pressure1" property="upBracePressure1" />
        <result column="up_brace_pressure2" property="upBracePressure2" />
        <result column="up_brace_pressure3" property="upBracePressure3" />
        <result column="up_brace_pressure4" property="upBracePressure4" />
        <result column="up_brace_pressure5" property="upBracePressure5" />
        <result column="up_brace_pressure6" property="upBracePressure6" />
        <result column="down_brace_pressure1" property="downBracePressure1" />
        <result column="down_brace_pressure2" property="downBracePressure2" />
        <result column="down_brace_pressure3" property="downBracePressure3" />
        <result column="down_brace_pressure4" property="downBracePressure4" />
        <result column="down_brace_pressure5" property="downBracePressure5" />
        <result column="down_brace_pressure6" property="downBracePressure6" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        code, up_brace_pressure1, up_brace_pressure2, up_brace_pressure3, up_brace_pressure4, up_brace_pressure5, up_brace_pressure6, down_brace_pressure1, down_brace_pressure2, down_brace_pressure3, down_brace_pressure4, down_brace_pressure5, down_brace_pressure6, create_time, update_time
    </sql>

    <!--    查看指定的表是否存在-->
    <select id="existTable" parameterType="String" resultType="Integer">
        select count(*)
        from information_schema.TABLES
        where table_name = #{tableName}
    </select>

    <!-- 删除指定的表-->
    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

    <!-- 创建新的月份表-->
    <update id="createTable" parameterType="String">
        CREATE TABLE ${tableName}
        (
            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '支撑系统参数ID',
            `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备编码',
            `up_brace_pressure1` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '1#上层撑靴压力/Double/bar',
            `up_brace_pressure2` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '2#上层撑靴压力/Double/bar',
            `up_brace_pressure3` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '3#上层撑靴压力/Double/bar',
            `up_brace_pressure4` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '4#上层撑靴压力/Double/bar',
            `up_brace_pressure5` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '5#上层撑靴压力/Double/bar',
            `up_brace_pressure6` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '6#上层撑靴压力/Double/bar',
            `down_brace_pressure1` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '1#下层撑靴压力/Double/bar',
            `down_brace_pressure2` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '2#下层撑靴压力/Double/bar',
            `down_brace_pressure3` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '3#下层撑靴压力/Double/bar',
            `down_brace_pressure4` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '4#下层撑靴压力/Double/bar',
            `down_brace_pressure5` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '5#下层撑靴压力/Double/bar',
            `down_brace_pressure6` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '6#下层撑靴压力/Double/bar',
            `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
            `ring_num` int NOT NULL DEFAULT '0' COMMENT '环数',
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='TBM支撑系统参数表(此表为原始表，数据按月分表)';
    </update>

    <select id="getRingNumTbmSupportSystem" resultType="com.fawkes.project.tbm.common.vo.TbmXYVO">
        SELECT
        <if test="position != null and position != ''">
            MAX( DISTINCT ${position} ) AS max,
            AVG( ${position} ) AS average,
        </if>
        <if test="position == null || position == ''">
            MAX( DISTINCT up_brace_pressure1 ) AS max,
            AVG( up_brace_pressure1 ) AS average,
        </if>
        ring_num AS ring
        FROM
        ${tableName}
        WHERE
        `code` = #{deviceCode}
        <if test="ringNumList != null and ringNumList.size() > 0">
            AND ring_num in (<foreach collection="ringNumList" separator="," item="ringNum">
            #{ringNum}
        </foreach>)
        </if>
        GROUP BY
        ring_num
    </select>
</mapper>