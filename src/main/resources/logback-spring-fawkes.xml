<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds">
    <springProperty scope="context" name="elkmode" source="fawkeslog.elkmode"/>
    <springProperty scope="context" name="bs" source="fawkeslog.bootstrap-servers"/>
    <springProperty scope="context" name="topic" source="fawkeslog.topic"/>
    <springProperty scope="context" name="auditmode" source="fawkeslog.auditmode"/>
    <springProperty scope="context" name="host" source="fawkeslog.rabbitmq.host"/>
    <springProperty scope="context" name="port" source="fawkeslog.rabbitmq.port"/>
    <springProperty scope="context" name="username" source="fawkeslog.rabbitmq.username"/>
    <springProperty scope="context" name="password" source="fawkeslog.rabbitmq.password"/>
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>


    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <!-- 生成日志文件 -->
    <appender name="INFO_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件输出的文件名 -->
            <FileNamePattern>target/fawkes/log/info-%d{yyyy-MM-dd}.log</FileNamePattern>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%n%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%X{traceId}] [%logger{50}] %n%-5level: %msg%n</pattern>
        </encoder>
        <!-- 打印日志级别 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 生成日志文件 -->
    <appender name="ERROR_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件输出的文件名 -->
            <FileNamePattern>target/fawkes/log/error-%d{yyyy-MM-dd}.log</FileNamePattern>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%n%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%X{traceId}] [%logger{50}] %n%-5level: %msg%n</pattern>
        </encoder>
        <!-- 打印日志级别 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>


    <!-- 是否开启ELK -->
    <if condition='${elkmode}'>
        <then>
            <!-- 日志经Stream包的kafkaAppender推入kafka -->
            <appender name="kafkaAppender" class="com.fawkes.stream.log.kafka.appender.KafkaAppender">
                <!-- 日志格式化为json-->
                <encoder charset="UTF-8"
                         class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                    <providers>
                        <mdc/>
                        <timestamp>
                            <timeZone>UTC</timeZone>
                        </timestamp>
                        <pattern>
                            <pattern>
                                {
                                "level": "%level",
                                "trace": "%X{X-B3-TraceId:-}",
                                "span": "%X{X-B3-SpanId:-}",
                                "parent": "%X{X-B3-ParentSpanId:-}",
                                "thread": "%thread",
                                "class": "%logger{40}",
                                "message": "%message"
                                }
                            </pattern>
                        </pattern>
                    </providers>
                </encoder>
                <topic>${topic}</topic>
                <keyingStrategy class="com.fawkes.stream.log.kafka.keying.NoKeyKeyingStrategy"/>
                <deliveryStrategy class="com.fawkes.stream.log.kafka.delivery.AsynchronousDeliveryStrategy"/>
                <producerConfig>bootstrap.servers=${bs}</producerConfig>
                <appender-ref ref="STDOUT"/>
            </appender>
        </then>
    </if>

    <!-- 是否开启ELK -->
    <if condition='${auditmode}'>
        <then>
            <!-- 日志经Stream包的rabbitMqAppender推入rabbitMq -->
            <appender name="rabbitMqAppender" class="com.fawkes.stream.log.rabbitmq.appender.RabbitMqAppender">
                <!-- 日志格式化为json-->
                <encoder charset="UTF-8"
                         class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                    <providers>
                        <mdc/>
                        <timestamp>
                            <timeZone>UTC</timeZone>
                        </timestamp>
                        <pattern>
                            <pattern>
                                {
                                "level": "%level",
                                "trace": "%X{X-B3-TraceId:-}",
                                "span": "%X{X-B3-SpanId:-}",
                                "parent": "%X{X-B3-ParentSpanId:-}",
                                "thread": "%thread",
                                "class": "%logger{40}",
                                "message": "%message"
                                }
                            </pattern>
                        </pattern>
                    </providers>
                </encoder>
                <host>${host}</host>
                <port>${port}</port>
                <username>${username}</username>
                <password>${password}</password>
            </appender>
        </then>
    </if>

    <!-- 使用异步的Appender -->
    <if condition='${auditmode}'>
        <then>
            <appender name="ASYNC_APPENDER" class="ch.qos.logback.classic.AsyncAppender">
                <appender-ref ref="rabbitMqAppender"/>
            </appender>
        </then>
    </if>


    <!-- 日志输出级别 -->
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="INFO_APPENDER"/>
        <appender-ref ref="ERROR_APPENDER"/>
        <if condition='${auditmode}'>
            <then>
                <appender-ref ref="ASYNC_APPENDER"/>
            </then>
        </if>
        <if condition='${elkmode}'>
            <then>
                <appender-ref ref="kafkaAppender"/>
            </then>
        </if>
    </root>

    <logger name="net.sf.ehcache" level="INFO"/>
    <logger name="druid.sql" level="INFO"/>

    <!-- MyBatis log configure -->
    <logger name="com.apache.ibatis" level="INFO"/>
    <logger name="org.mybatis.spring" level="DEBUG"/>
    <logger name="java.sql.Connection" level="DEBUG"/>
    <logger name="java.sql.Statement" level="DEBUG"/>
    <logger name="java.sql.PreparedStatement" level="DEBUG"/>

    <!-- 减少部分debug日志 -->
    <logger name="druid.sql" level="INFO"/>
    <logger name="org.apache.shiro" level="INFO"/>
    <logger name="org.mybatis.spring" level="INFO"/>
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.springframework.context" level="WARN"/>
    <logger name="org.springframework.beans" level="WARN"/>
    <logger name="com.baomidou.mybatisplus" level="INFO"/>
    <logger name="org.apache.ibatis.io" level="INFO"/>
    <logger name="org.apache.velocity" level="INFO"/>
    <logger name="org.eclipse.jetty" level="INFO"/>
    <logger name="io.undertow" level="INFO"/>
    <logger name="org.xnio.nio" level="INFO"/>
    <logger name="org.thymeleaf" level="INFO"/>
    <logger name="springfox.documentation" level="INFO"/>
    <logger name="org.hibernate.validator" level="INFO"/>
    <logger name="com.netflix.loadbalancer" level="INFO"/>
    <logger name="com.netflix.hystrix" level="INFO"/>
    <logger name="com.netflix.zuul" level="INFO"/>
    <logger name="de.codecentric" level="INFO"/>
    <!-- cache INFO -->
    <logger name="net.sf.ehcache" level="INFO"/>
    <logger name="org.springframework.cache" level="INFO"/>
    <!-- cloud -->
    <logger name="org.apache.http" level="INFO"/>
    <logger name="com.netflix.discovery" level="INFO"/>
    <logger name="com.netflix.eureka" level="INFO"/>
    <!-- 业务日志 -->
    <Logger name="com.fawkes" level="DEBUG"/>

    <!-- 减少nacos日志 -->
    <logger name="com.alibaba.nacos" level="ERROR"/>


</configuration>
