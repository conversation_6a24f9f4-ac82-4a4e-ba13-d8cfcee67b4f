//package com.fawkes.project.tbm;
//
//import com.fawkes.project.tbm.common.mapper.TbmCutDiskMapper;
//import com.fawkes.project.tbm.common.mapper.TbmDriveSystemMapper;
//import com.fawkes.project.tbm.common.model.*;
//import com.fawkes.project.tbm.service.*;
//import org.junit.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.List;
//
//@SpringBootTest
//public class ColonyBigscreenDataTests {
//    @Resource
//    TbmCutDiskMapper diskMapper;
//    @Resource
//    TbmDriveSystemMapper driveSystemMapper;
//    @Resource
//    private HarmfulGasDeviceService harmfulGasDeviceService;
//
//    @Resource
//    private TbmHarmfulGasService tbmHarmfulGasService;
//
//    @Resource
//    private TbmPropulsionSystemService tbmPropulsionSystemService;
//
//    @Resource
//    private TbmTappingSystemService tbmTappingSystemService;
//
//    @Resource
//    private TbmAttitudeService tbmAttitudeService;
//
//    @Resource
//    private TbmEarthSupportService tbmEarthSupportService;
//    /**
//     * 刀盘系统增加测试数据
//     */
//    @Test
//    public void tbmCutDiskInsertDataList() {
//
//
//        for (int i = 0; i <= 59; i++) {
//            TbmCutDisk cutDisk = new TbmCutDisk();
//            cutDisk.setCode("01");
//            cutDisk.setCutDiskTorchque(BigDecimal.valueOf(2000.2 + (i * 10)));
//            cutDisk.setPenetration(BigDecimal.valueOf(1 + (i * 0.01)));
//            cutDisk.setActualRpm(BigDecimal.valueOf(0.5 + (i * 0.02)));
//            cutDisk.setRingNum((i/4)+1);
//            try {
//                Thread.sleep(1000);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            diskMapper.insert(cutDisk);
//        }
//     }
//
//
//     @Test
//     public void tbmDriveSystemInsertDataList() {
//
//
//         for (int i = 0; i <= 59; i++) {
//             TbmDriveSystem tbmDriveSystem = new TbmDriveSystem();
//             tbmDriveSystem.setCode("04");
//             tbmDriveSystem.setPower(BigDecimal.valueOf(150 + (i * 10)));
//             tbmDriveSystem.setFrequency(BigDecimal.valueOf(40 + (i * 0.01)));
//             tbmDriveSystem.setElectricCurrentOne(BigDecimal.valueOf(25.22 + (i * 5)));
//             tbmDriveSystem.setElectricCurrentTwo(BigDecimal.valueOf(30.32 + (i * 0.6)));
//             tbmDriveSystem.setElectricCurrentThree(BigDecimal.valueOf(20.42 + (i * 0.7)));
//             tbmDriveSystem.setElectricCurrentFour(BigDecimal.valueOf(20.52 + (i * 1.2)));
//             tbmDriveSystem.setElectricCurrentFive(BigDecimal.valueOf(40.62 + (i * 2.2)));
//             tbmDriveSystem.setElectricCurrentSix(BigDecimal.valueOf(30.72 + (i * 0.6)));
//             tbmDriveSystem.setElectricCurrentSeven(BigDecimal.valueOf(15.82 + (i * 2)));
//             tbmDriveSystem.setElectricCurrentEight(BigDecimal.valueOf(18.92 + (i * 0.5)));
//             tbmDriveSystem.setElectricCurrentNine(BigDecimal.valueOf(52.02 + (i * 0.7)));
//             tbmDriveSystem.setElectricCurrentTen(BigDecimal.valueOf(56.12 + (i * 0.6)));
//             tbmDriveSystem.setElectricCurrentEleven(BigDecimal.valueOf(60.22 + (i * 0.3)));
//             tbmDriveSystem.setElectricCurrentTwelve(BigDecimal.valueOf(65.32 + (i * 0.5)));
//             tbmDriveSystem.setElectricCurrentThirteen(BigDecimal.valueOf(53.42 + (i * 1.2)));
//             tbmDriveSystem.setElectricCurrentFourteen(BigDecimal.valueOf(66.52 + (i * 2.2)));
//
//             tbmDriveSystem.setRingNum((i/4)+1);
//             try {
//                 Thread.sleep(1000);
//             } catch (InterruptedException e) {
//                 e.printStackTrace();
//             }
//             driveSystemMapper.insert(tbmDriveSystem);
//         }
//
//
//     }
//
//    /**
//     * 有害气体检测系统增加测试数据
//     */
//    @Test
//    public void tbmHarmfulGasInsertDataList() {
//        List<HarmfulGasDevice> list = harmfulGasDeviceService.lambdaQuery().eq(HarmfulGasDevice::getDeleteFlag, 0).list();
//        list.forEach(gas -> {
//            for (int i = 1; i <= 3; i++) {
//                TbmHarmfulGas insertData = new TbmHarmfulGas();
//                insertData.setCode(gas.getCode());
//                insertData.setCoLevel(BigDecimal.valueOf(10.2 + (i * 10)));
//                insertData.setCo2Level(BigDecimal.valueOf(5.2 + (i * 0.01)));
//                insertData.setO2Level(BigDecimal.valueOf(10.22 + (i * 0.02)));
//                insertData.setCh4Level(BigDecimal.valueOf(6.3 + (i * 0.02)));
//                try {
//                    Thread.sleep(1000);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//                tbmHarmfulGasService.saveOrUpdate(insertData);
//            }
//        });
//
//    }
//
//    /**
//     * 推进系统增加测试数据
//     */
//    @Test
//    public void tbmPropulsionSystemInsertDataList() {
//
//        for (int i = 0; i <= 59; i++) {
//            TbmPropulsionSystem tbmPropulsionSystem = new TbmPropulsionSystem();
//            tbmPropulsionSystem.setCode("04");
//            tbmPropulsionSystem.setThrust(BigDecimal.valueOf(4500.2 + (i * 10)));
//            tbmPropulsionSystem.setPropSpeed(BigDecimal.valueOf(10 + (i * 0.1)));
//            tbmPropulsionSystem.setCylinderPressure(BigDecimal.valueOf(8 + (i * 0.2)));
//            tbmPropulsionSystem.setOilTemp(new BigDecimal(20 + (i)));
//            tbmPropulsionSystem.setRingNum((i / 4) + 1);
//            try {
//                Thread.sleep(1000);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            tbmPropulsionSystemService.saveOrUpdate(tbmPropulsionSystem);
//        }
//
//    }
//
//
//    /**
//     * 出渣系统增加测试数据
//     */
//    @Test
//    public void tbmTappingSystemServiceInsertDataList() {
//
//        for (int i = 0; i <= 59; i++) {
//            TbmTappingSystem tbmPropulsionSystem = new TbmTappingSystem();
//            tbmPropulsionSystem.setCode("04");
//            tbmPropulsionSystem.setSnailRotationalSpeed(BigDecimal.valueOf(5 + (i * 0.2)));
//            tbmPropulsionSystem.setSnailTorchque(new BigDecimal(100 + (i * 2)));
//            tbmPropulsionSystem.setBeltRotationalSpeed(BigDecimal.valueOf(0 + (i * 0.02)));
//            tbmPropulsionSystem.setRingNum((i / 4) + 1);
//
//
//            try {
//                Thread.sleep(1000);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            tbmTappingSystemService.saveOrUpdate(tbmPropulsionSystem);
//        }
//
//    }
//
//    /**
//     * 姿态监测增加测试数据
//     */
//    @Test
//    public void tbmAttitudeInsertDataList() {
//        TbmAttitude tbmAttitude = null;
//        BigDecimal mileage = new BigDecimal(59.5);
//        for (int i = 0; i <= 59; i++) {
//            mileage = mileage.add(new BigDecimal(0.5));
//            tbmAttitude = new TbmAttitude();
//            tbmAttitude.setCode("01");
//            tbmAttitude.setHorizontalFront(new BigDecimal(59 - i));
//            tbmAttitude.setHorizontalEnd(new BigDecimal(59 - i));
//            tbmAttitude.setVerticalFront(new BigDecimal(59 - i));
//            tbmAttitude.setVerticalEnd(new BigDecimal(59 - i));
//            tbmAttitude.setRollingAngle(new BigDecimal(6.9 - (i * 0.1)));
//            tbmAttitude.setPitchAngle(new BigDecimal(6.9 - (i * 0.1)));
//            tbmAttitude.setMileage(mileage);
//            tbmAttitude.setRingNum((i / 4) + 31);
//            tbmAttitude.setRuningState("掘进中");
//            try {
//                Thread.sleep(1000);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            tbmAttitudeService.saveOrUpdate(tbmAttitude);
//        }
//
//    }
//
//    /**
//     * 土仓压力增加测试数据
//     */
//    @Test
//    public void tbmEarthSupportInsertDataList() {
//        TbmEarthSupport tbmEarthSupport = null;
//        for (int i = 0; i <= 59; i++) {
//            tbmEarthSupport = new TbmEarthSupport();
////            tbmEarthSupport.setCode("01");
////            tbmEarthSupport.setBracePressure(new BigDecimal(i * 0.04));
////            tbmEarthSupport.setRingNum((i / 4) + 16);
//            tbmEarthSupport.setCode("03");
//            tbmEarthSupport.setBracePressure(new BigDecimal(i * 2));
//            tbmEarthSupport.setRingNum((i / 4) + 1);
//            try {
//                Thread.sleep(1000);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            tbmEarthSupportService.saveOrUpdate(tbmEarthSupport);
//        }
//
//    }
//
//}
