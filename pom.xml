<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.12.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.fawkes</groupId>
    <artifactId>tbm</artifactId>
    <version>2.0.0.RELEASE</version>
    <packaging>jar</packaging>
    <name>tbm</name>
    <modelVersion>4.0.0</modelVersion>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring.cloud.version>Hoxton.SR12</spring.cloud.version>
        <docker.repository>reg.ecidi.com</docker.repository>
        <docker.registry.name>fawkes</docker.registry.name>
        <fawkes.project.version>2.0.0-RELEASE</fawkes.project.version>
        <mybatis-spring-boot-starter-version>2.0.0</mybatis-spring-boot-starter-version>
        <mybatis-generator-core-version>1.3.2</mybatis-generator-core-version>
        <alibaba.cloud.version>2.2.7.RELEASE</alibaba.cloud.version>
        <alibaba.nacos.client.version>2.0.3</alibaba.nacos.client.version>
        <jasypt-spring-boot-version>3.0.3</jasypt-spring-boot-version>
        <mysql-connector-version>8.0.17</mysql-connector-version>
        <com.github.pagehelper.version>1.2.5</com.github.pagehelper.version>
        <sqlserver-version>7.2.2.jre8</sqlserver-version>
        <poi-version>3.9</poi-version>
        <fastjson-version>1.2.76</fastjson-version>
        <org.projectlombok.version>1.18.22</org.projectlombok.version>
        <mybatis-plus.version>3.4.1</mybatis-plus.version>
        <hutool.version>5.8.18</hutool.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>${jasypt-spring-boot-version}</version>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>${sqlserver-version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-connector-version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>${alibaba.nacos.client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <version>${alibaba.cloud.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <version>${alibaba.cloud.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>${alibaba.cloud.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fawkes</groupId>
            <artifactId>fawkes-core</artifactId>
            <version>${fawkes.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fawkes</groupId>
            <artifactId>fawkes-starter-swagger</artifactId>
            <version>${fawkes.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fawkes</groupId>
            <artifactId>fawkes-core-log</artifactId>
            <version>${fawkes.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fawkes</groupId>
            <artifactId>fawkes-starter-stream-log</artifactId>
            <version>${fawkes.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fawkes</groupId>
            <artifactId>fawkes-starter-ribbon</artifactId>
            <version>${fawkes.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fawkes</groupId>
            <artifactId>fawkes-starter-stream-msg</artifactId>
            <version>${fawkes.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fawkes</groupId>
            <artifactId>fawkes-starter-secure-xss</artifactId>
            <version>${fawkes.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fawkes</groupId>
            <artifactId>fawkes-starter-api-crypto</artifactId>
            <version>${fawkes.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fawkes</groupId>
            <artifactId>fawkes-starter-secure-data</artifactId>
            <version>${fawkes.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fawkes</groupId>
            <artifactId>fawkes-starter-ribbon</artifactId>
            <version>${fawkes.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fawkes</groupId>
            <artifactId>fawkes-starter-sentinel</artifactId>
            <version>${fawkes.project.version}</version>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis-spring-boot-starter-version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <version>${mybatis-generator-core-version}</version>
        </dependency>
        <!--pagehelper-->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>${com.github.pagehelper.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${org.projectlombok.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson-version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.baomidou/mybatis-plus-extension -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-common</artifactId>
            <version>3.1.1277</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-ocr</artifactId>
            <version>3.1.1220</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.6</version>
        </dependency>

        <dependency>
            <groupId>org.locationtech.proj4j</groupId>
            <artifactId>proj4j</artifactId>
            <version>1.3.0</version>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.70</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.fawkes.platform</groupId>
                <artifactId>fawkes-bom</artifactId>
                <version>${fawkes.project.version}</version>
                <type>pom</type>
<!--                <scope>import</scope>-->
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
<!--                <scope>import</scope>-->
            </dependency>
        </dependencies>
    </dependencyManagement>
    <profiles>
        <profile>
            <!-- 本地开发环境 -->
            <id>dev</id>
            <properties>
                <profiles.active>dev</profiles.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <!-- 生产环境 -->
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
            </properties>
        </profile>
        <profile>
            <!-- 生产环境 -->
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
            </properties>
        </profile>
    </profiles>

    <build>
        <resources><!-- 使用@@站位符，输出Dockerfile至docker文件夹 -->
            <resource>
                <directory>src/main/docker</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/Dockerfile</include>
                </includes>
                <targetPath>../docker</targetPath>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>bootstrap.yml</include>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <configuration>
                    <configurationFile>src/main/resources/generatorConfig.xml</configurationFile>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.fawkes</groupId>
                        <artifactId>fawkes-core</artifactId>
                        <version>${fawkes.project.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>${mysql-connector-version}</version>
                    </dependency>
                </dependencies>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>com.spotify</groupId>-->
<!--                <artifactId>docker-maven-plugin</artifactId>-->
<!--                <version>1.1.0</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>build-image</id>-->
<!--                        <phase>package</phase>-->
<!--                        <goals>-->
<!--                            <goal>build</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                    <execution>-->
<!--                        <id>push-image</id>-->
<!--                        <phase>package</phase>-->
<!--                        <goals>-->
<!--                            <goal>push</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <imageName>-->
<!--                                ${docker.repository}/${docker.registry.name}/${project.artifactId}:${project.version}-->
<!--                            </imageName>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <serverId>harbor</serverId>-->
<!--                    <dockerDirectory>${project.build.directory}/docker</dockerDirectory>-->
<!--                    <imageName>-->
<!--                        ${docker.repository}/${docker.registry.name}/${project.artifactId}-->
<!--                    </imageName>-->
<!--                    <imageTags>-->
<!--                        &lt;!&ndash;docker的tag为项目版本号、latest&ndash;&gt;-->
<!--                        <imageTag>${project.version}</imageTag>-->
<!--                    </imageTags>-->
<!--                    <resources>-->
<!--                        <rescource>&lt;!&ndash; 将打包文件放入dockerDirectory指定的位置 &ndash;&gt;-->
<!--                            <targetPath>/</targetPath>-->
<!--                            <directory>${project.build.directory}</directory>-->
<!--                            <include>${project.build.finalName}.jar</include>-->
<!--                        </rescource>-->
<!--                    </resources>-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>
    </build>

</project>
